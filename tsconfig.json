{"compilerOptions": {"target": "es5", "lib": ["dom", "dom.iterable", "esnext"], "typeRoots": ["./node_modules/@types", "./src/types"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "baseUrl": ".", "paths": {"@/*": ["./src/*"]}}, "include": ["src"]}