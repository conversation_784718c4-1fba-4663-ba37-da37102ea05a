{"name": "eva-planning", "version": "0.1.0", "private": true, "dependencies": {"@tanstack/react-query": "^5.82.0", "@tanstack/react-query-devtools": "^5.82.0", "@testing-library/user-event": "^14.6.1", "@types/node": "^24.0.12", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "ajv": "^8.17.1", "axios": "^1.10.0", "bootstrap": "^5.3.7", "date-fns": "^4.1.0", "ics": "^3.8.1", "lucide-react": "^0.525.0", "moment": "^2.30.1", "react": "^19.1.0", "react-big-calendar": "^1.19.4", "react-bootstrap": "^2.10.10", "react-bootstrap-icons": "^1.11.6", "react-dom": "^19.1.0", "react-router-bootstrap": "^0.26.3", "react-router-dom": "^7.6.3", "react-scripts": "5.0.1", "react-select": "^5.10.1", "typescript": "^5.8.3", "web-vitals": "^5.0.3"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/react-hooks": "^8.0.1", "@types/jest": "^30.0.0", "@types/react-big-calendar": "^1.16.2", "@types/react-router-bootstrap": "^0.26.8", "vitest": "^3.2.4"}}