{"name": "eva-planning", "version": "0.1.0", "private": true, "dependencies": {"@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@tailwindcss/postcss": "^4.1.11", "@tanstack/react-query": "^5.82.0", "@tanstack/react-query-devtools": "^5.82.0", "@testing-library/user-event": "^14.6.1", "@types/node": "^24.0.12", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "ajv": "^8.17.1", "axios": "^1.10.0", "bootstrap": "^5.3.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "ics": "^3.8.1", "lucide-react": "^0.525.0", "moment": "^2.30.1", "react": "^19.1.0", "react-big-calendar": "^1.19.4", "react-bootstrap": "^2.10.10", "react-bootstrap-icons": "^1.11.6", "react-dom": "^19.1.0", "react-router-bootstrap": "^0.26.3", "react-router-dom": "^6.30.1", "react-scripts": "5.0.1", "react-select": "^5.10.1", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "typescript": "^5.8.3", "web-vitals": "^5.0.3"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/react-hooks": "^8.0.1", "@types/jest": "^30.0.0", "@types/react-big-calendar": "^1.16.2", "@types/react-router-bootstrap": "^0.26.8", "autoprefixer": "^10.4.21", "postcss": "^8.5.6", "tailwindcss": "^4.1.11", "vitest": "^3.2.4"}}