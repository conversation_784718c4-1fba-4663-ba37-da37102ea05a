{"name": "eva-planning", "version": "0.1.0", "private": true, "dependencies": {"@tanstack/react-query": "^5.82.0", "@tanstack/react-query-devtools": "^5.82.0", "@testing-library/user-event": "^13.5.0", "@types/node": "^16.18.112", "@types/react": "^18.3.11", "@types/react-dom": "^18.3.0", "ajv": "^8.17.1", "axios": "^1.7.7", "bootstrap": "^5.3.3", "date-fns": "^4.1.0", "ics": "^3.8.1", "lucide-react": "^0.447.0", "moment": "^2.30.1", "react": "^18.3.1", "react-big-calendar": "^1.15.0", "react-bootstrap": "^2.10.5", "react-bootstrap-icons": "^1.11.4", "react-dom": "^18.3.1", "react-router-bootstrap": "^0.26.3", "react-router-dom": "^6.26.2", "react-scripts": "5.0.1", "react-select": "^5.8.1", "typescript": "^4.9.5", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^15.0.7", "@testing-library/react-hooks": "^8.0.1", "@types/jest": "^30.0.0", "@types/react-big-calendar": "^1.8.12", "@types/react-router-bootstrap": "^0.26.6", "vitest": "^3.2.4"}}