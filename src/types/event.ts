export enum EventStatus {
    DRAFT = 'draft',
    PENDING = 'pending',
    CONFIRMED = 'confirmed',
    REJECTED = 'rejected',
    CANCELLED = 'cancelled'
}

export enum EventPriority {
    LOW = 'low',
    MEDIUM = 'medium',
    HIGH = 'high',
    CRITICAL = 'critical'
}

export interface EventConflict {
    id: string;
    type: 'time_overlap' | 'resource_conflict' | 'venue_conflict';
    conflictingEventId: string;
    description: string;
    severity: 'low' | 'medium' | 'high';
}

export interface EventApproval {
    id: string;
    eventId: string;
    reviewerId: string;
    reviewerName: string;
    status: 'approved' | 'rejected' | 'pending';
    comments?: string;
    reviewedAt?: string;
}

export interface EvaEvent {
    id: number | string;
    eventName: string;
    description?: string;
    date: string;
    time: string;
    venue: string;
    organizer: string;
    organizerId: string;
    departmentId: string;
    departmentName: string;
    departments: string[]; // Additional departments involved
    duration: number; // duration in minutes
    status: EventStatus;
    priority: EventPriority;
    maxAttendees?: number;
    estimatedAttendees?: number;
    budget?: number;
    resources?: string[]; // Required resources
    conflicts?: EventConflict[];
    approvals?: EventApproval[];
    submissionDeadline?: string;
    isRecurring?: boolean;
    recurringPattern?: string;
    tags?: string[];
    createdAt: string;
    updatedAt: string;
    submittedAt?: string;
    approvedAt?: string;
    rejectedAt?: string;
    // For react-big-calendar compatibility
    start?: Date;
    end?: Date;
    title?: string;
}

export interface SubmissionDeadline {
    id: string;
    year: number;
    deadline: string;
    description?: string;
    isActive: boolean;
    createdBy: string;
    createdAt: string;
}

export interface EventTemplate {
    id: string;
    name: string;
    description?: string;
    defaultDuration: number;
    defaultVenue?: string;
    requiredResources: string[];
    departmentId?: string;
    isPublic: boolean;
    createdBy: string;
    createdAt: string;
}

export interface CalendarExportOptions {
    format: 'ical' | 'google' | 'outlook' | 'csv' | 'json';
    dateRange: {
        start: string;
        end: string;
    };
    includeStatuses: EventStatus[];
    includeDepartments?: string[];
    includePrivateEvents?: boolean;
}

export interface EventStats {
    totalEvents: number;
    eventsByStatus: Record<EventStatus, number>;
    eventsByDepartment: Record<string, number>;
    conflictsCount: number;
    upcomingDeadlines: SubmissionDeadline[];
}
