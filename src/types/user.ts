export enum UserRole {
    ADMIN = 'admin',
    TEAM_LEADER = 'team_leader',
    EMPLOYEE = 'employee'
}

export interface Department {
    id: string;
    name: string;
    description?: string;
    teamLeaderId?: string;
    createdAt: string;
    updatedAt: string;
}

export interface User {
    id: number | string;
    firstName: string;
    lastName: string;
    email: string;
    organization?: string;
    role: UserRole;
    departmentId?: string;
    department?: Department;
    isActive: boolean;
    createdAt?: string;
    updatedAt?: string;
}

export interface SignupFormData {
    firstName: string;
    lastName: string;
    email: string;
    password: string;
    confirmPassword: string;
    organization?: string;
    role: UserRole;
    departmentId?: string;
    agreeToTerms: boolean;
}

export interface LoginFormData {
    email: string;
    password: string;
    rememberMe?: boolean;
}

export interface AuthResponse {
    user: User;
    token: string;
    refreshToken?: string;
}
