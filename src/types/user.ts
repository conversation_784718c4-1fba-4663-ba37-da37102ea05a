export interface User {
    id: number | string;
    firstName: string;
    lastName: string;
    email: string;
    organization?: string;
    role?: string;
    createdAt?: string;
    updatedAt?: string;
}

export interface SignupFormData {
    firstName: string;
    lastName: string;
    email: string;
    password: string;
    confirmPassword: string;
    organization?: string;
    role?: string;
    agreeToTerms: boolean;
}

export interface LoginFormData {
    email: string;
    password: string;
    rememberMe?: boolean;
}

export interface AuthResponse {
    user: User;
    token: string;
    refreshToken?: string;
}
