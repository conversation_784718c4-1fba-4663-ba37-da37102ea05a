import { useState, useEffect } from 'react';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import axios, { AxiosError } from 'axios';
import { User, SignupFormData, LoginFormData, AuthResponse } from '../types/user';

/**
 * Base URL for the authentication API endpoint
 */
const AUTH_API_URL = 'http://localhost:3000/auth';

/**
 * Custom hook for managing authentication state and operations
 */
export const useAuth = () => {
    const queryClient = useQueryClient();
    const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
    const [user, setUser] = useState<User | null>(null);

    // Check if user is authenticated on mount
    useEffect(() => {
        const token = localStorage.getItem('authToken');
        const userData = localStorage.getItem('userData');
        
        if (token && userData) {
            try {
                const parsedUser = JSON.parse(userData);
                setUser(parsedUser);
                setIsAuthenticated(true);
            } catch (error) {
                // Clear invalid data
                localStorage.removeItem('authToken');
                localStorage.removeItem('userData');
            }
        }
    }, []);

    /**
     * Sign up mutation
     */
    const signupMutation = useMutation<AuthResponse, AxiosError, Omit<SignupFormData, 'confirmPassword' | 'agreeToTerms'>>({
        mutationFn: async (signupData) => {
            const response = await axios.post<AuthResponse>(`${AUTH_API_URL}/signup`, signupData);
            return response.data;
        },
        onSuccess: (data) => {
            // Store auth data
            localStorage.setItem('authToken', data.token);
            localStorage.setItem('userData', JSON.stringify(data.user));
            
            // Update state
            setUser(data.user);
            setIsAuthenticated(true);
            
            // Invalidate any cached queries that depend on auth state
            queryClient.invalidateQueries({ queryKey: ['user'] });
        },
        onError: (error) => {
            console.error('Signup error:', error.message);
        }
    });

    /**
     * Login mutation
     */
    const loginMutation = useMutation<AuthResponse, AxiosError, LoginFormData>({
        mutationFn: async (loginData) => {
            const response = await axios.post<AuthResponse>(`${AUTH_API_URL}/login`, loginData);
            return response.data;
        },
        onSuccess: (data) => {
            // Store auth data
            localStorage.setItem('authToken', data.token);
            localStorage.setItem('userData', JSON.stringify(data.user));
            
            // Update state
            setUser(data.user);
            setIsAuthenticated(true);
            
            // Invalidate any cached queries that depend on auth state
            queryClient.invalidateQueries({ queryKey: ['user'] });
        },
        onError: (error) => {
            console.error('Login error:', error.message);
        }
    });

    /**
     * Logout function
     */
    const logout = () => {
        // Clear stored data
        localStorage.removeItem('authToken');
        localStorage.removeItem('userData');
        
        // Update state
        setUser(null);
        setIsAuthenticated(false);
        
        // Clear all cached queries
        queryClient.clear();
    };

    /**
     * Get current user profile
     */
    const { data: currentUser, isLoading: isLoadingUser } = useQuery<User, AxiosError>({
        queryKey: ['user', 'profile'],
        queryFn: async () => {
            const token = localStorage.getItem('authToken');
            if (!token) throw new Error('No auth token');
            
            const response = await axios.get<User>(`${AUTH_API_URL}/profile`, {
                headers: { Authorization: `Bearer ${token}` }
            });
            return response.data;
        },
        enabled: isAuthenticated,
        staleTime: 5 * 60 * 1000, // 5 minutes
    });

    return {
        // State
        isAuthenticated,
        user: currentUser || user,
        isLoadingUser,
        
        // Mutations
        signup: signupMutation,
        login: loginMutation,
        logout,
        
        // Loading states
        isSigningUp: signupMutation.isPending,
        isLoggingIn: loginMutation.isPending,
        
        // Error states
        signupError: signupMutation.error,
        loginError: loginMutation.error,
    };
};
