import { renderHook, act } from '@testing-library/react-hooks';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { useEvents } from '../useEvents';
import axios from 'axios';
import { vi } from 'vitest';

// Mock axios
vi.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

// Create a test query client
const createTestQueryClient = () => {
  return new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  });
};

// Create a wrapper component
const createWrapper = () => {
  const testQueryClient = createTestQueryClient();
  
  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={testQueryClient}>
      {children}
    </QueryClientProvider>
  );
};

describe('useEvents', () => {
  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks();
  });

  it('should fetch events successfully', async () => {
    const mockEvents = [
      { id: 1, eventName: 'Test Event', date: '2023-01-01', time: '10:00', venue: 'Test Venue', organizer: 'Test Organizer', departments: ['Test'], duration: 60 }
    ];
    
    mockedAxios.get.mockResolvedValueOnce({ data: mockEvents });
    
    const { result, waitFor } = renderHook(() => useEvents(), {
      wrapper: createWrapper(),
    });
    
    // Initial state should be loading
    expect(result.current.isLoading).toBe(true);
    
    // Wait for the query to complete
    await waitFor(() => result.current.isLoading === false);
    
    // Should have the mock data
    expect(result.current.events).toEqual(mockEvents);
    expect(mockedAxios.get).toHaveBeenCalledWith('http://localhost:3000/events');
  });

  it('should handle fetch error', async () => {
    const errorMessage = 'Network Error';
    mockedAxios.get.mockRejectedValueOnce(new Error(errorMessage));
    
    const { result, waitFor } = renderHook(() => useEvents(), {
      wrapper: createWrapper(),
    });
    
    await waitFor(() => result.current.isLoading === false);
    
    expect(result.current.error).not.toBeNull();
    expect(result.current.error?.message).toBe(errorMessage);
  });

  it('should create an event with optimistic update', async () => {
    const newEvent = {
      eventName: 'New Event',
      date: '2023-01-02',
      time: '11:00',
      venue: 'New Venue',
      organizer: 'New Organizer',
      departments: ['Test'],
      duration: 90
    };
    
    const createdEvent = { ...newEvent, id: 1 };
    
    mockedAxios.post.mockResolvedValueOnce({ data: createdEvent });
    
    const { result, waitFor } = renderHook(() => useEvents(), {
      wrapper: createWrapper(),
    });
    
    // Mock the initial empty state
    mockedAxios.get.mockResolvedValueOnce({ data: [] });
    
    await act(async () => {
      await result.current.createEvent.mutateAsync(newEvent);
    });
    
    // Should have called the API with the new event
    expect(mockedAxios.post).toHaveBeenCalledWith('http://localhost:3000/events', newEvent);
    
    // The optimistic update should have been rolled back and replaced with the server response
    await waitFor(() => {
      expect(result.current.events).toContainEqual(createdEvent);
    });
  });

  it('should update an event with optimistic update', async () => {
    const updatedEvent = {
      id: 1,
      eventName: 'Updated Event',
      date: '2023-01-01',
      time: '10:00',
      venue: 'Updated Venue',
      organizer: 'Updated Organizer',
      departments: ['Test'],
      duration: 120
    };
    
    mockedAxios.put.mockResolvedValueOnce({ data: updatedEvent });
    
    const { result } = renderHook(() => useEvents(), {
      wrapper: createWrapper(),
    });
    
    await act(async () => {
      await result.current.updateEvent.mutateAsync(updatedEvent);
    });
    
    expect(mockedAxios.put).toHaveBeenCalledWith(
      'http://localhost:3000/events/1',
      updatedEvent
    );
  });

  it('should delete an event with optimistic update', async () => {
    const eventId = 1;
    
    mockedAxios.delete.mockResolvedValueOnce({});
    
    const { result } = renderHook(() => useEvents(), {
      wrapper: createWrapper(),
    });
    
    await act(async () => {
      await result.current.deleteEvent.mutateAsync(eventId);
    });
    
    expect(mockedAxios.delete).toHaveBeenCalledWith(
      'http://localhost:3000/events/1'
    );
  });
});
