import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import axios, { AxiosError } from 'axios';
import { EvaEvent } from '../types/event';

/**
 * Base URL for the events API endpoint
 */
const API_URL = 'http://localhost:3000/events';

/**
 * Generates a temporary ID for optimistic updates
 * @returns {string} A unique temporary ID
 */
const generateTempId = (): string => `temp-${Date.now()}`;

/**
 * Context type for mutation callbacks
 */
type MutationContext = {
  /** Previous events before the mutation */
  previousEvents?: EvaEvent[];
  /** The optimistically created event (for create operations) */
  optimisticEvent?: EvaEvent;
};

/**
 * Custom hook for managing event-related data fetching and mutations
 * @returns {Object} An object containing events data and mutation functions
 * @property {EvaEvent[]} events - Array of events
 * @property {boolean} isLoading - Loading state for the initial query
 * @property {AxiosError | null} error - Error object if any error occurred
 * @property {Function} refetch - Function to manually refetch events
 * @property {Object} createEvent - Mutation object for creating events
 * @property {Object} updateEvent - Mutation object for updating events
 * @property {Object} deleteEvent - Mutation object for deleting events
 */
export const useEvents = () => {
  const queryClient = useQueryClient();

  /**
   * Fetches all events from the server
   * Uses React Query's useQuery for data fetching, caching, and state management
   */
  const {
    data: events = [],
    isLoading,
    error,
    refetch
  } = useQuery<EvaEvent[], AxiosError, EvaEvent[], ['events']>({
    queryKey: ['events'],
    queryFn: async () => {
      const response = await axios.get<EvaEvent[]>(API_URL);
      return response.data;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false
  });

  /**
   * Creates a new event with optimistic updates
   * Handles the API call and automatically updates the cache
   */
  const createEvent = useMutation<EvaEvent, AxiosError, Omit<EvaEvent, 'id'>, MutationContext>({
    mutationFn: async (newEvent) => {
      const response = await axios.post<EvaEvent>(API_URL, newEvent);
      return response.data;
    },
    onMutate: async (newEvent) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: ['events'] });

      // Snapshot the previous value
      const previousEvents = queryClient.getQueryData<EvaEvent[]>(['events']) || [];

      // Generate a temporary ID for the optimistic update
      const tempId = generateTempId();
      const optimisticEvent = { ...newEvent, id: tempId } as EvaEvent;

      // Optimistically update to the new value
      queryClient.setQueryData<EvaEvent[]>(['events'], (old = []) => [
        ...old,
        optimisticEvent,
      ]);

      // Return a context with the optimistic event and previous events
      return { previousEvents, optimisticEvent };
    },
    onError: (err, variables, context) => {
      // Rollback on error
      if (context?.previousEvents) {
        queryClient.setQueryData(['events'], context.previousEvents);
      }
      console.error('Error creating event:', err.message);
    },
    onSettled: () => {
      // Always refetch after error or success
      queryClient.invalidateQueries({ queryKey: ['events'] });
    },
  });

  /**
   * Updates an existing event with optimistic updates
   * Handles the API call and automatically updates the cache
   */
  const updateEvent = useMutation<EvaEvent, AxiosError, EvaEvent, MutationContext>({
    mutationFn: async (updatedEvent) => {
      const response = await axios.put<EvaEvent>(`${API_URL}/${updatedEvent.id}`, updatedEvent);
      return response.data;
    },
    onMutate: async (updatedEvent) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: ['events'] });

      // Snapshot the previous value
      const previousEvents = queryClient.getQueryData<EvaEvent[]>(['events']) || [];

      // Optimistically update to the new value
      queryClient.setQueryData<EvaEvent[]>(['events'], (old = []) =>
        old.map((event) =>
          event.id === updatedEvent.id ? { ...event, ...updatedEvent } : event
        )
      );

      // Return a context with the previous events
      return { previousEvents };
    },
    onError: (err, variables, context) => {
      // Rollback on error
      if (context?.previousEvents) {
        queryClient.setQueryData(['events'], context.previousEvents);
      }
      console.error('Error updating event:', err.message);
    },
    onSettled: () => {
      // Always refetch after error or success
      queryClient.invalidateQueries({ queryKey: ['events'] });
    },
  });

  /**
   * Deletes an event with optimistic updates
   * Handles the API call and automatically updates the cache
   */
  const deleteEvent = useMutation<number | string, AxiosError, number | string, MutationContext>({
    mutationFn: async (eventId) => {
      await axios.delete(`${API_URL}/${eventId}`);
      return eventId;
    },
    onMutate: async (eventId) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: ['events'] });

      // Snapshot the previous value
      const previousEvents = queryClient.getQueryData<EvaEvent[]>(['events']) || [];

      // Optimistically remove the event
      queryClient.setQueryData<EvaEvent[]>(['events'], (old = []) =>
        old.filter((event) => event.id !== eventId)
      );

      // Return a context with the previous events
      return { previousEvents };
    },
    onError: (err, eventId, context) => {
      // Rollback on error
      if (context?.previousEvents) {
        queryClient.setQueryData(['events'], context.previousEvents);
      }
      console.error('Error deleting event:', err.message);
    },
    onSettled: () => {
      // Always refetch after error or success
      queryClient.invalidateQueries({ queryKey: ['events'] });
    },
  });

  return {
    events,
    isLoading,
    error,
    refetch,
    createEvent,
    updateEvent,
    deleteEvent,
  };
};

export default useEvents;
