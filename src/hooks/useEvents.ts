import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import axios, { AxiosError } from 'axios';
import { EvaEvent, EventStatus, EventConflict } from '../types/event';
import { sampleEvents } from '../data/sampleEvents';

/**
 * Base URL for the events API endpoint
 */
const API_URL = 'http://localhost:3000/events';

/**
 * Generates a temporary ID for optimistic updates
 * @returns {string} A unique temporary ID
 */
const generateTempId = (): string => `temp-${Date.now()}`;

/**
 * Context type for mutation callbacks
 */
type MutationContext = {
  /** Previous events before the mutation */
  previousEvents?: EvaEvent[];
  /** The optimistically created event (for create operations) */
  optimisticEvent?: EvaEvent;
};

/**
 * Custom hook for managing event-related data fetching and mutations
 * @returns {Object} An object containing events data and mutation functions
 * @property {EvaEvent[]} events - Array of events
 * @property {boolean} isLoading - Loading state for the initial query
 * @property {AxiosError | null} error - Error object if any error occurred
 * @property {Function} refetch - Function to manually refetch events
 * @property {Object} createEvent - Mutation object for creating events
 * @property {Object} updateEvent - Mutation object for updating events
 * @property {Object} deleteEvent - Mutation object for deleting events
 */
export const useEvents = () => {
  const queryClient = useQueryClient();

  /**
   * Fetches all events from the server
   * Uses React Query's useQuery for data fetching, caching, and state management
   */
  const {
    data: events = sampleEvents,
    isLoading,
    error,
    refetch
  } = useQuery<EvaEvent[], AxiosError, EvaEvent[], ['events']>({
    queryKey: ['events'],
    queryFn: async () => {
      try {
        const response = await axios.get<EvaEvent[]>(API_URL);
        return response.data;
      } catch (error) {
        // Return sample events if API fails
        console.log('Using sample events for demo');
        return sampleEvents;
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false
  });

  /**
   * Creates a new event with optimistic updates
   * Handles the API call and automatically updates the cache
   */
  const createEvent = useMutation<EvaEvent, AxiosError, Omit<EvaEvent, 'id'>, MutationContext>({
    mutationFn: async (newEvent) => {
      const response = await axios.post<EvaEvent>(API_URL, newEvent);
      return response.data;
    },
    onMutate: async (newEvent) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: ['events'] });

      // Snapshot the previous value
      const previousEvents = queryClient.getQueryData<EvaEvent[]>(['events']) || [];

      // Generate a temporary ID for the optimistic update
      const tempId = generateTempId();
      const optimisticEvent = { ...newEvent, id: tempId } as EvaEvent;

      // Optimistically update to the new value
      queryClient.setQueryData<EvaEvent[]>(['events'], (old = []) => [
        ...old,
        optimisticEvent,
      ]);

      // Return a context with the optimistic event and previous events
      return { previousEvents, optimisticEvent };
    },
    onError: (err, variables, context) => {
      // Rollback on error
      if (context?.previousEvents) {
        queryClient.setQueryData(['events'], context.previousEvents);
      }
      console.error('Error creating event:', err.message);
    },
    onSettled: () => {
      // Always refetch after error or success
      queryClient.invalidateQueries({ queryKey: ['events'] });
    },
  });

  /**
   * Updates an existing event with optimistic updates
   * Handles the API call and automatically updates the cache
   */
  const updateEvent = useMutation<EvaEvent, AxiosError, EvaEvent, MutationContext>({
    mutationFn: async (updatedEvent) => {
      const response = await axios.put<EvaEvent>(`${API_URL}/${updatedEvent.id}`, updatedEvent);
      return response.data;
    },
    onMutate: async (updatedEvent) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: ['events'] });

      // Snapshot the previous value
      const previousEvents = queryClient.getQueryData<EvaEvent[]>(['events']) || [];

      // Optimistically update to the new value
      queryClient.setQueryData<EvaEvent[]>(['events'], (old = []) =>
        old.map((event) =>
          event.id === updatedEvent.id ? { ...event, ...updatedEvent } : event
        )
      );

      // Return a context with the previous events
      return { previousEvents };
    },
    onError: (err, variables, context) => {
      // Rollback on error
      if (context?.previousEvents) {
        queryClient.setQueryData(['events'], context.previousEvents);
      }
      console.error('Error updating event:', err.message);
    },
    onSettled: () => {
      // Always refetch after error or success
      queryClient.invalidateQueries({ queryKey: ['events'] });
    },
  });

  /**
   * Deletes an event with optimistic updates
   * Handles the API call and automatically updates the cache
   */
  const deleteEvent = useMutation<number | string, AxiosError, number | string, MutationContext>({
    mutationFn: async (eventId) => {
      await axios.delete(`${API_URL}/${eventId}`);
      return eventId;
    },
    onMutate: async (eventId) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: ['events'] });

      // Snapshot the previous value
      const previousEvents = queryClient.getQueryData<EvaEvent[]>(['events']) || [];

      // Optimistically remove the event
      queryClient.setQueryData<EvaEvent[]>(['events'], (old = []) =>
        old.filter((event) => event.id !== eventId)
      );

      // Return a context with the previous events
      return { previousEvents };
    },
    onError: (err, eventId, context) => {
      // Rollback on error
      if (context?.previousEvents) {
        queryClient.setQueryData(['events'], context.previousEvents);
      }
      console.error('Error deleting event:', err.message);
    },
    onSettled: () => {
      // Always refetch after error or success
      queryClient.invalidateQueries({ queryKey: ['events'] });
    },
  });

  /**
   * Submit event for approval (change status from DRAFT to PENDING)
   */
  const submitEvent = useMutation<EvaEvent, AxiosError, string>({
    mutationFn: async (eventId) => {
      const response = await axios.patch<EvaEvent>(`${API_URL}/${eventId}/submit`);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['events'] });
    },
  });

  /**
   * Approve event (change status from PENDING to CONFIRMED)
   */
  const approveEvent = useMutation<EvaEvent, AxiosError, { eventId: string; comments?: string; }>({
    mutationFn: async ({ eventId, comments }) => {
      const response = await axios.patch<EvaEvent>(`${API_URL}/${eventId}/approve`, { comments });
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['events'] });
    },
  });

  /**
   * Reject event (change status from PENDING to REJECTED)
   */
  const rejectEvent = useMutation<EvaEvent, AxiosError, { eventId: string; comments: string; }>({
    mutationFn: async ({ eventId, comments }) => {
      const response = await axios.patch<EvaEvent>(`${API_URL}/${eventId}/reject`, { comments });
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['events'] });
    },
  });

  /**
   * Check for event conflicts
   */
  const checkConflicts = useMutation<EventConflict[], AxiosError, Omit<EvaEvent, 'id'>>({
    mutationFn: async (eventData) => {
      const response = await axios.post<EventConflict[]>(`${API_URL}/check-conflicts`, eventData);
      return response.data;
    },
  });

  /**
   * Get events filtered by status
   */
  const getEventsByStatus = (status: EventStatus) => {
    return events.filter(event => event.status === status);
  };

  /**
   * Get events by department
   */
  const getEventsByDepartment = (departmentId: string) => {
    return events.filter(event => event.departmentId === departmentId);
  };

  /**
   * Get pending events that need approval
   */
  const getPendingEvents = () => {
    return getEventsByStatus(EventStatus.PENDING);
  };

  /**
   * Get events with conflicts
   */
  const getConflictedEvents = () => {
    return events.filter(event => event.conflicts && event.conflicts.length > 0);
  };

  return {
    events,
    isLoading,
    error,
    refetch,
    createEvent,
    updateEvent,
    deleteEvent,
    submitEvent,
    approveEvent,
    rejectEvent,
    checkConflicts,
    getEventsByStatus,
    getEventsByDepartment,
    getPendingEvents,
    getConflictedEvents,
  };
};

export default useEvents;
