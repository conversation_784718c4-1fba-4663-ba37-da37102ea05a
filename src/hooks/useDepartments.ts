import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import axios, { AxiosError } from 'axios';
import { Department } from '../types/user';

/**
 * Base URL for the departments API endpoint
 */
const API_URL = 'http://localhost:3000/departments';

/**
 * Custom hook for managing departments
 */
export const useDepartments = () => {
  const queryClient = useQueryClient();

  // Default departments for demo purposes
  const defaultDepartments: Department[] = [
    {
      id: 'hr',
      name: 'Human Resources',
      description: 'Employee relations, recruitment, and HR policies',
      teamLeaderId: 'hr-lead-1',
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z'
    },
    {
      id: 'it',
      name: 'Information Technology',
      description: 'IT infrastructure, software development, and technical support',
      teamLeaderId: 'it-lead-1',
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z'
    },
    {
      id: 'marketing',
      name: 'Marketing',
      description: 'Brand management, advertising, and market research',
      teamLeaderId: 'marketing-lead-1',
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z'
    },
    {
      id: 'sales',
      name: 'Sales',
      description: 'Customer acquisition, account management, and revenue generation',
      teamLeaderId: 'sales-lead-1',
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z'
    },
    {
      id: 'finance',
      name: 'Finance',
      description: 'Financial planning, accounting, and budget management',
      teamLeaderId: 'finance-lead-1',
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z'
    },
    {
      id: 'operations',
      name: 'Operations',
      description: 'Business operations, logistics, and process management',
      teamLeaderId: 'ops-lead-1',
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z'
    },
    {
      id: 'admin',
      name: 'Administration',
      description: 'Administrative functions and executive management',
      teamLeaderId: 'admin-lead-1',
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z'
    }
  ];

  /**
   * Fetches all departments
   */
  const {
    data: departments = defaultDepartments,
    isLoading,
    error,
    refetch
  } = useQuery<Department[], AxiosError>({
    queryKey: ['departments'],
    queryFn: async () => {
      try {
        const response = await axios.get<Department[]>(API_URL);
        return response.data;
      } catch (error) {
        // Return default departments if API fails
        console.log('Using default departments for demo');
        return defaultDepartments;
      }
    },
    staleTime: 10 * 60 * 1000, // 10 minutes
    retry: false, // Don't retry on failure, use defaults
  });

  /**
   * Creates a new department
   */
  const createDepartment = useMutation<Department, AxiosError, Omit<Department, 'id' | 'createdAt' | 'updatedAt'>>({
    mutationFn: async (newDepartment) => {
      const response = await axios.post<Department>(API_URL, newDepartment);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['departments'] });
    },
  });

  /**
   * Updates an existing department
   */
  const updateDepartment = useMutation<Department, AxiosError, { id: string; data: Partial<Department>; }>({
    mutationFn: async ({ id, data }) => {
      const response = await axios.put<Department>(`${API_URL}/${id}`, data);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['departments'] });
    },
  });

  /**
   * Deletes a department
   */
  const deleteDepartment = useMutation<void, AxiosError, string>({
    mutationFn: async (id) => {
      await axios.delete(`${API_URL}/${id}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['departments'] });
    },
  });

  /**
   * Gets department by ID
   */
  const getDepartmentById = (id: string) => {
    return departments.find(dept => dept.id === id);
  };

  /**
   * Gets departments where user is team leader
   */
  const getDepartmentsByTeamLeader = (teamLeaderId: string) => {
    return departments.filter(dept => dept.teamLeaderId === teamLeaderId);
  };

  return {
    departments,
    isLoading,
    error,
    refetch,
    createDepartment,
    updateDepartment,
    deleteDepartment,
    getDepartmentById,
    getDepartmentsByTeamLeader,
  };
};
