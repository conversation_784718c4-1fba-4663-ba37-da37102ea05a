/* Eva Planning Global Styles */

/* CSS Custom Properties */
:root {
  --eva-primary: #3174ad;
  --eva-primary-light: #4a8bc2;
  --eva-primary-dark: #1e4a73;
  --eva-secondary: #ff6b35;
  --eva-secondary-light: #ff8c5a;
  --eva-secondary-dark: #e55a2b;
  --eva-accent: #FFF100;
  --eva-accent-light: #ffff33;
  --eva-accent-dark: #e6d800;
  --eva-success: #198754;
  --eva-warning: #ffc107;
  --eva-danger: #dc3545;
  --eva-info: #0dcaf0;
  --eva-dark: #212529;
  --eva-light: #f8f9fa;
  --eva-muted: #6c757d;
  --eva-white: #ffffff;
  --eva-bg-primary: #ffffff;
  --eva-bg-secondary: #f8f9fa;
  --eva-bg-accent: rgba(49, 116, 173, 0.05);
  --eva-border-light: #dee2e6;
  --eva-border-primary: #3174ad;
  --eva-border-secondary: #ff6b35;
  --eva-gradient-primary: linear-gradient(135deg, #3174ad 0%, #4a8bc2 100%);
  --eva-gradient-secondary: linear-gradient(135deg, #ff6b35 0%, #ff8c5a 100%);
  --eva-gradient-accent: linear-gradient(135deg, #FFF100 0%, #ffff33 100%);
}

/* Override Bootstrap primary color */
.btn-primary {
  background-color: var(--eva-primary) !important;
  border-color: var(--eva-primary) !important;
}

.btn-primary:hover {
  background-color: var(--eva-primary-dark) !important;
  border-color: var(--eva-primary-dark) !important;
}

.btn-outline-primary {
  color: var(--eva-primary) !important;
  border-color: var(--eva-primary) !important;
}

.btn-outline-primary:hover {
  background-color: var(--eva-primary) !important;
  border-color: var(--eva-primary) !important;
}

/* Secondary button styles */
.btn-secondary {
  background-color: var(--eva-secondary) !important;
  border-color: var(--eva-secondary) !important;
}

.btn-secondary:hover {
  background-color: var(--eva-secondary-dark) !important;
  border-color: var(--eva-secondary-dark) !important;
}

.btn-outline-secondary {
  color: var(--eva-secondary) !important;
  border-color: var(--eva-secondary) !important;
}

.btn-outline-secondary:hover {
  background-color: var(--eva-secondary) !important;
  border-color: var(--eva-secondary) !important;
}

/* Text colors */
.text-primary {
  color: var(--eva-primary) !important;
}

.text-secondary {
  color: var(--eva-secondary) !important;
}

/* Border colors */
.border-primary {
  border-color: var(--eva-primary) !important;
}

.border-secondary {
  border-color: var(--eva-secondary) !important;
}

/* Background colors */
.bg-primary {
  background-color: var(--eva-primary) !important;
}

.bg-secondary {
  background-color: var(--eva-secondary) !important;
}

.bg-accent {
  background-color: var(--eva-bg-accent) !important;
}

/* Custom Eva styles */
.eva-card {
  background-color: var(--eva-bg-primary);
  border: 1px solid var(--eva-border-light);
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.2s ease;
}

.eva-card:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.eva-card-accent {
  background-color: var(--eva-bg-accent);
  border: 1px solid var(--eva-border-primary);
  border-radius: 8px;
}

.eva-gradient-primary {
  background: var(--eva-gradient-primary);
}

.eva-gradient-secondary {
  background: var(--eva-gradient-secondary);
}

.eva-gradient-accent {
  background: var(--eva-gradient-accent);
}

/* Navigation styles */
.navbar-brand {
  color: var(--eva-primary) !important;
  font-weight: 700;
}

.nav-link {
  color: var(--eva-dark) !important;
}

.nav-link:hover {
  color: var(--eva-primary) !important;
}

.nav-link.active {
  color: var(--eva-primary) !important;
  border-bottom: 2px solid var(--eva-primary);
}

/* Form styles */
.form-control:focus {
  border-color: var(--eva-primary);
  box-shadow: 0 0 0 0.2rem rgba(49, 116, 173, 0.25);
}

.form-select:focus {
  border-color: var(--eva-primary);
  box-shadow: 0 0 0 0.2rem rgba(49, 116, 173, 0.25);
}

/* Alert styles */
.alert-primary {
  background-color: var(--eva-bg-accent);
  border-color: var(--eva-primary);
  color: var(--eva-primary-dark);
}

.alert-secondary {
  background-color: rgba(255, 107, 53, 0.1);
  border-color: var(--eva-secondary);
  color: var(--eva-secondary-dark);
}

/* Badge styles */
.badge.bg-primary {
  background-color: var(--eva-primary) !important;
}

.badge.bg-secondary {
  background-color: var(--eva-secondary) !important;
}

/* Table styles */
.table-hover tbody tr:hover {
  background-color: var(--eva-bg-accent);
}

/* Calendar styles */
.rbc-calendar {
  font-family: inherit;
}

.rbc-header {
  background-color: var(--eva-bg-secondary);
  color: var(--eva-dark);
  font-weight: 600;
}

.rbc-today {
  background-color: var(--eva-bg-accent);
}

.rbc-toolbar button {
  color: var(--eva-primary);
  border-color: var(--eva-primary);
}

.rbc-toolbar button:hover {
  background-color: var(--eva-primary);
  color: var(--eva-white);
}

.rbc-toolbar button.rbc-active {
  background-color: var(--eva-primary);
  color: var(--eva-white);
}

/* Status indicators */
.status-draft {
  color: var(--eva-muted);
  background-color: rgba(108, 117, 125, 0.1);
}

.status-pending {
  color: var(--eva-warning);
  background-color: rgba(255, 193, 7, 0.1);
}

.status-confirmed {
  color: var(--eva-success);
  background-color: rgba(25, 135, 84, 0.1);
}

.status-rejected {
  color: var(--eva-danger);
  background-color: rgba(220, 53, 69, 0.1);
}

/* Priority indicators */
.priority-low {
  color: var(--eva-info);
}

.priority-medium {
  color: var(--eva-primary);
}

.priority-high {
  color: var(--eva-secondary);
}

.priority-critical {
  color: var(--eva-danger);
}

/* Utility classes */
.eva-shadow {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.eva-shadow-lg {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.eva-rounded {
  border-radius: 8px;
}

.eva-rounded-lg {
  border-radius: 12px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .eva-card {
    margin-bottom: 1rem;
  }
  
  .btn-group-vertical .btn {
    margin-bottom: 0.5rem;
  }
}

/* Animation utilities */
.eva-fade-in {
  animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.eva-slide-in {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}
