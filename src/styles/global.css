@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 210 79% 46%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 210 79% 46%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer components {
  /* WithKalenda custom components */
  .wk-container {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  .wk-card {
    @apply bg-white rounded-lg border shadow-sm hover:shadow-md transition-shadow duration-200;
  }

  .wk-card-accent {
    @apply bg-brand-blue/5 border-brand-blue/20 rounded-lg;
  }

  .wk-gradient-primary {
    @apply bg-gradient-to-br from-brand-blue to-brand-blue/80;
  }

  .wk-gradient-secondary {
    @apply bg-gradient-to-br from-brand-orange to-brand-orange/80;
  }

  .wk-gradient-accent {
    @apply bg-gradient-to-br from-brand-yellow to-brand-yellow/80;
  }

  /* Status indicators */
  .status-draft {
    @apply text-gray-600 bg-gray-100 px-2 py-1 rounded-full text-xs font-medium;
  }

  .status-pending {
    @apply text-yellow-700 bg-yellow-100 px-2 py-1 rounded-full text-xs font-medium;
  }

  .status-confirmed {
    @apply text-green-700 bg-green-100 px-2 py-1 rounded-full text-xs font-medium;
  }

  .status-rejected {
    @apply text-red-700 bg-red-100 px-2 py-1 rounded-full text-xs font-medium;
  }

  /* Priority indicators */
  .priority-low {
    @apply text-blue-600;
  }

  .priority-medium {
    @apply text-brand-blue;
  }

  .priority-high {
    @apply text-brand-orange;
  }

  .priority-critical {
    @apply text-red-600;
  }

  /* Animation utilities */
  .wk-fade-in {
    @apply animate-in fade-in duration-300;
  }

  .wk-slide-in {
    @apply animate-in slide-in-from-left duration-300;
  }
}
