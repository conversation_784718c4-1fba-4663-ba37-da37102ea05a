// WithKalenda Theme Colors
export const theme = {
  // Primary Colors (from homepage/landing)
  primary: '#3174ad',        // Primary blue
  primaryLight: '#4a8bc2',   // Lighter blue
  primaryDark: '#1e4a73',    // Darker blue

  // Secondary Colors
  secondary: '#ff6b35',      // Orange CTA color from homepage
  secondaryLight: '#ff8c5a', // Lighter orange
  secondaryDark: '#e55a2b',  // Darker orange

  // Accent Colors
  accent: '#FFF100',         // Yellow accent
  accentLight: '#ffff33',    // Lighter yellow
  accentDark: '#e6d800',     // Darker yellow

  // Status Colors
  success: '#198754',        // Green for confirmed/success
  warning: '#ffc107',        // Yellow for pending/warning
  danger: '#dc3545',         // Red for rejected/error
  info: '#0dcaf0',          // Light blue for info

  // Neutral Colors
  dark: '#212529',          // Dark text
  light: '#f8f9fa',         // Light background
  muted: '#6c757d',         // Muted text
  white: '#ffffff',         // White

  // Background Colors
  backgroundPrimary: '#ffffff',
  backgroundSecondary: '#f8f9fa',
  backgroundAccent: 'rgba(49, 116, 173, 0.05)', // Light blue tint

  // Border Colors
  borderLight: '#dee2e6',
  borderPrimary: '#3174ad',
  borderSecondary: '#ff6b35',

  // Gradient Colors
  gradientPrimary: 'linear-gradient(135deg, #3174ad 0%, #4a8bc2 100%)',
  gradientSecondary: 'linear-gradient(135deg, #ff6b35 0%, #ff8c5a 100%)',
  gradientAccent: 'linear-gradient(135deg, #FFF100 0%, #ffff33 100%)',
};

// CSS Custom Properties for easy usage
export const cssVariables = `
  :root {
    --eva-primary: ${theme.primary};
    --eva-primary-light: ${theme.primaryLight};
    --eva-primary-dark: ${theme.primaryDark};
    --eva-secondary: ${theme.secondary};
    --eva-secondary-light: ${theme.secondaryLight};
    --eva-secondary-dark: ${theme.secondaryDark};
    --eva-accent: ${theme.accent};
    --eva-accent-light: ${theme.accentLight};
    --eva-accent-dark: ${theme.accentDark};
    --eva-success: ${theme.success};
    --eva-warning: ${theme.warning};
    --eva-danger: ${theme.danger};
    --eva-info: ${theme.info};
    --eva-dark: ${theme.dark};
    --eva-light: ${theme.light};
    --eva-muted: ${theme.muted};
    --eva-white: ${theme.white};
    --eva-bg-primary: ${theme.backgroundPrimary};
    --eva-bg-secondary: ${theme.backgroundSecondary};
    --eva-bg-accent: ${theme.backgroundAccent};
    --eva-border-light: ${theme.borderLight};
    --eva-border-primary: ${theme.borderPrimary};
    --eva-border-secondary: ${theme.borderSecondary};
    --eva-gradient-primary: ${theme.gradientPrimary};
    --eva-gradient-secondary: ${theme.gradientSecondary};
    --eva-gradient-accent: ${theme.gradientAccent};
  }
`;

// Utility functions for theme usage
export const getStatusColor = (status: string): string => {
  switch (status.toLowerCase()) {
    case 'confirmed':
    case 'success':
    case 'approved':
      return theme.success;
    case 'pending':
    case 'warning':
      return theme.warning;
    case 'rejected':
    case 'error':
    case 'danger':
      return theme.danger;
    case 'draft':
    case 'info':
      return theme.info;
    default:
      return theme.muted;
  }
};

export const getPriorityColor = (priority: string): string => {
  switch (priority.toLowerCase()) {
    case 'critical':
      return theme.danger;
    case 'high':
      return theme.secondary;
    case 'medium':
      return theme.primary;
    case 'low':
      return theme.info;
    default:
      return theme.muted;
  }
};

// Button styles using theme colors
export const buttonStyles = {
  primary: {
    backgroundColor: theme.primary,
    borderColor: theme.primary,
    color: theme.white,
  },
  secondary: {
    backgroundColor: theme.secondary,
    borderColor: theme.secondary,
    color: theme.white,
  },
  outline: {
    backgroundColor: 'transparent',
    borderColor: theme.primary,
    color: theme.primary,
  },
  success: {
    backgroundColor: theme.success,
    borderColor: theme.success,
    color: theme.white,
  },
  warning: {
    backgroundColor: theme.warning,
    borderColor: theme.warning,
    color: theme.dark,
  },
  danger: {
    backgroundColor: theme.danger,
    borderColor: theme.danger,
    color: theme.white,
  },
};

// Card styles using theme colors
export const cardStyles = {
  primary: {
    backgroundColor: theme.backgroundPrimary,
    border: `1px solid ${theme.borderLight}`,
    borderRadius: '8px',
  },
  accent: {
    backgroundColor: theme.backgroundAccent,
    border: `1px solid ${theme.borderPrimary}`,
    borderRadius: '8px',
  },
  success: {
    backgroundColor: `${theme.success}10`,
    border: `1px solid ${theme.success}30`,
    borderRadius: '8px',
  },
  warning: {
    backgroundColor: `${theme.warning}10`,
    border: `1px solid ${theme.warning}30`,
    borderRadius: '8px',
  },
  danger: {
    backgroundColor: `${theme.danger}10`,
    border: `1px solid ${theme.danger}30`,
    borderRadius: '8px',
  },
};

export default theme;
