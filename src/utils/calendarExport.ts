import { createEvents, EventAttributes } from 'ics';
import { EvaEvent, CalendarExportOptions } from '../types/event';
import { createDateFromStrings, addMinutesToDate } from './dateUtils';

/**
 * Convert EvaEvent to ICS event format
 */
const convertToICSEvent = (event: EvaEvent): EventAttributes => {
  const startDate = createDateFromStrings(event.date, event.time);
  const endDate = addMinutesToDate(startDate, event.duration);

  return {
    start: [
      startDate.getFullYear(),
      startDate.getMonth() + 1, // ICS months are 1-based
      startDate.getDate(),
      startDate.getHours(),
      startDate.getMinutes()
    ],
    end: [
      endDate.getFullYear(),
      endDate.getMonth() + 1,
      endDate.getDate(),
      endDate.getHours(),
      endDate.getMinutes()
    ],
    title: event.eventName,
    description: [
      event.description || '',
      `Department: ${event.departmentName}`,
      `Organizer: ${event.organizer}`,
      `Status: ${event.status}`,
      event.resources && event.resources.length > 0 ? `Resources: ${event.resources.join(', ')}` : '',
      event.tags && event.tags.length > 0 ? `Tags: ${event.tags.join(', ')}` : ''
    ].filter(Boolean).join('\n'),
    location: event.venue,
    organizer: {
      name: event.organizer,
      email: `${event.organizerId}@company.com` // You might want to get actual email
    },
    categories: [event.departmentName, event.status, ...(event.tags || [])],
    status: event.status === 'confirmed' ? 'CONFIRMED' : 'TENTATIVE',
    uid: `event-${event.id}@company-calendar.com`,
    created: new Date(event.createdAt),
    lastModified: new Date(event.updatedAt)
  };
};

/**
 * Export events to iCal format
 */
export const exportToICal = (events: EvaEvent[], filename: string = 'events.ics'): void => {
  try {
    const icsEvents = events.map(convertToICSEvent);
    const { error, value } = createEvents(icsEvents);

    if (error) {
      console.error('Error creating iCal events:', error);
      throw new Error('Failed to create iCal file');
    }

    if (!value) {
      throw new Error('No iCal content generated');
    }

    // Create and download the file
    const blob = new Blob([value], { type: 'text/calendar;charset=utf-8' });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  } catch (error) {
    console.error('Error exporting to iCal:', error);
    throw error;
  }
};

/**
 * Generate Google Calendar URL for a single event
 */
export const generateGoogleCalendarUrl = (event: EvaEvent): string => {
  const startDate = createDateFromStrings(event.date, event.time);
  const endDate = addMinutesToDate(startDate, event.duration);

  const formatDateForGoogle = (date: Date): string => {
    return date.toISOString().replace(/[-:]/g, '').split('.')[0] + 'Z';
  };

  const params = new URLSearchParams({
    action: 'TEMPLATE',
    text: event.eventName,
    dates: `${formatDateForGoogle(startDate)}/${formatDateForGoogle(endDate)}`,
    details: [
      event.description || '',
      `Department: ${event.departmentName}`,
      `Organizer: ${event.organizer}`,
      `Status: ${event.status}`
    ].filter(Boolean).join('\n'),
    location: event.venue,
    ctz: Intl.DateTimeFormat().resolvedOptions().timeZone
  });

  return `https://calendar.google.com/calendar/render?${params.toString()}`;
};

/**
 * Export events to CSV format
 */
export const exportToCSV = (events: EvaEvent[], filename: string = 'events.csv'): void => {
  try {
    const headers = [
      'Event Name',
      'Description',
      'Date',
      'Time',
      'Duration (minutes)',
      'Venue',
      'Department',
      'Organizer',
      'Status',
      'Priority',
      'Estimated Attendees',
      'Budget',
      'Resources',
      'Tags',
      'Created At',
      'Updated At'
    ];

    const csvContent = [
      headers.join(','),
      ...events.map(event => [
        `"${event.eventName}"`,
        `"${event.description || ''}"`,
        event.date,
        event.time,
        event.duration,
        `"${event.venue}"`,
        `"${event.departmentName}"`,
        `"${event.organizer}"`,
        event.status,
        event.priority,
        event.estimatedAttendees || '',
        event.budget || '',
        `"${event.resources?.join('; ') || ''}"`,
        `"${event.tags?.join('; ') || ''}"`,
        event.createdAt,
        event.updatedAt
      ].join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8' });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  } catch (error) {
    console.error('Error exporting to CSV:', error);
    throw error;
  }
};

/**
 * Export events to JSON format
 */
export const exportToJSON = (events: EvaEvent[], filename: string = 'events.json'): void => {
  try {
    const jsonContent = JSON.stringify(events, null, 2);
    const blob = new Blob([jsonContent], { type: 'application/json;charset=utf-8' });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  } catch (error) {
    console.error('Error exporting to JSON:', error);
    throw error;
  }
};

/**
 * Filter events based on export options
 */
export const filterEventsForExport = (events: EvaEvent[], options: CalendarExportOptions): EvaEvent[] => {
  return events.filter(event => {
    // Filter by status
    if (!options.includeStatuses.includes(event.status)) {
      return false;
    }

    // Filter by department
    if (options.includeDepartments && options.includeDepartments.length > 0) {
      if (!options.includeDepartments.includes(event.departmentId)) {
        return false;
      }
    }

    // Filter by date range
    const eventDate = new Date(event.date);
    const startDate = new Date(options.dateRange.start);
    const endDate = new Date(options.dateRange.end);

    if (eventDate < startDate || eventDate > endDate) {
      return false;
    }

    return true;
  });
};

/**
 * Generate Outlook calendar URL for a single event
 */
export const generateOutlookUrl = (event: EvaEvent): string => {
  const startDate = createDateFromStrings(event.date, event.time);
  const endDate = addMinutesToDate(startDate, event.duration);

  const formatDateForOutlook = (date: Date): string => {
    return date.toISOString();
  };

  const params = new URLSearchParams({
    path: '/calendar/action/compose',
    rru: 'addevent',
    subject: event.eventName,
    startdt: formatDateForOutlook(startDate),
    enddt: formatDateForOutlook(endDate),
    body: [
      event.description || '',
      `Department: ${event.departmentName}`,
      `Organizer: ${event.organizer}`,
      `Status: ${event.status}`
    ].filter(Boolean).join('\n'),
    location: event.venue
  });

  return `https://outlook.live.com/calendar/0/deeplink/compose?${params.toString()}`;
};
