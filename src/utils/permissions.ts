import { User, UserRole } from '../types/user';
import { EvaEvent, EventStatus } from '../types/event';

export interface Permission {
  canCreateEvent: boolean;
  canEditEvent: (event: EvaEvent) => boolean;
  canDeleteEvent: (event: EvaEvent) => boolean;
  canApproveEvent: boolean;
  canRejectEvent: boolean;
  canViewAllEvents: boolean;
  canManageDepartments: boolean;
  canManageUsers: boolean;
  canSetDeadlines: boolean;
  canExportCalendar: boolean;
  canViewAnalytics: boolean;
}

/**
 * Get user permissions based on role and context
 */
export const getUserPermissions = (user: User | null): Permission => {
  if (!user) {
    return {
      canCreateEvent: false,
      canEditEvent: () => false,
      canDeleteEvent: () => false,
      canApproveEvent: false,
      canRejectEvent: false,
      canViewAllEvents: false,
      canManageDepartments: false,
      canManageUsers: false,
      canSetDeadlines: false,
      canExportCalendar: false,
      canViewAnalytics: false,
    };
  }

  const basePermissions = {
    canExportCalendar: true,
    canViewAnalytics: false,
  };

  switch (user.role) {
    case UserRole.ADMIN:
      return {
        ...basePermissions,
        canCreateEvent: true,
        canEditEvent: () => true,
        canDeleteEvent: () => true,
        canApproveEvent: true,
        canRejectEvent: true,
        canViewAllEvents: true,
        canManageDepartments: true,
        canManageUsers: true,
        canSetDeadlines: true,
        canViewAnalytics: true,
      };

    case UserRole.TEAM_LEADER:
      return {
        ...basePermissions,
        canCreateEvent: true,
        canEditEvent: (event: EvaEvent) => {
          // Team leaders can edit events from their department or events they created
          return event.departmentId === user.departmentId || event.organizerId === user.id;
        },
        canDeleteEvent: (event: EvaEvent) => {
          // Team leaders can delete their own events if not yet confirmed
          return (
            event.organizerId === user.id &&
            [EventStatus.DRAFT, EventStatus.PENDING].includes(event.status)
          );
        },
        canApproveEvent: false, // Only admins can approve
        canRejectEvent: false,
        canViewAllEvents: false, // Can only view department events
        canManageDepartments: false,
        canManageUsers: false,
        canSetDeadlines: false,
        canViewAnalytics: true, // Can view department analytics
      };

    case UserRole.EMPLOYEE:
      return {
        ...basePermissions,
        canCreateEvent: false, // Employees cannot create events
        canEditEvent: () => false,
        canDeleteEvent: () => false,
        canApproveEvent: false,
        canRejectEvent: false,
        canViewAllEvents: false, // Can only view department events
        canManageDepartments: false,
        canManageUsers: false,
        canSetDeadlines: false,
        canViewAnalytics: false,
      };

    default:
      return {
        ...basePermissions,
        canCreateEvent: false,
        canEditEvent: () => false,
        canDeleteEvent: () => false,
        canApproveEvent: false,
        canRejectEvent: false,
        canViewAllEvents: false,
        canManageDepartments: false,
        canManageUsers: false,
        canSetDeadlines: false,
        canViewAnalytics: false,
      };
  }
};

/**
 * Check if user can view a specific event
 */
export const canViewEvent = (user: User | null, event: EvaEvent): boolean => {
  if (!user) return false;

  const permissions = getUserPermissions(user);

  // Admins can view all events
  if (permissions.canViewAllEvents) return true;

  // Users can view events from their primary department
  if (event.departmentId === user.departmentId) return true;

  // Users can view events from their additional departments
  if (user.additionalDepartments?.includes(event.departmentId)) return true;

  // Users can view events they created
  if (event.organizerId === user.id) return true;

  // Users can view confirmed events that involve any of their departments
  if (event.status === EventStatus.CONFIRMED) {
    const userDepartments = [user.departmentId, ...(user.additionalDepartments || [])].filter(Boolean);
    if (event.departments.some(dept => userDepartments.includes(dept))) {
      return true;
    }
  }

  return false;
};

/**
 * Get filtered events based on user permissions
 */
export const getFilteredEvents = (user: User | null, events: EvaEvent[]): EvaEvent[] => {
  if (!user) return [];

  return events.filter(event => canViewEvent(user, event));
};

/**
 * Check if user can submit events (deadline check)
 */
export const canSubmitEvents = (deadline: string | null): boolean => {
  if (!deadline) return true; // No deadline set

  const now = new Date();
  const deadlineDate = new Date(deadline);

  return now <= deadlineDate;
};
