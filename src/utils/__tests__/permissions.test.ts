import { getUserPermissions, canViewEvent } from '../permissions';
import { User, UserRole } from '../../types/user';
import { EvaEvent, EventStatus } from '../../types/event';

const mockAdminUser: User = {
  id: 'admin-1',
  firstName: 'Admin',
  lastName: 'User',
  email: '<EMAIL>',
  role: UserRole.ADMIN,
  departmentId: 'admin',
  isActive: true
};

const mockTeamLeaderUser: User = {
  id: 'leader-1',
  firstName: 'Team',
  lastName: 'Leader',
  email: '<EMAIL>',
  role: UserRole.TEAM_LEADER,
  departmentId: 'engineering',
  additionalDepartments: ['marketing'],
  isActive: true
};

const mockEmployeeUser: User = {
  id: 'employee-1',
  firstName: 'Regular',
  lastName: 'Employee',
  email: '<EMAIL>',
  role: UserRole.EMPLOYEE,
  departmentId: 'engineering',
  isActive: true
};

const mockEvent: EvaEvent = {
  id: 'event-1',
  eventName: 'Test Event',
  description: 'Test Description',
  date: '2024-01-01',
  time: '10:00',
  venue: 'Test Venue',
  organizer: 'Test Organizer',
  organizerId: 'leader-1',
  departmentId: 'engineering',
  departmentName: 'Engineering',
  departments: ['engineering'],
  duration: 60,
  status: EventStatus.DRAFT,
  priority: 'medium' as any,
  createdAt: '2024-01-01T00:00:00Z',
  updatedAt: '2024-01-01T00:00:00Z'
};

describe('permissions', () => {
  describe('getUserPermissions', () => {
    it('returns no permissions for null user', () => {
      const permissions = getUserPermissions(null);

      expect(permissions.canCreateEvent).toBe(false);
      expect(permissions.canApproveEvent).toBe(false);
      expect(permissions.canRejectEvent).toBe(false);
      expect(permissions.canViewAllEvents).toBe(false);
      expect(permissions.canManageDepartments).toBe(false);
      expect(permissions.canManageUsers).toBe(false);
      expect(permissions.canSetDeadlines).toBe(false);
      expect(permissions.canExportCalendar).toBe(false);
      expect(permissions.canViewAnalytics).toBe(false);
      expect(permissions.canEditEvent(mockEvent)).toBe(false);
      expect(permissions.canDeleteEvent(mockEvent)).toBe(false);
    });

    it('returns full permissions for admin user', () => {
      const permissions = getUserPermissions(mockAdminUser);

      expect(permissions.canCreateEvent).toBe(true);
      expect(permissions.canApproveEvent).toBe(true);
      expect(permissions.canRejectEvent).toBe(true);
      expect(permissions.canViewAllEvents).toBe(true);
      expect(permissions.canManageDepartments).toBe(true);
      expect(permissions.canManageUsers).toBe(true);
      expect(permissions.canSetDeadlines).toBe(true);
      expect(permissions.canExportCalendar).toBe(true);
      expect(permissions.canViewAnalytics).toBe(true);
      expect(permissions.canEditEvent(mockEvent)).toBe(true);
      expect(permissions.canDeleteEvent(mockEvent)).toBe(true);
    });

    it('returns limited permissions for team leader', () => {
      const permissions = getUserPermissions(mockTeamLeaderUser);

      expect(permissions.canCreateEvent).toBe(true);
      expect(permissions.canApproveEvent).toBe(false);
      expect(permissions.canRejectEvent).toBe(false);
      expect(permissions.canViewAllEvents).toBe(false);
      expect(permissions.canManageDepartments).toBe(false);
      expect(permissions.canManageUsers).toBe(false);
      expect(permissions.canSetDeadlines).toBe(false);
      expect(permissions.canExportCalendar).toBe(true);
      expect(permissions.canViewAnalytics).toBe(true);
    });

    it('returns minimal permissions for employee', () => {
      const permissions = getUserPermissions(mockEmployeeUser);

      expect(permissions.canCreateEvent).toBe(false);
      expect(permissions.canApproveEvent).toBe(false);
      expect(permissions.canRejectEvent).toBe(false);
      expect(permissions.canViewAllEvents).toBe(false);
      expect(permissions.canManageDepartments).toBe(false);
      expect(permissions.canManageUsers).toBe(false);
      expect(permissions.canSetDeadlines).toBe(false);
      expect(permissions.canExportCalendar).toBe(true);
      expect(permissions.canViewAnalytics).toBe(false);
    });

    describe('canEditEvent function', () => {
      it('allows team leader to edit own events', () => {
        const permissions = getUserPermissions(mockTeamLeaderUser);
        const ownEvent = { ...mockEvent, organizerId: 'leader-1' };

        expect(permissions.canEditEvent(ownEvent)).toBe(true);
      });

      it('allows team leader to edit department events', () => {
        const permissions = getUserPermissions(mockTeamLeaderUser);
        const deptEvent = { ...mockEvent, departmentId: 'engineering' };

        expect(permissions.canEditEvent(deptEvent)).toBe(true);
      });

      it('prevents team leader from editing additional department events', () => {
        const permissions = getUserPermissions(mockTeamLeaderUser);
        const additionalDeptEvent = { ...mockEvent, departmentId: 'marketing', organizerId: 'other-user' };

        // Team leader logic only checks primary department and organizer, not additional departments
        expect(permissions.canEditEvent(additionalDeptEvent)).toBe(false);
      });

      it('prevents team leader from editing other department events', () => {
        const permissions = getUserPermissions(mockTeamLeaderUser);
        const otherDeptEvent = { ...mockEvent, departmentId: 'finance', organizerId: 'other-user' };

        expect(permissions.canEditEvent(otherDeptEvent)).toBe(false);
      });

      it('allows team leader to edit confirmed events they own', () => {
        const permissions = getUserPermissions(mockTeamLeaderUser);
        const confirmedEvent = { ...mockEvent, status: EventStatus.CONFIRMED, organizerId: 'leader-1' };

        // Team leaders can edit events they created regardless of status
        expect(permissions.canEditEvent(confirmedEvent)).toBe(true);
      });

      it('prevents employee from editing any events', () => {
        const permissions = getUserPermissions(mockEmployeeUser);

        expect(permissions.canEditEvent(mockEvent)).toBe(false);
      });
    });

    describe('canDeleteEvent function', () => {
      it('allows team leader to delete own draft events', () => {
        const permissions = getUserPermissions(mockTeamLeaderUser);
        const ownDraftEvent = {
          ...mockEvent,
          organizerId: 'leader-1',
          status: EventStatus.DRAFT
        };

        expect(permissions.canDeleteEvent(ownDraftEvent)).toBe(true);
      });

      it('allows team leader to delete pending events they own', () => {
        const permissions = getUserPermissions(mockTeamLeaderUser);
        const ownPendingEvent = {
          ...mockEvent,
          organizerId: 'leader-1',
          status: EventStatus.PENDING
        };

        // Team leaders can delete their own pending events
        expect(permissions.canDeleteEvent(ownPendingEvent)).toBe(true);
      });

      it('prevents team leader from deleting others\' events', () => {
        const permissions = getUserPermissions(mockTeamLeaderUser);
        const otherEvent = { ...mockEvent, organizerId: 'other-user' };

        expect(permissions.canDeleteEvent(otherEvent)).toBe(false);
      });

      it('prevents employee from deleting any events', () => {
        const permissions = getUserPermissions(mockEmployeeUser);

        expect(permissions.canDeleteEvent(mockEvent)).toBe(false);
      });
    });
  });

  describe('canViewEvent', () => {
    it('returns false for null user', () => {
      expect(canViewEvent(null, mockEvent)).toBe(false);
    });

    it('allows admin to view all events', () => {
      expect(canViewEvent(mockAdminUser, mockEvent)).toBe(true);
    });

    it('allows user to view events from their primary department', () => {
      const deptEvent = { ...mockEvent, departmentId: 'engineering' };
      expect(canViewEvent(mockTeamLeaderUser, deptEvent)).toBe(true);
      expect(canViewEvent(mockEmployeeUser, deptEvent)).toBe(true);
    });

    it('allows user to view events from their additional departments', () => {
      const additionalDeptEvent = { ...mockEvent, departmentId: 'marketing' };
      expect(canViewEvent(mockTeamLeaderUser, additionalDeptEvent)).toBe(true);
    });

    it('allows user to view events they created', () => {
      const ownEvent = { ...mockEvent, organizerId: 'leader-1' };
      expect(canViewEvent(mockTeamLeaderUser, ownEvent)).toBe(true);
    });

    it('prevents user from viewing events from other departments', () => {
      const otherDeptEvent = { ...mockEvent, departmentId: 'finance', organizerId: 'other-user' };
      expect(canViewEvent(mockTeamLeaderUser, otherDeptEvent)).toBe(false);
      expect(canViewEvent(mockEmployeeUser, otherDeptEvent)).toBe(false);
    });

    it('handles user without additional departments', () => {
      const userWithoutAdditional = { ...mockEmployeeUser, additionalDepartments: undefined };
      const deptEvent = { ...mockEvent, departmentId: 'engineering' };
      expect(canViewEvent(userWithoutAdditional, deptEvent)).toBe(true);
    });
  });



  describe('Edge Cases', () => {
    it('handles user with empty string departmentId', () => {
      const userWithEmptyDept = { ...mockEmployeeUser, departmentId: '' };
      const permissions = getUserPermissions(userWithEmptyDept);

      expect(permissions.canCreateEvent).toBe(false);
    });

    it('handles event with empty string departmentId', () => {
      const eventWithEmptyDept = { ...mockEvent, departmentId: '' };
      expect(canViewEvent(mockEmployeeUser, eventWithEmptyDept)).toBe(false);
    });

    it('handles user with null departmentId', () => {
      const userWithNullDept = { ...mockEmployeeUser, departmentId: undefined };
      const permissions = getUserPermissions(userWithNullDept);

      expect(permissions.canCreateEvent).toBe(false);
    });

    it('handles event with null organizerId', () => {
      const eventWithNullOrganizer = { ...mockEvent, organizerId: '' };
      expect(canViewEvent(mockTeamLeaderUser, eventWithNullOrganizer)).toBe(true); // Still in their dept
    });

    it('handles user with empty additionalDepartments array', () => {
      const userWithEmptyAdditional = { ...mockTeamLeaderUser, additionalDepartments: [] };
      const additionalDeptEvent = { ...mockEvent, departmentId: 'marketing', organizerId: 'other-user' };
      expect(canViewEvent(userWithEmptyAdditional, additionalDeptEvent)).toBe(false);
    });
  });
});
