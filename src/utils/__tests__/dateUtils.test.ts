// Jest is used instead of vitest
import {
  formatDate,
  formatTime,
  formatDateTime,
  isPastDate,
  isToday,
  isFuture,
  createDateFromStrings,
  addMinutesToDate
} from '../dateUtils';

// Mock date-fns functions
jest.mock('date-fns', () => ({
  format: jest.fn((date, formatStr) => {
    if (formatStr === 'MMM dd, yyyy') return 'Jan 01, 2024';
    if (formatStr === 'h:mm a') return '10:30 AM';
    if (formatStr === 'MMM dd, yyyy h:mm a') return 'Jan 01, 2024 10:30 AM';
    return 'formatted-date';
  }),
  parseISO: jest.fn((dateString) => {
    if (dateString === '2024-01-01') return new Date('2024-01-01T00:00:00.000Z');
    if (dateString === 'invalid-date') throw new Error('Invalid date');
    return new Date(dateString);
  }),
  isValid: jest.fn((date) => {
    return date instanceof Date && !isNaN(date.getTime());
  }),
  isPast: jest.fn(() => true),
  isToday: jest.fn(() => false),
  isFuture: jest.fn(() => false)
}));

describe('dateUtils', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('formatDate', () => {
    it('formats valid date string correctly', () => {
      const result = formatDate('2024-01-01');
      expect(result).toBe('Jan 01, 2024');
    });

    it('returns original string for invalid date', () => {
      const { parseISO, isValid } = require('date-fns');
      parseISO.mockReturnValueOnce(new Date('invalid'));
      isValid.mockReturnValueOnce(false);
      
      const result = formatDate('invalid-date');
      expect(result).toBe('invalid-date');
    });

    it('handles parsing errors gracefully', () => {
      const { parseISO } = require('date-fns');
      parseISO.mockImplementationOnce(() => {
        throw new Error('Parse error');
      });
      
      const result = formatDate('error-date');
      expect(result).toBe('error-date');
    });

    it('handles empty string', () => {
      const result = formatDate('');
      expect(result).toBe('');
    });
  });

  describe('formatTime', () => {
    it('formats valid time string correctly', () => {
      const result = formatTime('10:30');
      expect(result).toBe('10:30 AM');
    });

    it('returns original string for invalid time', () => {
      const { parseISO, isValid } = require('date-fns');
      parseISO.mockReturnValueOnce(new Date('invalid'));
      isValid.mockReturnValueOnce(false);
      
      const result = formatTime('invalid-time');
      expect(result).toBe('invalid-time');
    });

    it('handles parsing errors gracefully', () => {
      const { parseISO } = require('date-fns');
      parseISO.mockImplementationOnce(() => {
        throw new Error('Parse error');
      });
      
      const result = formatTime('error-time');
      expect(result).toBe('error-time');
    });

    it('handles empty string', () => {
      const result = formatTime('');
      expect(result).toBe('');
    });
  });

  describe('formatDateTime', () => {
    it('formats valid date and time correctly', () => {
      const result = formatDateTime('2024-01-01', '10:30');
      expect(result).toBe('Jan 01, 2024 10:30 AM');
    });

    it('returns empty string for invalid date', () => {
      const { parseISO, isValid } = require('date-fns');
      parseISO.mockReturnValueOnce(new Date('invalid'));
      isValid.mockReturnValueOnce(false);
      
      const result = formatDateTime('invalid-date', '10:30');
      expect(result).toBe('');
    });

    it('handles parsing errors gracefully', () => {
      const { parseISO } = require('date-fns');
      parseISO.mockImplementationOnce(() => {
        throw new Error('Parse error');
      });
      
      const result = formatDateTime('error-date', '10:30');
      expect(result).toBe('');
    });

    it('handles empty inputs', () => {
      const result = formatDateTime('', '');
      expect(result).toBe('');
    });
  });

  describe('isPastDate', () => {
    it('returns true for past dates', () => {
      const { isPast } = require('date-fns');
      isPast.mockReturnValueOnce(true);
      
      const result = isPastDate('2023-01-01');
      expect(result).toBe(true);
    });

    it('returns false for future dates', () => {
      const { isPast } = require('date-fns');
      isPast.mockReturnValueOnce(false);
      
      const result = isPastDate('2025-01-01');
      expect(result).toBe(false);
    });

    it('returns false for invalid dates', () => {
      const { parseISO, isValid } = require('date-fns');
      parseISO.mockReturnValueOnce(new Date('invalid'));
      isValid.mockReturnValueOnce(false);
      
      const result = isPastDate('invalid-date');
      expect(result).toBe(false);
    });

    it('handles parsing errors gracefully', () => {
      const { parseISO } = require('date-fns');
      parseISO.mockImplementationOnce(() => {
        throw new Error('Parse error');
      });
      
      const result = isPastDate('error-date');
      expect(result).toBe(false);
    });
  });

  describe('isToday', () => {
    it('returns true for today\'s date', () => {
      const { isToday: isTodayFn } = require('date-fns');
      isTodayFn.mockReturnValueOnce(true);
      
      const result = isToday('2024-01-01');
      expect(result).toBe(true);
    });

    it('returns false for other dates', () => {
      const { isToday: isTodayFn } = require('date-fns');
      isTodayFn.mockReturnValueOnce(false);
      
      const result = isToday('2023-01-01');
      expect(result).toBe(false);
    });

    it('returns false for invalid dates', () => {
      const { parseISO, isValid } = require('date-fns');
      parseISO.mockReturnValueOnce(new Date('invalid'));
      isValid.mockReturnValueOnce(false);
      
      const result = isToday('invalid-date');
      expect(result).toBe(false);
    });
  });

  describe('isFuture', () => {
    it('returns true for future dates', () => {
      const { isFuture: isFutureFn } = require('date-fns');
      isFutureFn.mockReturnValueOnce(true);
      
      const result = isFuture('2025-01-01');
      expect(result).toBe(true);
    });

    it('returns false for past dates', () => {
      const { isFuture: isFutureFn } = require('date-fns');
      isFutureFn.mockReturnValueOnce(false);
      
      const result = isFuture('2023-01-01');
      expect(result).toBe(false);
    });

    it('returns false for invalid dates', () => {
      const { parseISO, isValid } = require('date-fns');
      parseISO.mockReturnValueOnce(new Date('invalid'));
      isValid.mockReturnValueOnce(false);
      
      const result = isFuture('invalid-date');
      expect(result).toBe(false);
    });
  });

  describe('createDateFromStrings', () => {
    it('creates date from valid date and time strings', () => {
      const result = createDateFromStrings('2024-01-01', '10:30');
      
      expect(result).toBeInstanceOf(Date);
      expect(result.getHours()).toBe(10);
      expect(result.getMinutes()).toBe(30);
    });

    it('returns current date for invalid date string', () => {
      const { parseISO, isValid } = require('date-fns');
      parseISO.mockReturnValueOnce(new Date('invalid'));
      isValid.mockReturnValueOnce(false);
      
      const result = createDateFromStrings('invalid-date', '10:30');
      expect(result).toBeInstanceOf(Date);
    });

    it('handles invalid time format gracefully', () => {
      const result = createDateFromStrings('2024-01-01', 'invalid-time');
      expect(result).toBeInstanceOf(Date);
    });

    it('handles parsing errors gracefully', () => {
      const { parseISO } = require('date-fns');
      parseISO.mockImplementationOnce(() => {
        throw new Error('Parse error');
      });
      
      const result = createDateFromStrings('error-date', '10:30');
      expect(result).toBeInstanceOf(Date);
    });

    it('handles empty time string', () => {
      const result = createDateFromStrings('2024-01-01', '');
      expect(result).toBeInstanceOf(Date);
    });

    it('handles time with seconds', () => {
      const result = createDateFromStrings('2024-01-01', '10:30:45');
      
      expect(result).toBeInstanceOf(Date);
      expect(result.getHours()).toBe(10);
      expect(result.getMinutes()).toBe(30);
    });
  });

  describe('addMinutesToDate', () => {
    it('adds minutes to date correctly', () => {
      const baseDate = new Date('2024-01-01T10:00:00.000Z');
      const result = addMinutesToDate(baseDate, 30);
      
      expect(result).toBeInstanceOf(Date);
      expect(result.getTime()).toBe(baseDate.getTime() + 30 * 60000);
    });

    it('handles negative minutes', () => {
      const baseDate = new Date('2024-01-01T10:00:00.000Z');
      const result = addMinutesToDate(baseDate, -30);
      
      expect(result).toBeInstanceOf(Date);
      expect(result.getTime()).toBe(baseDate.getTime() - 30 * 60000);
    });

    it('handles zero minutes', () => {
      const baseDate = new Date('2024-01-01T10:00:00.000Z');
      const result = addMinutesToDate(baseDate, 0);
      
      expect(result).toBeInstanceOf(Date);
      expect(result.getTime()).toBe(baseDate.getTime());
    });

    it('handles large minute values', () => {
      const baseDate = new Date('2024-01-01T10:00:00.000Z');
      const result = addMinutesToDate(baseDate, 1440); // 24 hours
      
      expect(result).toBeInstanceOf(Date);
      expect(result.getTime()).toBe(baseDate.getTime() + 1440 * 60000);
    });

    it('does not mutate original date', () => {
      const baseDate = new Date('2024-01-01T10:00:00.000Z');
      const originalTime = baseDate.getTime();
      
      addMinutesToDate(baseDate, 30);
      
      expect(baseDate.getTime()).toBe(originalTime);
    });
  });

  describe('Edge Cases', () => {
    it('handles null and undefined inputs', () => {
      expect(formatDate(null as any)).toBe(null);
      expect(formatTime(undefined as any)).toBe(undefined);
      expect(isPastDate(null as any)).toBe(false);
    });

    it('handles very long date strings', () => {
      const longString = 'a'.repeat(1000);
      expect(formatDate(longString)).toBe(longString);
    });

    it('handles special characters in date strings', () => {
      const specialString = '2024-01-01T10:30:00.000Z@#$%';
      const result = formatDate(specialString);
      expect(typeof result).toBe('string');
    });
  });
});
