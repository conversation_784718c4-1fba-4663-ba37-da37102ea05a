import { format, parseISO, isValid } from 'date-fns';

/**
 * Format a date string for display
 */
export const formatDate = (dateString: string): string => {
  try {
    const date = parseISO(dateString);
    if (!isValid(date)) {
      return dateString; // Return original if invalid
    }
    return format(date, 'MMM dd, yyyy');
  } catch {
    return dateString;
  }
};

/**
 * Format a time string for display
 */
export const formatTime = (timeString: string): string => {
  try {
    // Handle both HH:mm and HH:mm:ss formats
    const timeParts = timeString.split(':');
    if (timeParts.length >= 2) {
      const hours = parseInt(timeParts[0], 10);
      const minutes = parseInt(timeParts[1], 10);
      
      if (isNaN(hours) || isNaN(minutes)) {
        return timeString;
      }
      
      const date = new Date();
      date.setHours(hours, minutes, 0, 0);
      return format(date, 'h:mm a');
    }
    return timeString;
  } catch {
    return timeString;
  }
};

/**
 * Format a full date and time for display
 */
export const formatDateTime = (dateString: string, timeString: string): string => {
  return `${formatDate(dateString)} at ${formatTime(timeString)}`;
};

/**
 * Check if a date is in the past
 */
export const isPastDate = (dateString: string, timeString?: string): boolean => {
  try {
    const date = parseISO(dateString);
    if (!isValid(date)) return false;
    
    if (timeString) {
      const timeParts = timeString.split(':');
      if (timeParts.length >= 2) {
        date.setHours(parseInt(timeParts[0], 10), parseInt(timeParts[1], 10));
      }
    }
    
    return date < new Date();
  } catch {
    return false;
  }
};

/**
 * Get relative time description (e.g., "in 3 days", "2 hours ago")
 */
export const getRelativeTime = (dateString: string, timeString?: string): string => {
  try {
    const date = parseISO(dateString);
    if (!isValid(date)) return '';
    
    if (timeString) {
      const timeParts = timeString.split(':');
      if (timeParts.length >= 2) {
        date.setHours(parseInt(timeParts[0], 10), parseInt(timeParts[1], 10));
      }
    }
    
    const now = new Date();
    const diffMs = date.getTime() - now.getTime();
    const diffDays = Math.ceil(diffMs / (1000 * 60 * 60 * 24));
    const diffHours = Math.ceil(diffMs / (1000 * 60 * 60));
    const diffMinutes = Math.ceil(diffMs / (1000 * 60));
    
    if (diffDays > 1) {
      return `in ${diffDays} days`;
    } else if (diffDays === 1) {
      return 'tomorrow';
    } else if (diffDays === 0) {
      return 'today';
    } else if (diffDays === -1) {
      return 'yesterday';
    } else if (diffDays < -1) {
      return `${Math.abs(diffDays)} days ago`;
    } else if (diffHours > 0) {
      return `in ${diffHours} hours`;
    } else if (diffHours < 0) {
      return `${Math.abs(diffHours)} hours ago`;
    } else if (diffMinutes > 0) {
      return `in ${diffMinutes} minutes`;
    } else {
      return `${Math.abs(diffMinutes)} minutes ago`;
    }
  } catch {
    return '';
  }
};

/**
 * Convert date and time strings to Date object for react-big-calendar
 */
export const createDateFromStrings = (dateString: string, timeString: string): Date => {
  try {
    const date = parseISO(dateString);
    if (!isValid(date)) return new Date();
    
    const timeParts = timeString.split(':');
    if (timeParts.length >= 2) {
      date.setHours(parseInt(timeParts[0], 10), parseInt(timeParts[1], 10), 0, 0);
    }
    
    return date;
  } catch {
    return new Date();
  }
};

/**
 * Add duration (in minutes) to a date
 */
export const addMinutesToDate = (date: Date, minutes: number): Date => {
  return new Date(date.getTime() + minutes * 60000);
};
