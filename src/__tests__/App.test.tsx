import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
// Jest is used instead of vitest
import App from '../App';

// Mock all the page components
jest.mock('../pages/Dashboard/Home', () => ({
  default: () => <div data-testid="dashboard-home">Dashboard Home</div>
}));

jest.mock('../pages/Features', () => ({
  default: () => <div data-testid="features">Features Page</div>
}));

jest.mock('../pages/About', () => ({
  default: () => <div data-testid="about">About Page</div>
}));

jest.mock('../pages/Contact', () => ({
  default: () => <div data-testid="contact">Contact Page</div>
}));

jest.mock('../pages/Demo', () => ({
  default: () => <div data-testid="demo">Demo Page</div>
}));

jest.mock('../pages/Login', () => ({
  default: () => <div data-testid="login">Login Page</div>
}));

jest.mock('../pages/Signup', () => ({
  default: () => <div data-testid="signup">Signup Page</div>
}));

jest.mock('../pages/ForgotPassword', () => ({
  default: () => <div data-testid="forgot-password">Forgot Password Page</div>
}));

jest.mock('../pages/Profile', () => ({
  default: () => <div data-testid="profile">Profile Page</div>
}));

jest.mock('../pages/Privacy', () => ({
  default: () => <div data-testid="privacy">Privacy Page</div>
}));

jest.mock('../pages/Terms', () => ({
  default: () => <div data-testid="terms">Terms Page</div>
}));

jest.mock('../pages/Pricing', () => ({
  default: () => <div data-testid="pricing">Pricing Page</div>
}));

jest.mock('../components/Planner', () => ({
  default: () => <div data-testid="planner">Planner Component</div>
}));

jest.mock('../components/Landing', () => ({
  default: () => <div data-testid="landing">Landing Page</div>
}));

jest.mock('../components/Home', () => ({
  default: () => <div data-testid="home">Home Page</div>
}));

jest.mock('../components/Layout', () => ({
  default: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="layout">{children}</div>
  )
}));

jest.mock('../components/ProtectedRoute', () => ({
  default: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="protected-route">{children}</div>
  )
}));

// Mock the AuthProvider
jest.mock('../contexts/AuthContext', () => ({
  AuthProvider: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="auth-provider">{children}</div>
  )
}));

// Mock React Router
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  BrowserRouter: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="browser-router">{children}</div>
  ),
  Routes: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="routes">{children}</div>
  ),
  Route: ({ element }: { element: React.ReactNode }) => (
    <div data-testid="route">{element}</div>
  ),
  useNavigate: () => mockNavigate
}));

// Mock Suspense fallback
const originalSuspense = React.Suspense;
React.Suspense = ({ children, fallback }: { children: React.ReactNode; fallback: React.ReactNode }) => {
  return <div data-testid="suspense">{children}</div>;
};

describe('App', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  afterAll(() => {
    React.Suspense = originalSuspense;
  });

  it('renders without crashing', () => {
    render(<App />);
    
    expect(screen.getByTestId('auth-provider')).toBeInTheDocument();
    expect(screen.getByTestId('browser-router')).toBeInTheDocument();
    expect(screen.getByTestId('suspense')).toBeInTheDocument();
    expect(screen.getByTestId('routes')).toBeInTheDocument();
  });

  it('wraps app in AuthProvider', () => {
    render(<App />);
    
    const authProvider = screen.getByTestId('auth-provider');
    const browserRouter = screen.getByTestId('browser-router');
    
    expect(authProvider).toBeInTheDocument();
    expect(authProvider).toContainElement(browserRouter);
  });

  it('wraps app in BrowserRouter', () => {
    render(<App />);
    
    const browserRouter = screen.getByTestId('browser-router');
    const suspense = screen.getByTestId('suspense');
    
    expect(browserRouter).toBeInTheDocument();
    expect(browserRouter).toContainElement(suspense);
  });

  it('wraps routes in Suspense', () => {
    render(<App />);
    
    const suspense = screen.getByTestId('suspense');
    const routes = screen.getByTestId('routes');
    
    expect(suspense).toBeInTheDocument();
    expect(suspense).toContainElement(routes);
  });

  it('renders all route components', () => {
    render(<App />);
    
    // Check that Routes component is rendered
    expect(screen.getByTestId('routes')).toBeInTheDocument();
    
    // Check that Route components are rendered
    const routes = screen.getAllByTestId('route');
    expect(routes.length).toBeGreaterThan(0);
  });

  it('includes global CSS imports', () => {
    // This test verifies that the CSS imports don't cause errors
    expect(() => render(<App />)).not.toThrow();
  });

  it('handles Suspense fallback', async () => {
    // Mock a component that throws a promise (simulating lazy loading)
    const LazyComponent = () => {
      throw new Promise(resolve => setTimeout(resolve, 100));
    };

    const TestApp = () => (
      <React.Suspense fallback={<div data-testid="loading">Loading...</div>}>
        <LazyComponent />
      </React.Suspense>
    );

    render(<TestApp />);
    
    expect(screen.getByTestId('loading')).toBeInTheDocument();
  });

  it('provides context to child components', () => {
    const TestComponent = () => {
      return <div data-testid="test-component">Test</div>;
    };

    const TestApp = () => (
      <App />
    );

    render(<TestApp />);
    
    // Verify that the app structure is correct
    expect(screen.getByTestId('auth-provider')).toBeInTheDocument();
    expect(screen.getByTestId('browser-router')).toBeInTheDocument();
  });

  it('handles routing structure correctly', () => {
    render(<App />);
    
    // Verify the routing structure is set up
    expect(screen.getByTestId('browser-router')).toBeInTheDocument();
    expect(screen.getByTestId('routes')).toBeInTheDocument();
    
    // Verify multiple routes are rendered
    const routes = screen.getAllByTestId('route');
    expect(routes.length).toBeGreaterThan(1);
  });

  it('imports required stylesheets', () => {
    // Test that importing the component doesn't throw errors related to CSS
    expect(() => {
      const AppModule = require('../App');
      expect(AppModule.default).toBeDefined();
    }).not.toThrow();
  });

  it('sets up proper component hierarchy', () => {
    render(<App />);
    
    const authProvider = screen.getByTestId('auth-provider');
    const browserRouter = screen.getByTestId('browser-router');
    const suspense = screen.getByTestId('suspense');
    const routes = screen.getByTestId('routes');
    
    // Check hierarchy: AuthProvider > BrowserRouter > Suspense > Routes
    expect(authProvider).toContainElement(browserRouter);
    expect(browserRouter).toContainElement(suspense);
    expect(suspense).toContainElement(routes);
  });

  it('handles component lazy loading', async () => {
    // Verify that the app can handle lazy-loaded components
    render(<App />);
    
    await waitFor(() => {
      expect(screen.getByTestId('routes')).toBeInTheDocument();
    });
  });

  it('provides error boundary protection', () => {
    // Test that the app doesn't crash with unexpected errors
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
    
    expect(() => render(<App />)).not.toThrow();
    
    consoleSpy.mockRestore();
  });

  it('maintains consistent provider order', () => {
    render(<App />);
    
    // AuthProvider should be the outermost provider
    const authProvider = screen.getByTestId('auth-provider');
    expect(authProvider).toBeInTheDocument();
    
    // BrowserRouter should be inside AuthProvider
    const browserRouter = screen.getByTestId('browser-router');
    expect(authProvider).toContainElement(browserRouter);
  });

  it('handles multiple route definitions', () => {
    render(<App />);
    
    const routes = screen.getAllByTestId('route');
    
    // Should have multiple routes for different pages
    expect(routes.length).toBeGreaterThanOrEqual(10);
  });

  it('supports nested routing structure', () => {
    render(<App />);
    
    // Verify that the routing structure supports nested routes
    expect(screen.getByTestId('routes')).toBeInTheDocument();
    
    // Check that routes are properly nested within the structure
    const routes = screen.getAllByTestId('route');
    routes.forEach(route => {
      expect(route).toBeInTheDocument();
    });
  });

  it('handles global state management setup', () => {
    render(<App />);
    
    // Verify that AuthProvider is set up for global state
    expect(screen.getByTestId('auth-provider')).toBeInTheDocument();
  });

  it('supports accessibility features', () => {
    render(<App />);
    
    // Basic accessibility check - app should render without accessibility violations
    const app = screen.getByTestId('auth-provider');
    expect(app).toBeInTheDocument();
    
    // Should not have any obvious accessibility issues in the structure
    expect(app.getAttribute('role')).not.toBe('presentation');
  });

  it('handles responsive design setup', () => {
    // Test that the app structure supports responsive design
    render(<App />);
    
    expect(screen.getByTestId('auth-provider')).toBeInTheDocument();
    
    // The app should render without layout issues
    const routes = screen.getAllByTestId('route');
    expect(routes.length).toBeGreaterThan(0);
  });
});
