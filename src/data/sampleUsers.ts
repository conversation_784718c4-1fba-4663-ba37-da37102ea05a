import { User, UserRole } from '../types/user';
import { Department } from '../types/user';

// Sample departments for reference
export const sampleDepartments: Department[] = [
  {
    id: 'admin',
    name: 'Administration',
    description: 'Executive management and administrative functions',
    teamLeaderId: 'admin-user',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  },
  {
    id: 'hr',
    name: 'Human Resources',
    description: 'Employee relations, recruitment, and HR policies',
    teamLeaderId: 'hr-lead-1',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  },
  {
    id: 'it',
    name: 'Information Technology',
    description: 'IT infrastructure, software development, and technical support',
    teamLeaderId: 'it-lead-1',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  },
  {
    id: 'marketing',
    name: 'Marketing',
    description: 'Brand management, advertising, and market research',
    teamLeaderId: 'marketing-lead',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  },
  {
    id: 'sales',
    name: 'Sales',
    description: 'Customer acquisition, account management, and revenue generation',
    teamLeaderId: 'sales-lead-1',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  },
  {
    id: 'finance',
    name: 'Finance',
    description: 'Financial planning, accounting, and budget management',
    teamLeaderId: 'finance-lead-1',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  },
  {
    id: 'operations',
    name: 'Operations',
    description: 'Business operations, logistics, and process management',
    teamLeaderId: 'ops-lead-1',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  }
];

// Sample users with comprehensive data
export const sampleUsers: User[] = [
  // Administrator User
  {
    id: 'admin-user',
    firstName: 'Admin',
    lastName: 'User',
    email: '<EMAIL>',
    phone: '+****************',
    organization: 'Demo Company Inc.',
    role: UserRole.ADMIN,
    departmentId: 'admin',
    department: sampleDepartments.find(d => d.id === 'admin'),
    additionalDepartments: ['hr', 'it', 'marketing', 'sales', 'finance', 'operations'],
    additionalDepartmentObjects: sampleDepartments.filter(d => d.id !== 'admin'),
    location: 'New York, NY',
    bio: 'System administrator responsible for overall event management and conflict resolution. Oversees all company departments and ensures smooth coordination of events.',
    timezone: 'America/New_York',
    isActive: true,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: new Date().toISOString()
  },
  
  // Team Leader User (Marketing)
  {
    id: 'marketing-lead',
    firstName: 'Marketing',
    lastName: 'Lead',
    email: '<EMAIL>',
    phone: '+****************',
    organization: 'Demo Company Inc.',
    role: UserRole.TEAM_LEADER,
    departmentId: 'marketing',
    department: sampleDepartments.find(d => d.id === 'marketing'),
    additionalDepartments: ['sales'],
    additionalDepartmentObjects: sampleDepartments.filter(d => d.id === 'sales'),
    location: 'Los Angeles, CA',
    bio: 'Marketing team leader responsible for coordinating marketing events and campaigns. Collaborates closely with sales team for integrated marketing efforts.',
    timezone: 'America/Los_Angeles',
    isActive: true,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: new Date().toISOString()
  },

  // Additional Team Leaders
  {
    id: 'hr-lead-1',
    firstName: 'Sarah',
    lastName: 'Johnson',
    email: '<EMAIL>',
    phone: '+****************',
    organization: 'Demo Company Inc.',
    role: UserRole.TEAM_LEADER,
    departmentId: 'hr',
    department: sampleDepartments.find(d => d.id === 'hr'),
    additionalDepartments: ['admin'],
    additionalDepartmentObjects: sampleDepartments.filter(d => d.id === 'admin'),
    location: 'Chicago, IL',
    bio: 'HR team leader focused on employee engagement and organizational development.',
    timezone: 'America/Chicago',
    isActive: true,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: new Date().toISOString()
  },

  {
    id: 'it-lead-1',
    firstName: 'Michael',
    lastName: 'Chen',
    email: '<EMAIL>',
    phone: '+****************',
    organization: 'Demo Company Inc.',
    role: UserRole.TEAM_LEADER,
    departmentId: 'it',
    department: sampleDepartments.find(d => d.id === 'it'),
    additionalDepartments: ['operations'],
    additionalDepartmentObjects: sampleDepartments.filter(d => d.id === 'operations'),
    location: 'Austin, TX',
    bio: 'IT team leader specializing in infrastructure and security management.',
    timezone: 'America/Chicago',
    isActive: true,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: new Date().toISOString()
  },

  // Sample Employees
  {
    id: 'emp-1',
    firstName: 'John',
    lastName: 'Smith',
    email: '<EMAIL>',
    phone: '+****************',
    organization: 'Demo Company Inc.',
    role: UserRole.EMPLOYEE,
    departmentId: 'marketing',
    department: sampleDepartments.find(d => d.id === 'marketing'),
    additionalDepartments: [],
    additionalDepartmentObjects: [],
    location: 'San Francisco, CA',
    bio: 'Marketing specialist focused on digital campaigns and social media.',
    timezone: 'America/Los_Angeles',
    isActive: true,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: new Date().toISOString()
  },

  {
    id: 'emp-2',
    firstName: 'Emily',
    lastName: 'Davis',
    email: '<EMAIL>',
    phone: '+****************',
    organization: 'Demo Company Inc.',
    role: UserRole.EMPLOYEE,
    departmentId: 'sales',
    department: sampleDepartments.find(d => d.id === 'sales'),
    additionalDepartments: ['marketing'],
    additionalDepartmentObjects: sampleDepartments.filter(d => d.id === 'marketing'),
    location: 'Miami, FL',
    bio: 'Sales representative specializing in enterprise accounts.',
    timezone: 'America/New_York',
    isActive: true,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: new Date().toISOString()
  }
];

// Function to get user by email (for login)
export const getUserByEmail = (email: string): User | undefined => {
  return sampleUsers.find(user => user.email.toLowerCase() === email.toLowerCase());
};

// Function to get user by ID
export const getUserById = (id: string): User | undefined => {
  return sampleUsers.find(user => user.id === id);
};

// Function to get users by department
export const getUsersByDepartment = (departmentId: string): User[] => {
  return sampleUsers.filter(user => 
    user.departmentId === departmentId || 
    user.additionalDepartments?.includes(departmentId)
  );
};

// Function to get all departments a user belongs to
export const getUserDepartments = (user: User): Department[] => {
  const departments: Department[] = [];
  
  // Add primary department
  if (user.department) {
    departments.push(user.department);
  }
  
  // Add additional departments
  if (user.additionalDepartmentObjects) {
    departments.push(...user.additionalDepartmentObjects);
  }
  
  return departments;
};
