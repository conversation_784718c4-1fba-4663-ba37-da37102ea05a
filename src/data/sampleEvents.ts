import { EvaEvent, EventStatus, EventPriority } from '../types/event';

// Sample events with comprehensive data
export const sampleEvents: EvaEvent[] = [
  // Confirmed Events
  {
    id: 'event-1',
    eventName: 'Q1 Marketing Campaign Launch',
    description: 'Launch of the new product marketing campaign for Q1. Includes presentation of campaign materials, timeline review, and team assignments.',
    date: '2024-03-15',
    time: '10:00',
    venue: 'Conference Room A, Marketing Floor',
    organizer: 'Marketing Lead',
    organizerId: 'marketing-lead',
    departmentId: 'marketing',
    departmentName: 'Marketing',
    departments: ['marketing', 'sales'],
    duration: 120,
    status: EventStatus.CONFIRMED,
    priority: EventPriority.HIGH,
    maxAttendees: 25,
    estimatedAttendees: 20,
    budget: 5000,
    resources: ['projector', 'microphone', 'catering', 'wifi'],
    conflicts: [],
    approvals: [{
      id: 'approval-1',
      eventId: 'event-1',
      reviewerId: 'admin-user',
      reviewerName: 'Admin User',
      status: 'approved',
      comments: 'Approved for Q1 launch. Budget allocated.',
      reviewedAt: '2024-02-15T10:00:00Z'
    }],
    isRecurring: false,
    tags: ['marketing', 'campaign', 'product-launch'],
    createdAt: '2024-02-01T09:00:00Z',
    updatedAt: '2024-02-15T10:00:00Z',
    submittedAt: '2024-02-10T14:00:00Z',
    approvedAt: '2024-02-15T10:00:00Z'
  },

  {
    id: 'event-2',
    eventName: 'IT Security Training',
    description: 'Mandatory cybersecurity training for all employees. Covers latest security protocols, phishing awareness, and password management.',
    date: '2024-03-20',
    time: '14:00',
    venue: 'Main Auditorium',
    organizer: 'Michael Chen',
    organizerId: 'it-lead-1',
    departmentId: 'it',
    departmentName: 'Information Technology',
    departments: ['it', 'hr', 'admin'],
    duration: 90,
    status: EventStatus.CONFIRMED,
    priority: EventPriority.MEDIUM,
    maxAttendees: 100,
    estimatedAttendees: 85,
    budget: 2000,
    resources: ['projector', 'microphone', 'speakers', 'recording'],
    conflicts: [],
    approvals: [{
      id: 'approval-2',
      eventId: 'event-2',
      reviewerId: 'admin-user',
      reviewerName: 'Admin User',
      status: 'approved',
      comments: 'Critical security training approved.',
      reviewedAt: '2024-02-20T11:00:00Z'
    }],
    isRecurring: true,
    recurringPattern: 'quarterly',
    tags: ['training', 'security', 'mandatory'],
    createdAt: '2024-02-05T11:00:00Z',
    updatedAt: '2024-02-20T11:00:00Z',
    submittedAt: '2024-02-15T09:00:00Z',
    approvedAt: '2024-02-20T11:00:00Z'
  },

  {
    id: 'event-3',
    eventName: 'Monthly Sales Review',
    description: 'Monthly review of sales performance, pipeline analysis, and target adjustments for the upcoming month.',
    date: '2024-03-25',
    time: '09:00',
    venue: 'Sales Conference Room',
    organizer: 'Sales Lead',
    organizerId: 'sales-lead-1',
    departmentId: 'sales',
    departmentName: 'Sales',
    departments: ['sales', 'marketing', 'finance'],
    duration: 60,
    status: EventStatus.CONFIRMED,
    priority: EventPriority.MEDIUM,
    maxAttendees: 15,
    estimatedAttendees: 12,
    budget: 500,
    resources: ['projector', 'whiteboard', 'wifi'],
    conflicts: [],
    approvals: [{
      id: 'approval-3',
      eventId: 'event-3',
      reviewerId: 'admin-user',
      reviewerName: 'Admin User',
      status: 'approved',
      comments: 'Regular monthly review approved.',
      reviewedAt: '2024-02-25T08:00:00Z'
    }],
    isRecurring: true,
    recurringPattern: 'monthly',
    tags: ['meeting', 'sales', 'review'],
    createdAt: '2024-02-10T08:00:00Z',
    updatedAt: '2024-02-25T08:00:00Z',
    submittedAt: '2024-02-20T10:00:00Z',
    approvedAt: '2024-02-25T08:00:00Z'
  },

  // Pending Events
  {
    id: 'event-4',
    eventName: 'Annual Company Retreat Planning',
    description: 'Planning session for the annual company retreat. Discussing venue options, activities, budget, and logistics.',
    date: '2024-04-10',
    time: '13:00',
    venue: 'Executive Conference Room',
    organizer: 'Sarah Johnson',
    organizerId: 'hr-lead-1',
    departmentId: 'hr',
    departmentName: 'Human Resources',
    departments: ['hr', 'admin', 'finance'],
    duration: 180,
    status: EventStatus.PENDING,
    priority: EventPriority.HIGH,
    maxAttendees: 10,
    estimatedAttendees: 8,
    budget: 15000,
    resources: ['projector', 'catering', 'wifi'],
    conflicts: [],
    approvals: [],
    isRecurring: false,
    tags: ['planning', 'retreat', 'company-wide'],
    createdAt: '2024-03-01T10:00:00Z',
    updatedAt: '2024-03-01T10:00:00Z',
    submittedAt: '2024-03-01T10:00:00Z'
  },

  {
    id: 'event-5',
    eventName: 'Budget Review Meeting',
    description: 'Quarterly budget review and financial planning session. Analysis of Q1 performance and Q2 projections.',
    date: '2024-04-15',
    time: '11:00',
    venue: 'Finance Department Meeting Room',
    organizer: 'Finance Lead',
    organizerId: 'finance-lead-1',
    departmentId: 'finance',
    departmentName: 'Finance',
    departments: ['finance', 'admin', 'operations'],
    duration: 90,
    status: EventStatus.PENDING,
    priority: EventPriority.CRITICAL,
    maxAttendees: 8,
    estimatedAttendees: 6,
    budget: 1000,
    resources: ['projector', 'whiteboard', 'wifi'],
    conflicts: [],
    approvals: [],
    isRecurring: true,
    recurringPattern: 'quarterly',
    tags: ['finance', 'budget', 'review'],
    createdAt: '2024-03-05T09:00:00Z',
    updatedAt: '2024-03-05T09:00:00Z',
    submittedAt: '2024-03-05T09:00:00Z'
  },

  // Draft Events
  {
    id: 'event-6',
    eventName: 'Team Building Workshop',
    description: 'Interactive team building workshop to improve collaboration and communication within the marketing team.',
    date: '2024-04-20',
    time: '10:00',
    venue: 'Training Room B',
    organizer: 'Marketing Lead',
    organizerId: 'marketing-lead',
    departmentId: 'marketing',
    departmentName: 'Marketing',
    departments: ['marketing'],
    duration: 240,
    status: EventStatus.DRAFT,
    priority: EventPriority.MEDIUM,
    maxAttendees: 20,
    estimatedAttendees: 18,
    budget: 3000,
    resources: ['catering', 'wifi'],
    conflicts: [],
    approvals: [],
    isRecurring: false,
    tags: ['team-building', 'workshop', 'training'],
    createdAt: '2024-03-10T14:00:00Z',
    updatedAt: '2024-03-10T14:00:00Z'
  },

  // Events with Conflicts
  {
    id: 'event-7',
    eventName: 'Product Demo Presentation',
    description: 'Demonstration of new product features to potential clients and stakeholders.',
    date: '2024-04-10',
    time: '14:00',
    venue: 'Main Auditorium',
    organizer: 'Marketing Lead',
    organizerId: 'marketing-lead',
    departmentId: 'marketing',
    departmentName: 'Marketing',
    departments: ['marketing', 'sales'],
    duration: 120,
    status: EventStatus.PENDING,
    priority: EventPriority.HIGH,
    maxAttendees: 50,
    estimatedAttendees: 40,
    budget: 4000,
    resources: ['projector', 'microphone', 'speakers', 'catering'],
    conflicts: [{
      id: 'conflict-1',
      type: 'time_overlap',
      conflictingEventId: 'event-4',
      description: 'Overlaps with Annual Company Retreat Planning meeting',
      severity: 'medium'
    }],
    approvals: [],
    isRecurring: false,
    tags: ['presentation', 'demo', 'client'],
    createdAt: '2024-03-08T11:00:00Z',
    updatedAt: '2024-03-08T11:00:00Z',
    submittedAt: '2024-03-08T11:00:00Z'
  },

  // More sample events for different departments
  {
    id: 'event-8',
    eventName: 'Operations Review',
    description: 'Monthly operations review covering logistics, supply chain, and process improvements.',
    date: '2024-03-28',
    time: '15:00',
    venue: 'Operations Center',
    organizer: 'Operations Lead',
    organizerId: 'ops-lead-1',
    departmentId: 'operations',
    departmentName: 'Operations',
    departments: ['operations', 'finance'],
    duration: 75,
    status: EventStatus.CONFIRMED,
    priority: EventPriority.MEDIUM,
    maxAttendees: 12,
    estimatedAttendees: 10,
    budget: 800,
    resources: ['projector', 'whiteboard'],
    conflicts: [],
    approvals: [{
      id: 'approval-8',
      eventId: 'event-8',
      reviewerId: 'admin-user',
      reviewerName: 'Admin User',
      status: 'approved',
      comments: 'Monthly operations review approved.',
      reviewedAt: '2024-02-28T12:00:00Z'
    }],
    isRecurring: true,
    recurringPattern: 'monthly',
    tags: ['operations', 'review', 'monthly'],
    createdAt: '2024-02-15T13:00:00Z',
    updatedAt: '2024-02-28T12:00:00Z',
    submittedAt: '2024-02-25T09:00:00Z',
    approvedAt: '2024-02-28T12:00:00Z'
  }
];

// Function to get events by department
export const getEventsByDepartment = (departmentId: string): EvaEvent[] => {
  return sampleEvents.filter(event => 
    event.departmentId === departmentId || 
    event.departments.includes(departmentId)
  );
};

// Function to get events by status
export const getEventsByStatus = (status: EventStatus): EvaEvent[] => {
  return sampleEvents.filter(event => event.status === status);
};

// Function to get events by user (based on their departments)
export const getEventsByUser = (userDepartments: string[]): EvaEvent[] => {
  return sampleEvents.filter(event => 
    userDepartments.includes(event.departmentId) ||
    event.departments.some(dept => userDepartments.includes(dept))
  );
};

// Function to get upcoming events
export const getUpcomingEvents = (days: number = 30): EvaEvent[] => {
  const now = new Date();
  const futureDate = new Date(now.getTime() + days * 24 * 60 * 60 * 1000);
  
  return sampleEvents.filter(event => {
    const eventDate = new Date(event.date);
    return eventDate >= now && eventDate <= futureDate && event.status === EventStatus.CONFIRMED;
  });
};
