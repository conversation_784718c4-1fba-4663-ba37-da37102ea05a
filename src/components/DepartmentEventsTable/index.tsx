import React, { useState } from 'react';
import { Edit, Trash2, MapPin, Clock } from 'lucide-react';

interface Event {
  id: string;
  name: string;
  date: string;
  time: string;
  venue: string;
  organizer: string;
  status: 'CONFIRMED' | 'PENDING' | 'DRAFT';
  priority: 'HIGH' | 'MEDIUM' | 'LOW';
}

const sampleEvents: Event[] = [
  {
    id: '1',
    name: 'Q1 Marketing Campaign Launch',
    date: 'Mar 15, 2024',
    time: '10:00 AM (1 min)',
    venue: 'Conference Room A, Marketing Floor',
    organizer: 'Marketing Lead',
    status: 'CONFIRMED',
    priority: 'HIGH'
  },
  {
    id: '2',
    name: 'IT Security Training',
    date: 'Mar 20, 2024',
    time: '2:00 PM (90 min)',
    venue: 'Main Auditorium',
    organizer: '<PERSON>',
    status: 'CONFIRMED',
    priority: 'MEDIUM'
  },
  {
    id: '3',
    name: 'Monthly Sales Review',
    date: 'Mar 25, 2024',
    time: '3:00 PM (60 min)',
    venue: 'Sales Conference Room',
    organizer: 'Sales Lead',
    status: 'CONFIRMED',
    priority: 'MEDIUM'
  },
  {
    id: '4',
    name: 'Team Building Workshop',
    date: 'Apr 01, 2024',
    time: '9:00 AM (4 hours)',
    venue: 'Training Center',
    organizer: 'HR Manager',
    status: 'PENDING',
    priority: 'LOW'
  },
  {
    id: '5',
    name: 'Product Launch Event',
    date: 'Apr 10, 2024',
    time: '11:00 AM (2 hours)',
    venue: 'Main Conference Hall',
    organizer: 'Product Manager',
    status: 'DRAFT',
    priority: 'HIGH'
  }
];

const DepartmentEventsTable: React.FC = () => {
  const [currentPage, setCurrentPage] = useState(1);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('All Statuses');
  const itemsPerPage = 10;

  const filteredEvents = sampleEvents.filter(event => {
    const matchesSearch = event.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         event.organizer.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         event.venue.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'All Statuses' || event.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const totalPages = Math.ceil(filteredEvents.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const paginatedEvents = filteredEvents.slice(startIndex, startIndex + itemsPerPage);

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'CONFIRMED':
        return <span className="bg-green-100 text-green-800 px-2 py-1 rounded text-xs font-medium">CONFIRMED</span>;
      case 'PENDING':
        return <span className="bg-yellow-100 text-yellow-800 px-2 py-1 rounded text-xs font-medium">PENDING</span>;
      case 'DRAFT':
        return <span className="bg-gray-100 text-gray-800 px-2 py-1 rounded text-xs font-medium">DRAFT</span>;
      default:
        return <span className="bg-gray-100 text-gray-800 px-2 py-1 rounded text-xs font-medium">{status}</span>;
    }
  };

  const getPriorityBadge = (priority: string) => {
    switch (priority) {
      case 'HIGH':
        return <span className="bg-red-100 text-red-800 px-2 py-1 rounded text-xs font-medium">HIGH</span>;
      case 'MEDIUM':
        return <span className="bg-yellow-100 text-yellow-800 px-2 py-1 rounded text-xs font-medium">MEDIUM</span>;
      case 'LOW':
        return <span className="bg-green-100 text-green-800 px-2 py-1 rounded text-xs font-medium">LOW</span>;
      default:
        return <span className="bg-gray-100 text-gray-800 px-2 py-1 rounded text-xs font-medium">{priority}</span>;
    }
  };

  return (
    <div className="bg-white rounded-lg border border-gray-200">
      <div className="p-4 border-b border-gray-200">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold text-gray-900">Department Events</h3>
        </div>
        
        <div className="flex justify-between items-center">
          <input
            type="text"
            placeholder="Search events..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option>All Statuses</option>
            <option>CONFIRMED</option>
            <option>PENDING</option>
            <option>DRAFT</option>
          </select>
        </div>
      </div>

      <div className="overflow-x-auto">
        <table className="w-full">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Event Name</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date & Time</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Venue</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Organizer</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Priority</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {paginatedEvents.map((event) => (
              <tr key={event.id} className="hover:bg-gray-50">
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm font-medium text-gray-900">{event.name}</div>
                  <div className="text-sm text-gray-500">Launch of the Q1 marketing campaign</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center text-sm text-gray-900">
                    <Clock className="mr-1" size={14} />
                    {event.date}
                  </div>
                  <div className="text-sm text-gray-500">{event.time}</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center text-sm text-gray-900">
                    <MapPin className="mr-1" size={14} />
                    {event.venue}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {event.organizer}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  {getStatusBadge(event.status)}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  {getPriorityBadge(event.priority)}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <div className="flex space-x-2">
                    <button className="text-blue-600 hover:text-blue-900">
                      <Edit size={16} />
                    </button>
                    <button className="text-red-600 hover:text-red-900">
                      <Trash2 size={16} />
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="px-6 py-3 border-t border-gray-200 flex items-center justify-between">
          <div className="text-sm text-gray-700">
            Showing {startIndex + 1} to {Math.min(startIndex + itemsPerPage, filteredEvents.length)} of {filteredEvents.length} results
          </div>
          <div className="flex space-x-1">
            <button
              onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
              disabled={currentPage === 1}
              className="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Previous
            </button>
            {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
              <button
                key={page}
                onClick={() => setCurrentPage(page)}
                className={`px-3 py-1 text-sm border rounded ${
                  currentPage === page
                    ? 'bg-blue-600 text-white border-blue-600'
                    : 'border-gray-300 hover:bg-gray-50'
                }`}
              >
                {page}
              </button>
            ))}
            <button
              onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
              disabled={currentPage === totalPages}
              className="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Next
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default DepartmentEventsTable;
