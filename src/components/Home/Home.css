.homepage {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

/* Header Styles */
.navbar-brand {
  font-weight: 700;
  font-size: 1.5rem;
}

.shop-now-btn {
  background-color: #e67e5b !important;
  border: none !important;
  font-weight: 600;
  transition: all 0.3s ease;
}

.shop-now-btn:hover {
  background-color: #d66b4a !important;
  transform: translateY(-1px);
}

/* Hero Section */
.hero-section {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  min-height: 80vh;
  position: relative;
}

.hero-title {
  color: #2c3e50;
  line-height: 1.2;
  font-weight: 800;
}

.hero-subtitle {
  color: #6c757d;
  line-height: 1.6;
  font-size: 1.1rem;
}

.search-input {
  border-radius: 50px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.search-input .form-control {
  border: none;
  padding: 1rem 1.5rem;
  font-size: 1rem;
}

.search-input .input-group-text {
  border: none;
  padding: 1rem 1.5rem;
}

/* Hero Image Section */
.hero-image-section {
  position: relative;
}

.main-hero-image {
  width: 100%;
  max-width: 500px;
  height: auto;
  object-fit: cover;
}

.rating-badge {
  top: 10%;
  left: -10%;
  z-index: 10;
}

.rating-badge .bg-white {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.stats-cards .stat-card {
  bottom: 20%;
  right: -5%;
  z-index: 10;
  min-width: 180px;
}

.stats-cards .stat-card-2 {
  bottom: 5%;
  left: -15%;
  z-index: 10;
  min-width: 180px;
}

/* Services Section */
.services-section {
  background-color: #ffffff;
}

.service-card {
  transition: all 0.3s ease;
  border: none !important;
}

.service-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1) !important;
}

.service-icon {
  display: flex;
  justify-content: center;
  align-items: center;
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero-title {
    font-size: 2.5rem;
  }
  
  .hero-subtitle {
    font-size: 1rem;
  }
  
  .rating-badge {
    position: static;
    margin-bottom: 1rem;
  }
  
  .stats-cards .stat-card,
  .stats-cards .stat-card-2 {
    position: static;
    margin-top: 1rem;
  }
  
  .hero-image-section {
    margin-top: 2rem;
  }
}

@media (max-width: 576px) {
  .hero-title {
    font-size: 2rem;
  }
  
  .search-input .form-control,
  .search-input .input-group-text {
    padding: 0.75rem 1rem;
  }
}

/* Animation for smooth loading */
.hero-content {
  animation: fadeInUp 0.8s ease-out;
}

.hero-image-section {
  animation: fadeInRight 0.8s ease-out 0.2s both;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #e67e5b;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #d66b4a;
}
