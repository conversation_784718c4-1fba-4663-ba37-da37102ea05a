import React from 'react';
import { Search, Building, Calculator, Settings, Star } from '../ui/icons';
import { Input } from '../ui/input';
import { Card } from '../ui/card';
import Layout from '../Layout';
import './Home.css';

const HomePage: React.FC = () => {
    return (
        <Layout>
            <div className="homepage">

                {/* Hero Section */}
                <section className="hero-section py-12">
                    <div className="max-w-7xl mx-auto px-4">
                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
                            <div className="hero-content">
                                <h1 className="hero-title text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
                                    Event planning made easier for everyone
                                </h1>
                                <p className="hero-subtitle text-gray-600 mb-6 text-lg">
                                    GoPlanMe provides guidance and vendors for a variety of event types
                                    including funerals, birthday parties, family reunions.
                                </p>
                                <div className="search-container mb-6">
                                    <div className="relative">
                                        <Search size={20} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                                        <Input
                                            placeholder="Start Your Search"
                                            className="pl-10 h-12 text-lg"
                                        />
                                    </div>
                                </div>
                            </div>
                            <div className="hero-image-section relative">
                                {/* 5 Star Rating Badge */}
                                <div className="rating-badge absolute top-4 left-4">
                                    <div className="flex items-center bg-white rounded-full px-4 py-2 shadow-lg">
                                        <div className="rating-icon mr-2">
                                            <Star size={20} fill="#ffc107" color="#ffc107" />
                                        </div>
                                        <div>
                                            <div className="font-bold">5 Star</div>
                                            <small className="text-gray-500">Based on 420 reviews</small>
                                        </div>
                                    </div>
                                </div>

                                {/* Main Hero Image */}
                                <div className="hero-image-container">
                                    <img
                                        src="/api/placeholder/500/400"
                                        alt="Event planning professional"
                                        className="w-full rounded-2xl shadow-lg main-hero-image"
                                    />
                                </div>

                                {/* Stats Cards */}
                                <div className="stats-cards">
                                    <div className="stat-card bg-white rounded-xl p-3 shadow-lg absolute bottom-4 left-4">
                                        <div className="flex items-center">
                                            <div className="stat-icon mr-2">
                                                <div className="rounded-full bg-red-500 flex items-center justify-center w-10 h-10">
                                                    <span className="text-white font-bold text-sm">72%</span>
                                                </div>
                                            </div>
                                            <div>
                                                <div className="font-bold">72%</div>
                                                <small className="text-gray-500">Customer Target</small>
                                            </div>
                                        </div>
                                    </div>

                                    <div className="stat-card-2 bg-white rounded-xl p-3 shadow-lg absolute bottom-4 right-4">
                                        <div className="flex items-center">
                                            <div className="stat-icon mr-2">
                                                <div className="rounded-full bg-yellow-500 flex items-center justify-center w-10 h-10">
                                                    <span className="text-white font-bold text-sm">96%</span>
                                                </div>
                                            </div>
                                            <div>
                                                <div className="font-bold">96%</div>
                                                <small className="text-gray-500">Sales Target</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                {/* Our Services Section */}
                <section className="services-section py-12">
                    <div className="max-w-7xl mx-auto px-4">
                        <h2 className="text-center mb-12 text-4xl font-bold text-gray-900">Our Services</h2>
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                            <Card className="service-card h-full border-0 shadow-sm rounded-2xl p-6" style={{ backgroundColor: '#e8e4ff' }}>
                                <div className="text-center">
                                    <div className="service-icon mb-6">
                                        <Building size={48} className="text-brand-primary mx-auto" />
                                    </div>
                                    <h3 className="text-xl font-bold mb-4">Steel Fabrication</h3>
                                    <p className="text-gray-600">
                                        Steel fabrication involves techniques like machining, welding, cutting, drilling,
                                        coating to turn the material.
                                    </p>
                                </div>
                            </Card>
                            <Card className="service-card h-full border-0 shadow-sm rounded-2xl p-6" style={{ backgroundColor: '#e8f5e8' }}>
                                <div className="text-center">
                                    <div className="service-icon mb-6">
                                        <Calculator size={48} className="text-green-600 mx-auto" />
                                    </div>
                                    <h3 className="text-xl font-bold mb-4">Take-off and Estimation</h3>
                                    <p className="text-gray-600">
                                        Once you provide us with the engineering plans, we start our take off and estimation
                                        process.
                                    </p>
                                </div>
                            </Card>
                            <Card className="service-card h-full border-0 shadow-sm rounded-2xl p-6" style={{ backgroundColor: '#ffe8e8' }}>
                                <div className="text-center">
                                    <div className="service-icon mb-6">
                                        <Settings size={48} className="text-red-600 mx-auto" />
                                    </div>
                                    <h3 className="text-xl font-bold mb-4">Installation</h3>
                                    <p className="text-gray-600">
                                        Steel structure installation composed of steel beams, columns, trusses and other
                                        components. Our team make sure.
                                    </p>
                                </div>
                            </Card>
                        </div>
                    </div>
                </section>

            </div>
        </Layout>
    );
};

export default HomePage;
