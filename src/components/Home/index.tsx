import React from 'react';
import { Container, Row, Col, Card, Form, InputGroup } from 'react-bootstrap';
import { Search, Building, Calculator, Settings, Star } from 'lucide-react';
import Layout from '../Layout';
import './Home.css';

const HomePage: React.FC = () => {
    return (
        <Layout>
            <div className="homepage">

                {/* Hero Section */}
                <section className="hero-section py-5">
                    <Container>
                        <Row className="align-items-center">
                            <Col lg={6} className="hero-content">
                                <h1 className="hero-title display-3 fw-bold text-dark mb-4">
                                    Event planning made easier for everyone
                                </h1>
                                <p className="hero-subtitle text-muted mb-4 fs-5">
                                    GoPlanMe provides guidance and vendors for a variety of event types
                                    including funerals, birthday parties, family reunions.
                                </p>
                                <div className="search-container mb-4">
                                    <InputGroup size="lg" className="search-input">
                                        <InputGroup.Text className="bg-white border-end-0">
                                            <Search size={20} className="text-muted" />
                                        </InputGroup.Text>
                                        <Form.Control
                                            placeholder="Start Your Search"
                                            className="border-start-0 ps-0"
                                            style={{ boxShadow: 'none' }}
                                        />
                                    </InputGroup>
                                </div>
                            </Col>
                            <Col lg={6} className="hero-image-section position-relative">
                                {/* 5 Star Rating Badge */}
                                <div className="rating-badge position-absolute">
                                    <div className="d-flex align-items-center bg-white rounded-pill px-3 py-2 shadow">
                                        <div className="rating-icon me-2">
                                            <Star size={20} fill="#ffc107" color="#ffc107" />
                                        </div>
                                        <div>
                                            <div className="fw-bold">5 Star</div>
                                            <small className="text-muted">Based on 420 reviews</small>
                                        </div>
                                    </div>
                                </div>

                                {/* Main Hero Image */}
                                <div className="hero-image-container">
                                    <img
                                        src="/api/placeholder/500/400"
                                        alt="Event planning professional"
                                        className="img-fluid rounded-4 shadow-lg main-hero-image"
                                    />
                                </div>

                                {/* Stats Cards */}
                                <div className="stats-cards">
                                    <div className="stat-card bg-white rounded-3 p-3 shadow position-absolute">
                                        <div className="d-flex align-items-center">
                                            <div className="stat-icon me-2">
                                                <div className="rounded-circle bg-danger d-flex align-items-center justify-content-center" style={{ width: '40px', height: '40px' }}>
                                                    <span className="text-white fw-bold">72%</span>
                                                </div>
                                            </div>
                                            <div>
                                                <div className="fw-bold">72%</div>
                                                <small className="text-muted">Customer Target</small>
                                            </div>
                                        </div>
                                    </div>

                                    <div className="stat-card-2 bg-white rounded-3 p-3 shadow position-absolute">
                                        <div className="d-flex align-items-center">
                                            <div className="stat-icon me-2">
                                                <div className="rounded-circle bg-warning d-flex align-items-center justify-content-center" style={{ width: '40px', height: '40px' }}>
                                                    <span className="text-white fw-bold">96%</span>
                                                </div>
                                            </div>
                                            <div>
                                                <div className="fw-bold">96%</div>
                                                <small className="text-muted">Sales Target</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </Col>
                        </Row>
                    </Container>
                </section>

                {/* Our Services Section */}
                <section className="services-section py-5">
                    <Container>
                        <h2 className="text-center mb-5 display-5 fw-bold text-dark">Our Services</h2>
                        <Row className="g-4">
                            <Col lg={4} md={6}>
                                <Card className="service-card h-100 border-0 shadow-sm rounded-4 p-4" style={{ backgroundColor: '#e8e4ff' }}>
                                    <Card.Body className="text-center">
                                        <div className="service-icon mb-4">
                                            <Building size={48} className="text-primary" />
                                        </div>
                                        <Card.Title className="h4 fw-bold mb-3">Steel Fabrication</Card.Title>
                                        <Card.Text className="text-muted">
                                            Steel fabrication involves techniques like machining, welding, cutting, drilling,
                                            coating to turn the material.
                                        </Card.Text>
                                    </Card.Body>
                                </Card>
                            </Col>
                            <Col lg={4} md={6}>
                                <Card className="service-card h-100 border-0 shadow-sm rounded-4 p-4" style={{ backgroundColor: '#e8f5e8' }}>
                                    <Card.Body className="text-center">
                                        <div className="service-icon mb-4">
                                            <Calculator size={48} className="text-success" />
                                        </div>
                                        <Card.Title className="h4 fw-bold mb-3">Take-off and Estimation</Card.Title>
                                        <Card.Text className="text-muted">
                                            Once you provide us with the engineering plans, we start our take off and estimation
                                            process.
                                        </Card.Text>
                                    </Card.Body>
                                </Card>
                            </Col>
                            <Col lg={4} md={6}>
                                <Card className="service-card h-100 border-0 shadow-sm rounded-4 p-4" style={{ backgroundColor: '#ffe8e8' }}>
                                    <Card.Body className="text-center">
                                        <div className="service-icon mb-4">
                                            <Settings size={48} className="text-danger" />
                                        </div>
                                        <Card.Title className="h4 fw-bold mb-3">Installation</Card.Title>
                                        <Card.Text className="text-muted">
                                            Steel structure installation composed of steel beams, columns, trusses and other
                                            components. Our team make sure.
                                        </Card.Text>
                                    </Card.Body>
                                </Card>
                            </Col>
                        </Row>
                    </Container>
                </section>

            </div>
        </Layout>
    );
};

export default HomePage;
