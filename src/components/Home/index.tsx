import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON> } from 'react-bootstrap';

const HomePage: React.FC = () => {
    return (
        <>
            {/* Hero Section */}
            <section className="bg-light py-5">
                <Container>
                    <Row className="align-items-center">
                        <Col md={6}>
                            <h1 className="display-4">Simplify Event Management</h1>
                            <p className="lead">
                                Manage all your events effortlessly with our comprehensive platform. Plan, schedule, and track your events with just a few clicks.
                            </p>
                            <Button variant="primary" size="lg" href="/signup">
                                Get Started for Free
                            </Button>
                        </Col>
                        <Col md={6}>
                            <img
                                src="/images/event-management.svg"
                                alt="Event Management"
                                className="img-fluid"
                            />
                        </Col>
                    </Row>
                </Container>
            </section>

            {/* Features Section */}
            <section className="py-5">
                <Container>
                    <h2 className="text-center mb-4">Features to Supercharge Your Events</h2>
                    <Row>
                        <Col md={4}>
                            <Card className="mb-4">
                                <Card.Body>
                                    <Card.Title>All-in-One Calendar</Card.Title>
                                    <Card.Text>
                                        Integrate with Google Calendar and iCal to manage your schedule in one place.
                                    </Card.Text>
                                </Card.Body>
                            </Card>
                        </Col>
                        <Col md={4}>
                            <Card className="mb-4">
                                <Card.Body>
                                    <Card.Title>Custom Event Templates</Card.Title>
                                    <Card.Text>
                                        Create and reuse event templates to streamline your workflow.
                                    </Card.Text>
                                </Card.Body>
                            </Card>
                        </Col>
                        <Col md={4}>
                            <Card className="mb-4">
                                <Card.Body>
                                    <Card.Title>Real-Time Notifications</Card.Title>
                                    <Card.Text>
                                        Receive instant notifications for any updates or changes to your events.
                                    </Card.Text>
                                </Card.Body>
                            </Card>
                        </Col>
                    </Row>
                </Container>
            </section>

            {/* Testimonials Section */}
            <section className="bg-light py-5">
                <Container>
                    <h2 className="text-center mb-4">What Our Users Say</h2>
                    <Row>
                        <Col md={4}>
                            <Card className="mb-4">
                                <Card.Body>
                                    <Card.Text>
                                        "This platform has transformed how we manage our team meetings and company events."
                                    </Card.Text>
                                    <Card.Subtitle className="text-muted mt-2">- Alex J.</Card.Subtitle>
                                </Card.Body>
                            </Card>
                        </Col>
                        <Col md={4}>
                            <Card className="mb-4">
                                <Card.Body>
                                    <Card.Text>
                                        "I love how easy it is to keep track of everything in one place!"
                                    </Card.Text>
                                    <Card.Subtitle className="text-muted mt-2">- Maria L.</Card.Subtitle>
                                </Card.Body>
                            </Card>
                        </Col>
                        <Col md={4}>
                            <Card className="mb-4">
                                <Card.Body>
                                    <Card.Text>
                                        "Our productivity has increased ever since we started using this tool."
                                    </Card.Text>
                                    <Card.Subtitle className="text-muted mt-2">- Chris K.</Card.Subtitle>
                                </Card.Body>
                            </Card>
                        </Col>
                    </Row>
                </Container>
            </section>

            {/* CTA Section */}
            <section className="py-5 text-center">
                <Container>
                    <h2>Ready to Get Started?</h2>
                    <p className="lead">
                        Sign up now and take control of your event management process.
                    </p>
                    <Button variant="primary" size="lg" href="/signup">
                        Start Your Free Trial
                    </Button>
                </Container>
            </section>
        </>
    );
};

export default HomePage;
