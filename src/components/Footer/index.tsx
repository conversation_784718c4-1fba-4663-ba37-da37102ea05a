
import WithKalendaBrand from '../Brand';

const Footer = () => {
    const footerLinks = [
        { href: '/about', label: 'About Us' },
        { href: '/contact', label: 'Contact' },
        { href: '/privacy', label: 'Privacy Policy' },
        { href: '/terms', label: 'Terms of Service' },
    ];

    return (
        <footer className="bg-gray-900 text-white py-8 mt-auto">
            <div className="wk-container">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <div>
                        <div className="mb-4">
                            <WithKalendaBrand />
                        </div>
                        <p className="text-gray-300 leading-relaxed">
                            Bringing teams, companies, religious organizations, and charities together through
                            seamless collaborative event planning and calendar management.
                        </p>
                    </div>
                    <div className="md:text-right">
                        <nav className="flex flex-wrap justify-start md:justify-end gap-6 mb-4">
                            {footerLinks.map((link) => (
                                <a
                                    key={link.href}
                                    href={link.href}
                                    className="text-white hover:text-gray-300 transition-colors"
                                >
                                    {link.label}
                                </a>
                            ))}
                        </nav>
                        <p className="text-gray-400 text-sm">
                            &copy; 2024 WithKalenda. All rights reserved.
                        </p>
                    </div>
                </div>
                <div className="mt-8 pt-4 border-t border-gray-800 text-center">
                    <small className="text-gray-400">
                        WithKalenda - a product of Soft Creative &copy; {new Date().getFullYear()}
                    </small>
                </div>
            </div>
        </footer>
    );
};

export default Footer;
