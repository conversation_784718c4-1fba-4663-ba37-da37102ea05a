import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
// Jest is used instead of vitest
import Pagination from '../index';

describe('Pagination', () => {
  const mockOnPageChange = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  const defaultProps = {
    currentPage: 1,
    totalPages: 10,
    onPageChange: mockOnPageChange,
    itemsPerPage: 10,
    totalItems: 100
  };

  it('renders pagination component with correct info', () => {
    render(<Pagination {...defaultProps} />);
    
    expect(screen.getByText('Showing 1 to 10 of 100 items')).toBeInTheDocument();
  });

  it('renders pagination buttons', () => {
    render(<Pagination {...defaultProps} />);
    
    expect(screen.getByRole('button', { name: /first page/i })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /previous page/i })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /next page/i })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /last page/i })).toBeInTheDocument();
  });

  it('displays current page number', () => {
    render(<Pagination {...defaultProps} currentPage={5} />);
    
    expect(screen.getByText('5')).toBeInTheDocument();
  });

  it('calls onPageChange when page number is clicked', () => {
    render(<Pagination {...defaultProps} />);
    
    const pageButton = screen.getByText('2');
    fireEvent.click(pageButton);
    
    expect(mockOnPageChange).toHaveBeenCalledWith(2);
  });

  it('calls onPageChange when next button is clicked', () => {
    render(<Pagination {...defaultProps} />);
    
    const nextButton = screen.getByRole('button', { name: /next page/i });
    fireEvent.click(nextButton);
    
    expect(mockOnPageChange).toHaveBeenCalledWith(2);
  });

  it('calls onPageChange when previous button is clicked', () => {
    render(<Pagination {...defaultProps} currentPage={5} />);
    
    const prevButton = screen.getByRole('button', { name: /previous page/i });
    fireEvent.click(prevButton);
    
    expect(mockOnPageChange).toHaveBeenCalledWith(4);
  });

  it('calls onPageChange when first page button is clicked', () => {
    render(<Pagination {...defaultProps} currentPage={5} />);
    
    const firstButton = screen.getByRole('button', { name: /first page/i });
    fireEvent.click(firstButton);
    
    expect(mockOnPageChange).toHaveBeenCalledWith(1);
  });

  it('calls onPageChange when last page button is clicked', () => {
    render(<Pagination {...defaultProps} currentPage={5} />);
    
    const lastButton = screen.getByRole('button', { name: /last page/i });
    fireEvent.click(lastButton);
    
    expect(mockOnPageChange).toHaveBeenCalledWith(10);
  });

  it('disables first and previous buttons on first page', () => {
    render(<Pagination {...defaultProps} currentPage={1} />);
    
    const firstButton = screen.getByRole('button', { name: /first page/i });
    const prevButton = screen.getByRole('button', { name: /previous page/i });
    
    expect(firstButton).toBeDisabled();
    expect(prevButton).toBeDisabled();
  });

  it('disables next and last buttons on last page', () => {
    render(<Pagination {...defaultProps} currentPage={10} />);
    
    const nextButton = screen.getByRole('button', { name: /next page/i });
    const lastButton = screen.getByRole('button', { name: /last page/i });
    
    expect(nextButton).toBeDisabled();
    expect(lastButton).toBeDisabled();
  });

  it('shows ellipsis for large page ranges', () => {
    render(<Pagination {...defaultProps} currentPage={5} totalPages={20} />);
    
    const ellipsis = screen.getAllByText('...');
    expect(ellipsis.length).toBeGreaterThan(0);
  });

  it('shows correct item range for middle pages', () => {
    render(<Pagination {...defaultProps} currentPage={5} />);
    
    expect(screen.getByText('Showing 41 to 50 of 100 items')).toBeInTheDocument();
  });

  it('shows correct item range for last page with partial items', () => {
    render(<Pagination {...defaultProps} currentPage={10} totalItems={95} />);
    
    expect(screen.getByText('Showing 91 to 95 of 95 items')).toBeInTheDocument();
  });

  it('hides info when showInfo is false', () => {
    render(<Pagination {...defaultProps} showInfo={false} />);
    
    expect(screen.queryByText('Showing 1 to 10 of 100 items')).not.toBeInTheDocument();
  });

  it('returns null when totalPages is 0', () => {
    const { container } = render(<Pagination {...defaultProps} totalPages={0} totalItems={0} />);
    
    expect(container.firstChild).toBeNull();
  });

  it('shows single item correctly', () => {
    render(<Pagination {...defaultProps} totalPages={1} totalItems={1} itemsPerPage={10} />);
    
    expect(screen.getByText('Showing 1 item')).toBeInTheDocument();
  });

  it('shows multiple items correctly for single page', () => {
    render(<Pagination {...defaultProps} totalPages={1} totalItems={5} itemsPerPage={10} />);
    
    expect(screen.getByText('Showing 5 items')).toBeInTheDocument();
  });

  it('highlights current page button', () => {
    render(<Pagination {...defaultProps} currentPage={3} />);
    
    const currentPageButton = screen.getByText('3');
    expect(currentPageButton.closest('button')).toHaveClass('bg-primary');
  });

  it('shows correct page numbers around current page', () => {
    render(<Pagination {...defaultProps} currentPage={5} totalPages={10} />);
    
    // Should show pages around current page
    expect(screen.getByText('3')).toBeInTheDocument();
    expect(screen.getByText('4')).toBeInTheDocument();
    expect(screen.getByText('5')).toBeInTheDocument();
    expect(screen.getByText('6')).toBeInTheDocument();
    expect(screen.getByText('7')).toBeInTheDocument();
  });

  it('shows first and last pages when not adjacent to current page', () => {
    render(<Pagination {...defaultProps} currentPage={10} totalPages={20} />);
    
    expect(screen.getByText('1')).toBeInTheDocument();
    expect(screen.getByText('20')).toBeInTheDocument();
  });

  it('handles edge case with 2 total pages', () => {
    render(<Pagination {...defaultProps} totalPages={2} />);
    
    expect(screen.getByText('1')).toBeInTheDocument();
    expect(screen.getByText('2')).toBeInTheDocument();
    expect(screen.queryByText('...')).not.toBeInTheDocument();
  });

  it('handles edge case with 3 total pages', () => {
    render(<Pagination {...defaultProps} totalPages={3} currentPage={2} />);
    
    expect(screen.getByText('1')).toBeInTheDocument();
    expect(screen.getByText('2')).toBeInTheDocument();
    expect(screen.getByText('3')).toBeInTheDocument();
    expect(screen.queryByText('...')).not.toBeInTheDocument();
  });

  it('applies correct CSS classes', () => {
    render(<Pagination {...defaultProps} />);
    
    const container = screen.getByText('Showing 1 to 10 of 100 items').closest('div');
    expect(container).toHaveClass('flex', 'flex-col', 'md:flex-row', 'justify-between', 'items-center');
  });

  it('handles very large page numbers', () => {
    render(<Pagination {...defaultProps} currentPage={500} totalPages={1000} />);
    
    expect(screen.getByText('500')).toBeInTheDocument();
    expect(screen.getByText('1')).toBeInTheDocument();
    expect(screen.getByText('1000')).toBeInTheDocument();
  });

  it('calculates visible pages correctly for start of range', () => {
    render(<Pagination {...defaultProps} currentPage={1} totalPages={20} />);
    
    expect(screen.getByText('1')).toBeInTheDocument();
    expect(screen.getByText('2')).toBeInTheDocument();
    expect(screen.getByText('3')).toBeInTheDocument();
  });

  it('calculates visible pages correctly for end of range', () => {
    render(<Pagination {...defaultProps} currentPage={20} totalPages={20} />);
    
    expect(screen.getByText('18')).toBeInTheDocument();
    expect(screen.getByText('19')).toBeInTheDocument();
    expect(screen.getByText('20')).toBeInTheDocument();
  });
});
