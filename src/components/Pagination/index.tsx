import React from 'react';
import { Pagination as BootstrapPagination } from 'react-bootstrap';
import { ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight } from 'lucide-react';

interface PaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  itemsPerPage: number;
  totalItems: number;
  showInfo?: boolean;
  size?: 'sm' | 'lg';
}

const Pagination: React.FC<PaginationProps> = ({
  currentPage,
  totalPages,
  onPageChange,
  itemsPerPage,
  totalItems,
  showInfo = true,
  size
}) => {
  const startItem = (currentPage - 1) * itemsPerPage + 1;
  const endItem = Math.min(currentPage * itemsPerPage, totalItems);

  const getVisiblePages = () => {
    const delta = 2;
    const range = [];
    const rangeWithDots = [];

    for (let i = Math.max(2, currentPage - delta); i <= Math.min(totalPages - 1, currentPage + delta); i++) {
      range.push(i);
    }

    if (currentPage - delta > 2) {
      rangeWithDots.push(1, '...');
    } else {
      rangeWithDots.push(1);
    }

    rangeWithDots.push(...range);

    if (currentPage + delta < totalPages - 1) {
      rangeWithDots.push('...', totalPages);
    } else {
      rangeWithDots.push(totalPages);
    }

    return rangeWithDots;
  };

  if (totalPages <= 1) {
    return showInfo ? (
      <div className="d-flex justify-content-between align-items-center">
        <span className="text-muted">
          Showing {totalItems} {totalItems === 1 ? 'item' : 'items'}
        </span>
      </div>
    ) : null;
  }

  const visiblePages = getVisiblePages();

  return (
    <div className="d-flex justify-content-between align-items-center flex-wrap">
      {showInfo && (
        <span className="text-muted mb-2 mb-md-0">
          Showing {startItem} to {endItem} of {totalItems} items
        </span>
      )}
      
      <BootstrapPagination size={size} className="mb-0">
        {/* First Page */}
        <BootstrapPagination.Item
          onClick={() => onPageChange(1)}
          disabled={currentPage === 1}
          style={{ 
            backgroundColor: currentPage === 1 ? '#f8f9fa' : 'white',
            borderColor: '#3174ad',
            color: currentPage === 1 ? '#6c757d' : '#3174ad'
          }}
        >
          <ChevronsLeft size={16} />
        </BootstrapPagination.Item>

        {/* Previous Page */}
        <BootstrapPagination.Prev
          onClick={() => onPageChange(currentPage - 1)}
          disabled={currentPage === 1}
          style={{ 
            backgroundColor: currentPage === 1 ? '#f8f9fa' : 'white',
            borderColor: '#3174ad',
            color: currentPage === 1 ? '#6c757d' : '#3174ad'
          }}
        >
          <ChevronLeft size={16} />
        </BootstrapPagination.Prev>

        {/* Page Numbers */}
        {visiblePages.map((page, index) => {
          if (page === '...') {
            return (
              <BootstrapPagination.Ellipsis 
                key={`ellipsis-${index}`}
                style={{ borderColor: '#3174ad', color: '#3174ad' }}
              />
            );
          }

          const pageNumber = page as number;
          const isActive = pageNumber === currentPage;

          return (
            <BootstrapPagination.Item
              key={pageNumber}
              active={isActive}
              onClick={() => onPageChange(pageNumber)}
              style={{
                backgroundColor: isActive ? '#3174ad' : 'white',
                borderColor: '#3174ad',
                color: isActive ? 'white' : '#3174ad'
              }}
            >
              {pageNumber}
            </BootstrapPagination.Item>
          );
        })}

        {/* Next Page */}
        <BootstrapPagination.Next
          onClick={() => onPageChange(currentPage + 1)}
          disabled={currentPage === totalPages}
          style={{ 
            backgroundColor: currentPage === totalPages ? '#f8f9fa' : 'white',
            borderColor: '#3174ad',
            color: currentPage === totalPages ? '#6c757d' : '#3174ad'
          }}
        >
          <ChevronRight size={16} />
        </BootstrapPagination.Next>

        {/* Last Page */}
        <BootstrapPagination.Item
          onClick={() => onPageChange(totalPages)}
          disabled={currentPage === totalPages}
          style={{ 
            backgroundColor: currentPage === totalPages ? '#f8f9fa' : 'white',
            borderColor: '#3174ad',
            color: currentPage === totalPages ? '#6c757d' : '#3174ad'
          }}
        >
          <ChevronsRight size={16} />
        </BootstrapPagination.Item>
      </BootstrapPagination>
    </div>
  );
};

export default Pagination;
