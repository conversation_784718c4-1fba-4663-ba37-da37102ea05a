import React, { useState, useMemo } from 'react';
import { <PERSON><PERSON>, Container, Form, Row, Col, Alert, Badge } from 'react-bootstrap';
import { Calendar as CalendarIcon, Clock, CheckCircle, Edit } from 'lucide-react';
import { Calendar, momentLocalizer } from 'react-big-calendar';

import moment from 'moment';
import 'react-big-calendar/lib/css/react-big-calendar.css';
import { useEvents } from '../../hooks/useEvents';
import { EvaEvent, EventStatus, EventPriority } from '../../types/event';
import { useAuth } from '../../contexts/AuthContext';
import { getFilteredEvents } from '../../utils/permissions';
import EventsTable from '../Planner/EventsTable';
import EditEventModal from '../Planner/EditEventModal';
import CalendarExport from '../CalendarExport';
import { emptyEvent } from '../Planner';
import { createDateFromStrings, addMinutesToDate } from '../../utils/dateUtils';

const localizer = momentLocalizer(moment);

const DepartmentEventView: React.FC = () => {
    const { user, permissions } = useAuth();
    const { events, createEvent, updateEvent, deleteEvent } = useEvents();

    const [showEditModal, setShowEditModal] = useState(false);
    const [editEventId, setEditEventId] = useState<number | string | null>(null);
    const [eventDetails, setEventDetails] = useState<Omit<EvaEvent, 'id'>>(emptyEvent);
    const [showExportModal, setShowExportModal] = useState(false);
    const [currentDate, setCurrentDate] = useState(new Date());
    const [currentView, setCurrentView] = useState<'month' | 'week' | 'day' | 'agenda'>('month');

    // Filter events to show only user's department events
    const departmentEvents = useMemo(() => {
        if (!user?.departmentId) return [];

        return events.filter(event => {
            // Show events from user's primary department
            if (event.departmentId === user.departmentId) return true;

            // Show events from user's additional departments
            if (user.additionalDepartments?.includes(event.departmentId)) return true;

            // Show events that involve user's departments
            if (user.departmentId && event.departments.includes(user.departmentId)) return true;
            if (user.additionalDepartments?.some(dept => event.departments.includes(dept))) return true;

            return false;
        });
    }, [events, user]);

    // Convert events for calendar display
    const calendarEvents = useMemo(() => {
        return departmentEvents.map(event => ({
            ...event,
            start: createDateFromStrings(event.date, event.time),
            end: addMinutesToDate(createDateFromStrings(event.date, event.time), event.duration),
            title: event.eventName,
        }));
    }, [departmentEvents]);

    // Handle creating a new event
    const handleCreateEvent = () => {
        if (!permissions.canCreateEvent) {
            alert('You do not have permission to create events.');
            return;
        }

        const newEvent = {
            ...emptyEvent,
            organizer: user ? `${user.firstName} ${user.lastName}` : '',
            organizerId: user?.id?.toString() || '',
            departmentId: user?.departmentId || '',
            departmentName: user?.department?.name || '',
        };

        setEventDetails(newEvent);
        setEditEventId(null);
        setShowEditModal(true);
    };

    // Handle selecting a time slot on the calendar
    const handleSelectSlot = (slotInfo: any) => {
        if (!permissions.canCreateEvent) {
            alert('You do not have permission to create events.');
            return;
        }

        const startDate = new Date(slotInfo.start);
        const endDate = new Date(slotInfo.end);
        const duration = Math.round((endDate.getTime() - startDate.getTime()) / (1000 * 60));

        const newEvent = {
            ...emptyEvent,
            date: startDate.toISOString().split('T')[0],
            time: startDate.toTimeString().slice(0, 5),
            duration: duration > 0 ? duration : 60,
            organizer: user ? `${user.firstName} ${user.lastName}` : '',
            organizerId: user?.id?.toString() || '',
            departmentId: user?.departmentId || '',
            departmentName: user?.department?.name || '',
        };

        setEventDetails(newEvent);
        setEditEventId(null);
        setShowEditModal(true);
    };

    // Handle editing an event
    const handleEditEvent = (event: EvaEvent) => {
        if (!permissions.canEditEvent(event)) {
            alert('You do not have permission to edit this event.');
            return;
        }

        const { id, start, end, title, ...eventWithoutId } = event;
        setEventDetails(eventWithoutId);
        setEditEventId(event.id);
        setShowEditModal(true);
    };

    // Handle event deletion
    const handleDeleteEvent = async (id: number | string) => {
        const event = departmentEvents.find(e => e.id === id);
        if (!event) return;

        if (!permissions.canDeleteEvent(event)) {
            alert('You do not have permission to delete this event.');
            return;
        }

        if (window.confirm('Are you sure you want to delete this event?')) {
            try {
                await deleteEvent.mutateAsync(id);
            } catch (error) {
                console.error('Error deleting event:', error);
            }
        }
    };

    // Handle form submission
    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        try {
            if (editEventId) {
                await updateEvent.mutateAsync({ id: editEventId, ...eventDetails });
            } else {
                await createEvent.mutateAsync(eventDetails);
            }
            setShowEditModal(false);
        } catch (error) {
            console.error('Error saving event:', error);
        }
    };

    // Handle form field changes
    const handleChange = (name: string, value: any) => {
        setEventDetails(prev => ({ ...prev, [name]: value }));
    };

    // Style events based on their status and priority
    const eventStyleGetter = (event: any) => {
        let backgroundColor = '#3174ad';
        let borderColor = '#3174ad';

        switch (event.status) {
            case EventStatus.DRAFT:
                backgroundColor = '#6c757d';
                borderColor = '#6c757d';
                break;
            case EventStatus.PENDING:
                backgroundColor = '#ffc107';
                borderColor = '#ffc107';
                break;
            case EventStatus.CONFIRMED:
                backgroundColor = '#198754';
                borderColor = '#198754';
                break;
            case EventStatus.REJECTED:
                backgroundColor = '#dc3545';
                borderColor = '#dc3545';
                break;
            default:
                backgroundColor = '#3174ad';
                borderColor = '#3174ad';
        }

        if (event.priority === EventPriority.HIGH) {
            borderColor = '#fd7e14';
        } else if (event.priority === EventPriority.CRITICAL) {
            borderColor = '#dc3545';
        }

        return {
            style: {
                backgroundColor,
                borderColor,
                borderWidth: '2px',
                borderStyle: 'solid',
                color: 'white',
                borderRadius: '4px',
                opacity: event.status === EventStatus.REJECTED ? 0.6 : 1
            }
        };
    };

    const confirmedEvents = departmentEvents.filter(e => e.status === EventStatus.CONFIRMED);
    const pendingEvents = departmentEvents.filter(e => e.status === EventStatus.PENDING);
    const draftEvents = departmentEvents.filter(e => e.status === EventStatus.DRAFT);

    return (
        <Container className="py-4">
            <div className="d-flex justify-content-between align-items-center mb-4 eva-fade-in">
                <div>
                    <h1 className="mb-1 fw-bold" style={{ color: '#3174ad' }}>
                        <CalendarIcon className="me-2" size={28} />
                        Department Events
                    </h1>
                    <p className="text-muted mb-0">
                        Events for <span className="fw-semibold">{user?.department?.name || 'your department'}</span>
                    </p>
                </div>
                <div className="d-flex gap-2">
                    {permissions.canExportCalendar && (
                        <Button
                            variant="outline-secondary"
                            onClick={() => setShowExportModal(true)}
                            className="d-flex align-items-center"
                            style={{ borderColor: '#3174ad', color: '#3174ad' }}
                        >
                            <CalendarIcon className="me-2" size={16} />
                            Export Calendar
                        </Button>
                    )}
                </div>
            </div>

            {/* Event Statistics */}
            <Row className="mb-4 eva-slide-in">
                <Col md={3} className="mb-3">
                    <div className="eva-card text-center p-4 h-100">
                        <div className="mb-2">
                            <CheckCircle size={32} style={{ color: '#198754' }} />
                        </div>
                        <h3 className="fw-bold mb-1" style={{ color: '#198754' }}>{confirmedEvents.length}</h3>
                        <p className="mb-0 text-muted">Confirmed Events</p>
                    </div>
                </Col>
                <Col md={3} className="mb-3">
                    <div className="eva-card text-center p-4 h-100">
                        <div className="mb-2">
                            <Clock size={32} style={{ color: '#ffc107' }} />
                        </div>
                        <h3 className="fw-bold mb-1" style={{ color: '#ffc107' }}>{pendingEvents.length}</h3>
                        <p className="mb-0 text-muted">Pending Approval</p>
                    </div>
                </Col>
                <Col md={3} className="mb-3">
                    <div className="eva-card text-center p-4 h-100">
                        <div className="mb-2">
                            <Edit size={32} style={{ color: '#6c757d' }} />
                        </div>
                        <h3 className="fw-bold mb-1" style={{ color: '#6c757d' }}>{draftEvents.length}</h3>
                        <p className="mb-0 text-muted">Draft Events</p>
                    </div>
                </Col>
                <Col md={3} className="mb-3">
                    <div className="eva-card text-center p-4 h-100">
                        <div className="mb-2">
                            <CalendarIcon size={32} style={{ color: '#3174ad' }} />
                        </div>
                        <h3 className="fw-bold mb-1" style={{ color: '#3174ad' }}>{departmentEvents.length}</h3>
                        <p className="mb-0 text-muted">Total Events</p>
                    </div>
                </Col>
            </Row>

            {/* Create Event Button */}
            {permissions.canCreateEvent && (
                <div className="mb-4">
                    <Button
                        onClick={handleCreateEvent}
                        size="lg"
                        className="d-flex align-items-center eva-fade-in"
                        style={{ backgroundColor: '#ff6b35', borderColor: '#ff6b35', color: 'white' }}
                    >
                        <CalendarIcon className="me-2" size={20} />
                        Create New Event
                    </Button>
                </div>
            )}

            {/* Calendar View */}
            <div className="mb-4" style={{ height: 600 }}>
                <Calendar
                    localizer={localizer}
                    events={calendarEvents}
                    startAccessor="start"
                    endAccessor="end"
                    style={{ height: '100%' }}
                    onSelectEvent={(event: any) => handleEditEvent(event as unknown as EvaEvent)}
                    onSelectSlot={handleSelectSlot}
                    selectable={permissions.canCreateEvent}
                    popup
                    views={['month', 'week', 'day', 'agenda']}
                    view={currentView}
                    onView={setCurrentView}
                    date={currentDate}
                    onNavigate={setCurrentDate}
                    step={30}
                    showMultiDayTimes
                    eventPropGetter={eventStyleGetter}
                    messages={{
                        next: "Next",
                        previous: "Previous",
                        today: "Today",
                        month: "Month",
                        week: "Week",
                        day: "Day",
                        agenda: "Agenda",
                        date: "Date",
                        time: "Time",
                        event: "Event",
                        noEventsInRange: "No events in this range",
                        showMore: (total: number) => `+${total} more`
                    }}
                />
            </div>

            {/* Events Table */}
            <EventsTable
                events={departmentEvents}
                onEdit={handleEditEvent}
                onDelete={handleDeleteEvent}
                title="Department Events"
                itemsPerPage={10}
            />

            {/* Edit Event Modal */}
            <EditEventModal
                show={showEditModal}
                onHide={() => setShowEditModal(false)}
                event={eventDetails}
                onChange={handleChange}
                onSubmit={handleSubmit}
                isEdit={!!editEventId}
                isLoading={createEvent.isPending || updateEvent.isPending}
                error={createEvent.error || updateEvent.error}
            />

            {/* Calendar Export Modal */}
            <CalendarExport
                show={showExportModal}
                onHide={() => setShowExportModal(false)}
            />
        </Container>
    );
};

export default DepartmentEventView;
