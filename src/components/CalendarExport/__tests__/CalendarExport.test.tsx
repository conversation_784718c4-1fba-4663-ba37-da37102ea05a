import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
// Jest is used instead of vitest
import CalendarExport from '../index';
import { AuthProvider } from '../../../contexts/AuthContext';
import { sampleUsers } from '../../../data/sampleUsers';
import { sampleEvents } from '../../../data/sampleEvents';
import { EventStatus } from '../../../types/event';

// Mock the calendar export utilities
jest.mock('../../../utils/calendarExport', () => ({
  exportToICal: jest.fn(),
  exportToCSV: jest.fn(),
  exportToJSON: jest.fn(),
  filterEventsForExport: jest.fn((events) => events),
  generateGoogleCalendarUrl: jest.fn(() => 'https://calendar.google.com/test'),
  generateOutlookUrl: jest.fn(() => 'https://outlook.live.com/test')
}));

// Mock the useEvents hook
const mockUseEvents = {
  events: sampleEvents
};

jest.mock('../../../hooks/useEvents', () => ({
  useEvents: () => mockUseEvents
}));

// Mock the useAuth hook
const mockUseAuth = {
  user: sampleUsers[0], // Admin user
  permissions: {
    canExportCalendar: true,
    canViewAllEvents: true,
    canCreateEvent: true,
    canEditEvent: () => true,
    canDeleteEvent: () => true,
    canApproveEvent: true,
    canRejectEvent: true,
    canManageDepartments: true,
    canManageUsers: true,
    canSetDeadlines: true,
    canViewAnalytics: true
  },
  isAuthenticated: true,
  login: jest.fn(),
  logout: jest.fn(),
  isLoading: false
};

jest.mock('../../../contexts/AuthContext', () => ({
  useAuth: () => mockUseAuth,
  AuthProvider: ({ children }: { children: React.ReactNode }) => <div>{children}</div>
}));

// Mock window.open
const mockWindowOpen = jest.fn();
Object.defineProperty(window, 'open', {
  value: mockWindowOpen,
  writable: true
});

const createTestQueryClient = () => {
  return new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false }
    }
  });
};

const renderWithProviders = (component: React.ReactElement) => {
  const queryClient = createTestQueryClient();
  return render(
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        {component}
      </AuthProvider>
    </QueryClientProvider>
  );
};

describe('CalendarExport', () => {
  const mockOnHide = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders calendar export modal when show is true', () => {
    renderWithProviders(<CalendarExport show={true} onHide={mockOnHide} />);
    
    expect(screen.getByText('Export Calendar')).toBeInTheDocument();
    expect(screen.getByText('Export Format')).toBeInTheDocument();
  });

  it('does not render modal when show is false', () => {
    renderWithProviders(<CalendarExport show={false} onHide={mockOnHide} />);
    
    expect(screen.queryByText('Export Calendar')).not.toBeInTheDocument();
  });

  it('displays all export format options', () => {
    renderWithProviders(<CalendarExport show={true} onHide={mockOnHide} />);
    
    expect(screen.getByText('iCal (.ics)')).toBeInTheDocument();
    expect(screen.getByText('Google Calendar')).toBeInTheDocument();
    expect(screen.getByText('Outlook')).toBeInTheDocument();
    expect(screen.getByText('CSV (.csv)')).toBeInTheDocument();
    expect(screen.getByText('JSON (.json)')).toBeInTheDocument();
  });

  it('shows date range inputs', () => {
    renderWithProviders(<CalendarExport show={true} onHide={mockOnHide} />);
    
    expect(screen.getByLabelText('Start Date')).toBeInTheDocument();
    expect(screen.getByLabelText('End Date')).toBeInTheDocument();
  });

  it('shows event status checkboxes', () => {
    renderWithProviders(<CalendarExport show={true} onHide={mockOnHide} />);
    
    expect(screen.getByText('Include Event Statuses')).toBeInTheDocument();
    expect(screen.getByLabelText('Confirmed')).toBeInTheDocument();
    expect(screen.getByLabelText('Pending')).toBeInTheDocument();
    expect(screen.getByLabelText('Draft')).toBeInTheDocument();
  });

  it('updates export format when radio button is selected', () => {
    renderWithProviders(<CalendarExport show={true} onHide={mockOnHide} />);
    
    const csvRadio = screen.getByLabelText('CSV (.csv)');
    fireEvent.click(csvRadio);
    
    expect(csvRadio).toBeChecked();
  });

  it('updates date range when inputs change', () => {
    renderWithProviders(<CalendarExport show={true} onHide={mockOnHide} />);
    
    const startDateInput = screen.getByLabelText('Start Date');
    fireEvent.change(startDateInput, { target: { value: '2024-01-01' } });
    
    expect(startDateInput).toHaveValue('2024-01-01');
  });

  it('toggles status checkboxes correctly', () => {
    renderWithProviders(<CalendarExport show={true} onHide={mockOnHide} />);
    
    const pendingCheckbox = screen.getByLabelText('Pending');
    fireEvent.click(pendingCheckbox);
    
    expect(pendingCheckbox).toBeChecked();
  });

  it('calls export function when export button is clicked for iCal', async () => {
    const { exportToICal } = require('../../../utils/calendarExport');
    
    renderWithProviders(<CalendarExport show={true} onHide={mockOnHide} />);
    
    const exportButton = screen.getByText('Export Calendar');
    fireEvent.click(exportButton);
    
    await waitFor(() => {
      expect(exportToICal).toHaveBeenCalled();
    });
  });

  it('calls export function when export button is clicked for CSV', async () => {
    const { exportToCSV } = require('../../../utils/calendarExport');
    
    renderWithProviders(<CalendarExport show={true} onHide={mockOnHide} />);
    
    const csvRadio = screen.getByLabelText('CSV (.csv)');
    fireEvent.click(csvRadio);
    
    const exportButton = screen.getByText('Export Calendar');
    fireEvent.click(exportButton);
    
    await waitFor(() => {
      expect(exportToCSV).toHaveBeenCalled();
    });
  });

  it('calls export function when export button is clicked for JSON', async () => {
    const { exportToJSON } = require('../../../utils/calendarExport');
    
    renderWithProviders(<CalendarExport show={true} onHide={mockOnHide} />);
    
    const jsonRadio = screen.getByLabelText('JSON (.json)');
    fireEvent.click(jsonRadio);
    
    const exportButton = screen.getByText('Export Calendar');
    fireEvent.click(exportButton);
    
    await waitFor(() => {
      expect(exportToJSON).toHaveBeenCalled();
    });
  });

  it('opens Google Calendar URLs when Google format is selected', async () => {
    renderWithProviders(<CalendarExport show={true} onHide={mockOnHide} />);
    
    const googleRadio = screen.getByLabelText('Google Calendar');
    fireEvent.click(googleRadio);
    
    const exportButton = screen.getByText('Export Calendar');
    fireEvent.click(exportButton);
    
    await waitFor(() => {
      expect(mockWindowOpen).toHaveBeenCalled();
    });
  });

  it('shows filename input', () => {
    renderWithProviders(<CalendarExport show={true} onHide={mockOnHide} />);
    
    expect(screen.getByLabelText('Filename')).toBeInTheDocument();
  });

  it('updates filename when input changes', () => {
    renderWithProviders(<CalendarExport show={true} onHide={mockOnHide} />);
    
    const filenameInput = screen.getByLabelText('Filename');
    fireEvent.change(filenameInput, { target: { value: 'my-calendar' } });
    
    expect(filenameInput).toHaveValue('my-calendar');
  });

  it('closes modal when cancel button is clicked', () => {
    renderWithProviders(<CalendarExport show={true} onHide={mockOnHide} />);
    
    const cancelButton = screen.getByText('Cancel');
    fireEvent.click(cancelButton);
    
    expect(mockOnHide).toHaveBeenCalled();
  });

  it('closes modal when close button is clicked', () => {
    renderWithProviders(<CalendarExport show={true} onHide={mockOnHide} />);
    
    const closeButton = screen.getByRole('button', { name: /close/i });
    fireEvent.click(closeButton);
    
    expect(mockOnHide).toHaveBeenCalled();
  });

  it('shows department filter for admin users', () => {
    renderWithProviders(<CalendarExport show={true} onHide={mockOnHide} />);
    
    expect(screen.getByText('Include Departments')).toBeInTheDocument();
  });

  it('shows private events option for admin users', () => {
    renderWithProviders(<CalendarExport show={true} onHide={mockOnHide} />);
    
    expect(screen.getByText('Include Private Events')).toBeInTheDocument();
  });

  it('does not show admin-only options for regular users', () => {
    const regularUserAuth = {
      ...mockUseAuth,
      user: sampleUsers[2], // Employee
      permissions: {
        ...mockUseAuth.permissions,
        canViewAllEvents: false,
        canManageDepartments: false
      }
    };

    jest.fn().mockImplementation(require('../../../contexts/AuthContext').useAuth).mockReturnValue(regularUserAuth);
    
    renderWithProviders(<CalendarExport show={true} onHide={mockOnHide} />);
    
    expect(screen.queryByText('Include Departments')).not.toBeInTheDocument();
    expect(screen.queryByText('Include Private Events')).not.toBeInTheDocument();
  });

  it('displays event count preview', () => {
    renderWithProviders(<CalendarExport show={true} onHide={mockOnHide} />);
    
    expect(screen.getByText(/events will be exported/)).toBeInTheDocument();
  });

  it('handles empty events list gracefully', () => {
    jest.fn().mockImplementation(require('../../../hooks/useEvents').useEvents).mockReturnValue({
      events: []
    });
    
    renderWithProviders(<CalendarExport show={true} onHide={mockOnHide} />);
    
    expect(screen.getByText('0 events will be exported')).toBeInTheDocument();
  });

  it('shows warning for large number of Google Calendar events', async () => {
    // Mock alert
    const mockAlert = jest.fn();
    window.alert = mockAlert;
    
    // Create many events
    const manyEvents = Array.from({ length: 15 }, (_, i) => ({
      ...sampleEvents[0],
      id: `event-${i}`
    }));
    
    jest.fn().mockImplementation(require('../../../hooks/useEvents').useEvents).mockReturnValue({
      events: manyEvents
    });
    
    renderWithProviders(<CalendarExport show={true} onHide={mockOnHide} />);
    
    const googleRadio = screen.getByLabelText('Google Calendar');
    fireEvent.click(googleRadio);
    
    const exportButton = screen.getByText('Export Calendar');
    fireEvent.click(exportButton);
    
    await waitFor(() => {
      expect(mockAlert).toHaveBeenCalledWith(expect.stringContaining('Only the first 10 events'));
    });
  });
});
