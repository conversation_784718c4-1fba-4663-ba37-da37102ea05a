import React, { useState } from 'react';
import { <PERSON><PERSON>, Button, Form, Row, Col, Al<PERSON>, Badge } from 'react-bootstrap';
import { Download, Calendar, FileText, Database } from 'lucide-react';
import { useEvents } from '../../hooks/useEvents';
import { useAuth } from '../../contexts/AuthContext';
import { EventStatus, CalendarExportOptions } from '../../types/event';
import { 
  exportToICal, 
  exportToCSV, 
  exportToJSON, 
  filterEventsForExport,
  generateGoogleCalendarUrl,
  generateOutlookUrl
} from '../../utils/calendarExport';

interface CalendarExportProps {
  show: boolean;
  onHide: () => void;
}

const CalendarExport: React.FC<CalendarExportProps> = ({ show, onHide }) => {
  const { events } = useEvents();
  const { permissions } = useAuth();
  
  const [exportOptions, setExportOptions] = useState<CalendarExportOptions>({
    format: 'ical',
    dateRange: {
      start: new Date().toISOString().split('T')[0], // Today
      end: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] // One year from now
    },
    includeStatuses: [EventStatus.CONFIRMED],
    includeDepartments: [],
    includePrivateEvents: false
  });

  const [isExporting, setIsExporting] = useState(false);
  const [exportError, setExportError] = useState<string>('');

  // Check permissions
  if (!permissions.canExportCalendar) {
    return (
      <Modal show={show} onHide={onHide}>
        <Modal.Header closeButton>
          <Modal.Title>Export Calendar</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Alert variant="danger">
            You do not have permission to export calendar data.
          </Alert>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={onHide}>Close</Button>
        </Modal.Footer>
      </Modal>
    );
  }

  const handleExport = async () => {
    setIsExporting(true);
    setExportError('');

    try {
      const filteredEvents = filterEventsForExport(events, exportOptions);
      
      if (filteredEvents.length === 0) {
        setExportError('No events match the selected criteria.');
        return;
      }

      const timestamp = new Date().toISOString().split('T')[0];
      const filename = `calendar-export-${timestamp}`;

      switch (exportOptions.format) {
        case 'ical':
          exportToICal(filteredEvents, `${filename}.ics`);
          break;
        case 'csv':
          exportToCSV(filteredEvents, `${filename}.csv`);
          break;
        case 'json':
          exportToJSON(filteredEvents, `${filename}.json`);
          break;
        case 'google':
          // For Google Calendar, we'll open multiple tabs (limited to first 10 events)
          const googleEvents = filteredEvents.slice(0, 10);
          googleEvents.forEach((event, index) => {
            setTimeout(() => {
              window.open(generateGoogleCalendarUrl(event), '_blank');
            }, index * 500); // Stagger the opens to avoid popup blocking
          });
          if (filteredEvents.length > 10) {
            alert(`Only the first 10 events were opened in Google Calendar. Total events: ${filteredEvents.length}`);
          }
          break;
        case 'outlook':
          // Similar to Google Calendar
          const outlookEvents = filteredEvents.slice(0, 10);
          outlookEvents.forEach((event, index) => {
            setTimeout(() => {
              window.open(generateOutlookUrl(event), '_blank');
            }, index * 500);
          });
          if (filteredEvents.length > 10) {
            alert(`Only the first 10 events were opened in Outlook. Total events: ${filteredEvents.length}`);
          }
          break;
        default:
          throw new Error('Unsupported export format');
      }

      onHide();
    } catch (error) {
      setExportError(error instanceof Error ? error.message : 'Export failed');
    } finally {
      setIsExporting(false);
    }
  };

  const handleStatusChange = (status: EventStatus, checked: boolean) => {
    setExportOptions(prev => ({
      ...prev,
      includeStatuses: checked 
        ? [...prev.includeStatuses, status]
        : prev.includeStatuses.filter(s => s !== status)
    }));
  };

  const filteredEvents = filterEventsForExport(events, exportOptions);
  const uniqueDepartments = Array.from(new Set(events.map(e => e.departmentId)));

  return (
    <Modal show={show} onHide={onHide} size="lg">
      <Modal.Header closeButton>
        <Modal.Title>
          <Download className="me-2" />
          Export Calendar
        </Modal.Title>
      </Modal.Header>
      <Modal.Body>
        <Form>
          {/* Export Format */}
          <Form.Group className="mb-3">
            <Form.Label>Export Format</Form.Label>
            <Form.Select
              value={exportOptions.format}
              onChange={(e) => setExportOptions(prev => ({ 
                ...prev, 
                format: e.target.value as CalendarExportOptions['format']
              }))}
            >
              <option value="ical">iCal (.ics) - Universal calendar format</option>
              <option value="google">Google Calendar - Open in browser</option>
              <option value="outlook">Outlook Calendar - Open in browser</option>
              <option value="csv">CSV (.csv) - Spreadsheet format</option>
              <option value="json">JSON (.json) - Data format</option>
            </Form.Select>
          </Form.Group>

          {/* Date Range */}
          <Row className="mb-3">
            <Col md={6}>
              <Form.Group>
                <Form.Label>Start Date</Form.Label>
                <Form.Control
                  type="date"
                  value={exportOptions.dateRange.start}
                  onChange={(e) => setExportOptions(prev => ({
                    ...prev,
                    dateRange: { ...prev.dateRange, start: e.target.value }
                  }))}
                />
              </Form.Group>
            </Col>
            <Col md={6}>
              <Form.Group>
                <Form.Label>End Date</Form.Label>
                <Form.Control
                  type="date"
                  value={exportOptions.dateRange.end}
                  onChange={(e) => setExportOptions(prev => ({
                    ...prev,
                    dateRange: { ...prev.dateRange, end: e.target.value }
                  }))}
                />
              </Form.Group>
            </Col>
          </Row>

          {/* Event Status Filter */}
          <Form.Group className="mb-3">
            <Form.Label>Include Event Statuses</Form.Label>
            <div>
              {Object.values(EventStatus).map(status => (
                <Form.Check
                  key={status}
                  type="checkbox"
                  id={`status-${status}`}
                  label={status.charAt(0).toUpperCase() + status.slice(1)}
                  checked={exportOptions.includeStatuses.includes(status)}
                  onChange={(e) => handleStatusChange(status, e.target.checked)}
                  inline
                />
              ))}
            </div>
          </Form.Group>

          {/* Department Filter */}
          {permissions.canViewAllEvents && uniqueDepartments.length > 1 && (
            <Form.Group className="mb-3">
              <Form.Label>Include Departments (leave empty for all)</Form.Label>
              <Form.Control
                as="select"
                multiple
                value={exportOptions.includeDepartments}
                onChange={(e) => {
                  const selected = Array.from(e.target.selectedOptions, option => option.value);
                  setExportOptions(prev => ({ ...prev, includeDepartments: selected }));
                }}
              >
                {uniqueDepartments.map(deptId => {
                  const event = events.find(e => e.departmentId === deptId);
                  return (
                    <option key={deptId} value={deptId}>
                      {event?.departmentName || deptId}
                    </option>
                  );
                })}
              </Form.Control>
              <Form.Text className="text-muted">
                Hold Ctrl/Cmd to select multiple departments
              </Form.Text>
            </Form.Group>
          )}

          {/* Export Preview */}
          <Alert variant="info">
            <div className="d-flex justify-content-between align-items-center">
              <span>
                <strong>{filteredEvents.length}</strong> events will be exported
              </span>
              <div>
                {exportOptions.includeStatuses.map(status => (
                  <Badge key={status} bg="secondary" className="me-1">
                    {status}
                  </Badge>
                ))}
              </div>
            </div>
          </Alert>

          {/* Error Message */}
          {exportError && (
            <Alert variant="danger">
              {exportError}
            </Alert>
          )}

          {/* Format-specific Notes */}
          {exportOptions.format === 'google' && (
            <Alert variant="warning">
              <strong>Note:</strong> Google Calendar export will open each event in a new browser tab. 
              Limited to 10 events to prevent browser blocking.
            </Alert>
          )}

          {exportOptions.format === 'outlook' && (
            <Alert variant="warning">
              <strong>Note:</strong> Outlook export will open each event in a new browser tab. 
              Limited to 10 events to prevent browser blocking.
            </Alert>
          )}
        </Form>
      </Modal.Body>
      <Modal.Footer>
        <Button variant="secondary" onClick={onHide}>
          Cancel
        </Button>
        <Button 
          variant="primary" 
          onClick={handleExport}
          disabled={isExporting || filteredEvents.length === 0}
        >
          {isExporting ? 'Exporting...' : (
            <>
              {exportOptions.format === 'ical' && <Calendar className="me-2" size={16} />}
              {exportOptions.format === 'csv' && <FileText className="me-2" size={16} />}
              {exportOptions.format === 'json' && <Database className="me-2" size={16} />}
              {(exportOptions.format === 'google' || exportOptions.format === 'outlook') && <Calendar className="me-2" size={16} />}
              Export {filteredEvents.length} Events
            </>
          )}
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

export default CalendarExport;
