import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
// Jest is used instead of vitest
import EventSubmission from '../index';
import { AuthProvider } from '../../../contexts/AuthContext';
import { sampleUsers } from '../../../data/sampleUsers';
import { sampleEvents } from '../../../data/sampleEvents';
import { EventStatus, EventConflict } from '../../../types/event';

// Mock the useEvents hook
const mockConflicts: EventConflict[] = [
  {
    id: '1',
    type: 'time_overlap',
    conflictingEventId: '2',
    description: 'Time overlap with another event',
    severity: 'medium'
  }
];

const mockUseEvents = {
  submitEvent: { mutateAsync: jest.fn(), isPending: false },
  checkConflicts: { mutateAsync: jest.fn().mockResolvedValue(mockConflicts) }
};

jest.mock('../../../hooks/useEvents', () => ({
  useEvents: () => mockUseEvents
}));

// Mock the useAuth hook
const mockUseAuth = {
  user: sampleUsers[1], // Team leader
  permissions: {
    canCreateEvent: true,
    canEditEvent: () => true,
    canDeleteEvent: () => false,
    canApproveEvent: false,
    canRejectEvent: false,
    canViewAllEvents: false,
    canManageDepartments: false,
    canManageUsers: false,
    canSetDeadlines: false,
    canExportCalendar: true,
    canViewAnalytics: false
  },
  isAuthenticated: true,
  login: jest.fn(),
  logout: jest.fn(),
  isLoading: false
};

jest.mock('../../../contexts/AuthContext', () => ({
  useAuth: () => mockUseAuth,
  AuthProvider: ({ children }: { children: React.ReactNode }) => <div>{children}</div>
}));

const createTestQueryClient = () => {
  return new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false }
    }
  });
};

const renderWithProviders = (component: React.ReactElement) => {
  const queryClient = createTestQueryClient();
  return render(
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        {component}
      </AuthProvider>
    </QueryClientProvider>
  );
};

const mockDraftEvent = {
  ...sampleEvents[0],
  status: EventStatus.DRAFT,
  id: 'draft-event-1'
};

const mockPendingEvent = {
  ...sampleEvents[0],
  status: EventStatus.PENDING,
  id: 'pending-event-1'
};

const mockRejectedEvent = {
  ...sampleEvents[0],
  status: EventStatus.REJECTED,
  id: 'rejected-event-1'
};

describe('EventSubmission', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders event submission component for draft event', () => {
    renderWithProviders(<EventSubmission event={mockDraftEvent} />);
    
    expect(screen.getByText('Event Submission')).toBeInTheDocument();
    expect(screen.getByText('Current Status:')).toBeInTheDocument();
    expect(screen.getByText('DRAFT')).toBeInTheDocument();
  });

  it('shows submit button for draft events', () => {
    renderWithProviders(<EventSubmission event={mockDraftEvent} />);
    
    expect(screen.getByText('Submit for Review')).toBeInTheDocument();
  });

  it('checks for conflicts on mount for draft events', async () => {
    renderWithProviders(<EventSubmission event={mockDraftEvent} />);
    
    await waitFor(() => {
      expect(mockUseEvents.checkConflicts.mutateAsync).toHaveBeenCalled();
    });
  });

  it('displays conflict information when conflicts exist', async () => {
    renderWithProviders(<EventSubmission event={mockDraftEvent} />);
    
    await waitFor(() => {
      expect(screen.getByText('1 conflict(s) detected:')).toBeInTheDocument();
    });
  });

  it('shows check conflicts button', () => {
    renderWithProviders(<EventSubmission event={mockDraftEvent} />);
    
    expect(screen.getByText('Check for Conflicts')).toBeInTheDocument();
  });

  it('manually checks conflicts when button is clicked', async () => {
    renderWithProviders(<EventSubmission event={mockDraftEvent} />);
    
    const checkButton = screen.getByText('Check for Conflicts');
    fireEvent.click(checkButton);
    
    await waitFor(() => {
      expect(mockUseEvents.checkConflicts.mutateAsync).toHaveBeenCalledTimes(2); // Once on mount, once on click
    });
  });

  it('opens confirmation modal when submit button is clicked', async () => {
    renderWithProviders(<EventSubmission event={mockDraftEvent} />);
    
    const submitButton = screen.getByText('Submit for Review');
    fireEvent.click(submitButton);
    
    await waitFor(() => {
      expect(screen.getByText('Confirm Event Submission')).toBeInTheDocument();
    });
  });

  it('displays event details in confirmation modal', async () => {
    renderWithProviders(<EventSubmission event={mockDraftEvent} />);
    
    const submitButton = screen.getByText('Submit for Review');
    fireEvent.click(submitButton);
    
    await waitFor(() => {
      expect(screen.getByText(mockDraftEvent.eventName)).toBeInTheDocument();
      expect(screen.getByText(mockDraftEvent.venue)).toBeInTheDocument();
    });
  });

  it('submits event when confirmed in modal', async () => {
    renderWithProviders(<EventSubmission event={mockDraftEvent} />);
    
    const submitButton = screen.getByText('Submit for Review');
    fireEvent.click(submitButton);
    
    await waitFor(() => {
      expect(screen.getByText('Confirm Event Submission')).toBeInTheDocument();
    });
    
    const confirmButton = screen.getByText('Submit Event');
    fireEvent.click(confirmButton);
    
    await waitFor(() => {
      expect(mockUseEvents.submitEvent.mutateAsync).toHaveBeenCalledWith(mockDraftEvent.id.toString());
    });
  });

  it('calls onSubmitted callback after successful submission', async () => {
    const onSubmitted = jest.fn();
    renderWithProviders(<EventSubmission event={mockDraftEvent} onSubmitted={onSubmitted} />);
    
    const submitButton = screen.getByText('Submit for Review');
    fireEvent.click(submitButton);
    
    await waitFor(() => {
      expect(screen.getByText('Confirm Event Submission')).toBeInTheDocument();
    });
    
    const confirmButton = screen.getByText('Submit Event');
    fireEvent.click(confirmButton);
    
    await waitFor(() => {
      expect(onSubmitted).toHaveBeenCalled();
    });
  });

  it('shows pending status message for pending events', () => {
    renderWithProviders(<EventSubmission event={mockPendingEvent} />);
    
    expect(screen.getByText('This event is currently under review by administrators.')).toBeInTheDocument();
  });

  it('shows rejection message for rejected events', () => {
    renderWithProviders(<EventSubmission event={mockRejectedEvent} />);
    
    expect(screen.getByText('This event was rejected. Please review the feedback and create a new event if needed.')).toBeInTheDocument();
  });

  it('does not show submit button for non-draft events', () => {
    renderWithProviders(<EventSubmission event={mockPendingEvent} />);
    
    expect(screen.queryByText('Submit for Review')).not.toBeInTheDocument();
  });

  it('handles submission error gracefully', async () => {
    mockUseEvents.submitEvent.mutateAsync.mockRejectedValueOnce(new Error('Submission failed'));
    
    renderWithProviders(<EventSubmission event={mockDraftEvent} />);
    
    const submitButton = screen.getByText('Submit for Review');
    fireEvent.click(submitButton);
    
    await waitFor(() => {
      expect(screen.getByText('Confirm Event Submission')).toBeInTheDocument();
    });
    
    const confirmButton = screen.getByText('Submit Event');
    fireEvent.click(confirmButton);
    
    await waitFor(() => {
      expect(screen.getByText('Failed to submit event for review. Please try again.')).toBeInTheDocument();
    });
  });

  it('shows permission error when user cannot create events', async () => {
    const noPermissionAuth = {
      ...mockUseAuth,
      permissions: {
        ...mockUseAuth.permissions,
        canCreateEvent: false
      }
    };

    jest.fn().mockImplementation(require('../../../contexts/AuthContext').useAuth).mockReturnValue(noPermissionAuth);
    
    renderWithProviders(<EventSubmission event={mockDraftEvent} />);
    
    const submitButton = screen.getByText('Submit for Review');
    fireEvent.click(submitButton);
    
    await waitFor(() => {
      expect(screen.getByText('Confirm Event Submission')).toBeInTheDocument();
    });
    
    const confirmButton = screen.getByText('Submit Event');
    fireEvent.click(confirmButton);
    
    await waitFor(() => {
      expect(screen.getByText('You do not have permission to submit events.')).toBeInTheDocument();
    });
  });

  it('closes confirmation modal when cancel is clicked', async () => {
    renderWithProviders(<EventSubmission event={mockDraftEvent} />);
    
    const submitButton = screen.getByText('Submit for Review');
    fireEvent.click(submitButton);
    
    await waitFor(() => {
      expect(screen.getByText('Confirm Event Submission')).toBeInTheDocument();
    });
    
    const cancelButton = screen.getByText('Cancel');
    fireEvent.click(cancelButton);
    
    await waitFor(() => {
      expect(screen.queryByText('Confirm Event Submission')).not.toBeInTheDocument();
    });
  });

  it('displays conflict severity badges correctly', async () => {
    renderWithProviders(<EventSubmission event={mockDraftEvent} />);
    
    await waitFor(() => {
      expect(screen.getByText('MEDIUM')).toBeInTheDocument();
    });
  });

  it('shows high conflict warning in submit button', async () => {
    const highConflicts: EventConflict[] = [
      {
        id: '1',
        type: 'time_overlap',
        conflictingEventId: '2',
        description: 'High priority conflict',
        severity: 'high'
      }
    ];

    mockUseEvents.checkConflicts.mutateAsync.mockResolvedValueOnce(highConflicts);
    
    renderWithProviders(<EventSubmission event={mockDraftEvent} />);
    
    await waitFor(() => {
      expect(screen.getByText('Submit Despite Conflicts')).toBeInTheDocument();
    });
  });
});
