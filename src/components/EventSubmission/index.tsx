import React, { useState, useEffect } from 'react';
import { <PERSON>, Button, Alert, Badge, Modal } from 'react-bootstrap';
import { AlertTriangle, Clock, CheckCircle, Send } from 'lucide-react';
import { useEvents } from '../../hooks/useEvents';
import { useAuth } from '../../contexts/AuthContext';
import { EvaEvent, EventStatus, EventConflict } from '../../types/event';
import { formatDateTime } from '../../utils/dateUtils';

interface EventSubmissionProps {
  event: EvaEvent;
  onSubmitted?: () => void;
}

const EventSubmission: React.FC<EventSubmissionProps> = ({ event, onSubmitted }) => {
  const { permissions } = useAuth();
  const { submitEvent, checkConflicts } = useEvents();

  const [conflicts, setConflicts] = useState<EventConflict[]>([]);
  const [isCheckingConflicts, setIsCheckingConflicts] = useState(false);
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [submitError, setSubmitError] = useState<string>('');

  // Check for conflicts when component mounts
  useEffect(() => {
    if (event.status === EventStatus.DRAFT) {
      checkForConflicts();
    }
  }, [event]);

  const checkForConflicts = async () => {
    setIsCheckingConflicts(true);
    try {
      const { id, start, end, title, ...eventData } = event;
      const detectedConflicts = await checkConflicts.mutateAsync(eventData);
      setConflicts(detectedConflicts);
    } catch (error) {
      console.error('Error checking conflicts:', error);
    } finally {
      setIsCheckingConflicts(false);
    }
  };

  const handleSubmitForReview = async () => {
    if (!permissions.canCreateEvent) {
      setSubmitError('You do not have permission to submit events.');
      return;
    }

    try {
      await submitEvent.mutateAsync(event.id.toString());
      setShowConfirmModal(false);
      onSubmitted?.();
    } catch (error) {
      setSubmitError('Failed to submit event for review. Please try again.');
      console.error('Error submitting event:', error);
    }
  };

  const getConflictIcon = (severity: string) => {
    switch (severity) {
      case 'high':
        return <AlertTriangle className="text-danger" size={16} />;
      case 'medium':
        return <AlertTriangle className="text-warning" size={16} />;
      default:
        return <AlertTriangle className="text-info" size={16} />;
    }
  };

  const getConflictBadge = (severity: string) => {
    const variants = {
      low: 'info',
      medium: 'warning',
      high: 'danger'
    };
    return <Badge bg={variants[severity as keyof typeof variants] || 'secondary'}>{severity.toUpperCase()}</Badge>;
  };

  const canSubmit = event.status === EventStatus.DRAFT && permissions.canCreateEvent;
  const hasHighConflicts = conflicts.some(c => c.severity === 'high');

  return (
    <Card>
      <Card.Header>
        <h6 className="mb-0">
          <Send className="me-2" />
          Event Submission
        </h6>
      </Card.Header>
      <Card.Body>
        {/* Event Status */}
        <div className="mb-3">
          <strong>Current Status: </strong>
          <Badge
            bg={
              event.status === EventStatus.DRAFT ? 'secondary' :
                event.status === EventStatus.PENDING ? 'warning' :
                  event.status === EventStatus.CONFIRMED ? 'success' :
                    'danger'
            }
          >
            {event.status.toUpperCase()}
          </Badge>
        </div>

        {/* Event Details Summary */}
        <div className="mb-3">
          <h6>Event Details</h6>
          <p className="mb-1"><strong>Name:</strong> {event.eventName}</p>
          <p className="mb-1"><strong>Date & Time:</strong> {formatDateTime(event.date, event.time)}</p>
          <p className="mb-1"><strong>Venue:</strong> {event.venue}</p>
          <p className="mb-1"><strong>Department:</strong> {event.departmentName}</p>
          <p className="mb-0"><strong>Duration:</strong> {event.duration} minutes</p>
        </div>

        {/* Conflict Detection */}
        {event.status === EventStatus.DRAFT && (
          <div className="mb-3">
            <div className="d-flex justify-content-between align-items-center mb-2">
              <h6 className="mb-0">Conflict Check</h6>
              <Button
                size="sm"
                variant="outline-primary"
                onClick={checkForConflicts}
                disabled={isCheckingConflicts}
              >
                {isCheckingConflicts ? 'Checking...' : 'Recheck'}
              </Button>
            </div>

            {isCheckingConflicts ? (
              <Alert variant="info">
                <Clock className="me-2" />
                Checking for conflicts...
              </Alert>
            ) : conflicts.length === 0 ? (
              <Alert variant="success">
                <CheckCircle className="me-2" />
                No conflicts detected. Event is ready for submission.
              </Alert>
            ) : (
              <Alert variant={hasHighConflicts ? 'danger' : 'warning'}>
                <AlertTriangle className="me-2" />
                {conflicts.length} conflict(s) detected:
                <ul className="mb-0 mt-2">
                  {conflicts.map((conflict, index) => (
                    <li key={index} className="d-flex align-items-center">
                      {getConflictIcon(conflict.severity)}
                      <span className="ms-2 me-2">{conflict.description}</span>
                      {getConflictBadge(conflict.severity)}
                    </li>
                  ))}
                </ul>
              </Alert>
            )}
          </div>
        )}

        {/* Submission Guidelines */}
        {canSubmit && (
          <Alert variant="info">
            <h6>Submission Guidelines:</h6>
            <ul className="mb-0">
              <li>Ensure all event details are accurate</li>
              <li>Review any conflicts and consider alternative times if needed</li>
              <li>Once submitted, the event will be reviewed by administrators</li>
              <li>You will be notified of the approval decision</li>
            </ul>
          </Alert>
        )}

        {/* Error Message */}
        {submitError && (
          <Alert variant="danger">
            {submitError}
          </Alert>
        )}

        {/* Submit Button */}
        {canSubmit && (
          <div className="d-grid">
            <Button
              variant={hasHighConflicts ? 'warning' : 'primary'}
              size="lg"
              onClick={() => setShowConfirmModal(true)}
              disabled={submitEvent.isPending}
            >
              {submitEvent.isPending ? 'Submitting...' :
                hasHighConflicts ? 'Submit Despite Conflicts' : 'Submit for Review'}
            </Button>
          </div>
        )}

        {/* Status Messages for Non-Draft Events */}
        {event.status === EventStatus.PENDING && (
          <Alert variant="warning">
            <Clock className="me-2" />
            This event is currently under review by administrators.
          </Alert>
        )}

        {event.status === EventStatus.CONFIRMED && (
          <Alert variant="success">
            <CheckCircle className="me-2" />
            This event has been approved and confirmed.
          </Alert>
        )}

        {event.status === EventStatus.REJECTED && (
          <Alert variant="danger">
            <AlertTriangle className="me-2" />
            This event was rejected. Please review the feedback and create a new event if needed.
          </Alert>
        )}
      </Card.Body>

      {/* Confirmation Modal */}
      <Modal show={showConfirmModal} onHide={() => setShowConfirmModal(false)}>
        <Modal.Header closeButton>
          <Modal.Title>Confirm Event Submission</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <p>Are you sure you want to submit this event for review?</p>

          <div className="mb-3">
            <strong>Event:</strong> {event.eventName}<br />
            <strong>Date & Time:</strong> {formatDateTime(event.date, event.time)}<br />
            <strong>Venue:</strong> {event.venue}
          </div>

          {hasHighConflicts && (
            <Alert variant="warning">
              <AlertTriangle className="me-2" />
              <strong>Warning:</strong> This event has high-priority conflicts.
              Consider resolving these conflicts before submission.
            </Alert>
          )}

          <p className="text-muted">
            Once submitted, you will not be able to edit the event until it's reviewed.
          </p>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowConfirmModal(false)}>
            Cancel
          </Button>
          <Button
            variant={hasHighConflicts ? 'warning' : 'primary'}
            onClick={handleSubmitForReview}
            disabled={submitEvent.isPending}
          >
            {submitEvent.isPending ? 'Submitting...' : 'Submit Event'}
          </Button>
        </Modal.Footer>
      </Modal>
    </Card>
  );
};

export default EventSubmission;
