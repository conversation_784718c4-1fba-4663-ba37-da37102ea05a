// Centralized icon exports for the application
// This file serves as the single source of truth for all icons
// Priority: shadcn/ui icons first, then lucide-react as fallback

// Import all icons from lucide-react (shadcn/ui's standard icon library)
import {
  // Navigation & UI
  Menu,
  X,
  ChevronLeft,
  ChevronRight,
  ChevronDown,
  ChevronUp,
  ArrowRight,
  ArrowLeft,

  // User & Auth
  User,
  Users,
  LogOut,
  Eye,
  EyeOff,
  Mail,
  Lock,

  // Calendar & Time
  Calendar,
  Clock,

  // Actions
  Settings,
  Download,
  Upload,
  Edit,
  Trash2,
  Plus,
  Minus,
  Check,
  CheckCircle,
  X as XIcon,
  XCircle,

  // Content
  Search,
  Filter,
  Star,
  Heart,
  Bell,

  // Business & Analytics
  BarChart3,
  TrendingUp,
  Building,
  Calculator,

  // Communication
  Phone,
  Video,

  // Status & Alerts
  AlertTriangle,
  Shield,
  Zap,

  // Media & Files
  Play,
  FileText,
  Database,

  // Misc
  Globe,
  MapPin,
  HeadphonesIcon,

  // Additional icons
  Send,
  Loader2,
  Info,
  ExternalLink,
} from 'lucide-react';

// Export all icons with consistent naming
export {
  // Navigation & UI
  Menu,
  X,
  ChevronLeft,
  ChevronRight,
  ChevronDown,
  ChevronUp,
  ArrowRight,
  ArrowLeft,

  // User & Auth
  User,
  Users,
  LogOut,
  Eye,
  EyeOff,
  Mail,
  Lock,

  // Calendar & Time
  Calendar,
  Clock,

  // Actions
  Settings,
  Download,
  Upload,
  Edit,
  Trash2,
  Plus,
  Minus,
  Check,
  CheckCircle,
  XIcon,
  XCircle,

  // Content
  Search,
  Filter,
  Star,
  Heart,
  Bell,

  // Business & Analytics
  BarChart3,
  TrendingUp,
  Building,
  Calculator,

  // Communication
  Phone,
  Video,

  // Status & Alerts
  AlertTriangle,
  Shield,
  Zap,

  // Media & Files
  Play,
  FileText,
  Database,

  // Misc
  Globe,
  MapPin,
  HeadphonesIcon,

  // Additional icons
  Send,
  Loader2,
  Info,
  ExternalLink,
};

// Icon component wrapper for consistent sizing and styling
export interface IconProps {
  size?: number | string;
  className?: string;
  color?: string;
}

// Common icon sizes
export const iconSizes = {
  xs: 12,
  sm: 16,
  md: 20,
  lg: 24,
  xl: 32,
} as const;

// Helper function to get icon size
export const getIconSize = (size?: number | string | keyof typeof iconSizes): number => {
  if (typeof size === 'number') return size;
  if (typeof size === 'string' && size in iconSizes) {
    return iconSizes[size as keyof typeof iconSizes];
  }
  return iconSizes.md; // default
};
