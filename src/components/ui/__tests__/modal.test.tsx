import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
// Jest is used instead of vitest
import { <PERSON><PERSON>, <PERSON>dal<PERSON>ontent, <PERSON>dal<PERSON>eader, ModalTitle, ModalDescription, ModalFooter } from '../modal';

// Mock the Radix UI Dialog components
jest.mock('@radix-ui/react-dialog', () => ({
  Root: ({ children, open }: { children: React.ReactNode; open?: boolean }) => 
    open ? <div data-testid="modal-root">{children}</div> : null,
  Portal: ({ children }: { children: React.ReactNode }) => 
    <div data-testid="modal-portal">{children}</div>,
  Overlay: React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(
    ({ className, ...props }, ref) => (
      <div ref={ref} className={className} data-testid="modal-overlay" {...props} />
    )
  ),
  Content: React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(
    ({ className, children, ...props }, ref) => (
      <div ref={ref} className={className} data-testid="modal-content" {...props}>
        {children}
      </div>
    )
  ),
  Title: React.forwardRef<HTMLHeadingElement, React.HTMLAttributes<HTMLHeadingElement>>(
    ({ className, ...props }, ref) => (
      <h2 ref={ref} className={className} data-testid="modal-title" {...props} />
    )
  ),
  Description: React.forwardRef<HTMLParagraphElement, React.HTMLAttributes<HTMLParagraphElement>>(
    ({ className, ...props }, ref) => (
      <p ref={ref} className={className} data-testid="modal-description" {...props} />
    )
  ),
  Close: React.forwardRef<HTMLButtonElement, React.ButtonHTMLAttributes<HTMLButtonElement>>(
    ({ className, children, ...props }, ref) => (
      <button ref={ref} className={className} data-testid="modal-close" {...props}>
        {children}
      </button>
    )
  ),
}));

describe('Modal Components', () => {
  describe('Modal', () => {
    it('renders modal when open', () => {
      render(
        <Modal open={true}>
          <ModalContent>
            <div>Modal content</div>
          </ModalContent>
        </Modal>
      );
      
      expect(screen.getByTestId('modal-root')).toBeInTheDocument();
      expect(screen.getByText('Modal content')).toBeInTheDocument();
    });

    it('does not render modal when closed', () => {
      render(
        <Modal open={false}>
          <ModalContent>
            <div>Modal content</div>
          </ModalContent>
        </Modal>
      );
      
      expect(screen.queryByTestId('modal-root')).not.toBeInTheDocument();
    });

    it('calls onOpenChange when state changes', () => {
      const onOpenChange = jest.fn();
      render(
        <Modal open={true} onOpenChange={onOpenChange}>
          <ModalContent>Content</ModalContent>
        </Modal>
      );
      
      // This would be triggered by Radix UI internally
      expect(screen.getByTestId('modal-root')).toBeInTheDocument();
    });
  });

  describe('ModalContent', () => {
    it('renders modal content with correct classes', () => {
      render(
        <Modal open={true}>
          <ModalContent>
            <div>Content</div>
          </ModalContent>
        </Modal>
      );
      
      const content = screen.getByTestId('modal-content');
      expect(content).toHaveClass(
        'fixed',
        'left-[50%]',
        'top-[50%]',
        'z-50',
        'grid',
        'w-full',
        'max-w-lg',
        'translate-x-[-50%]',
        'translate-y-[-50%]',
        'gap-4',
        'border',
        'bg-background',
        'p-6',
        'shadow-lg',
        'duration-200'
      );
    });

    it('applies custom className', () => {
      render(
        <Modal open={true}>
          <ModalContent className="custom-content">
            <div>Content</div>
          </ModalContent>
        </Modal>
      );
      
      const content = screen.getByTestId('modal-content');
      expect(content).toHaveClass('custom-content');
    });

    it('renders close button', () => {
      render(
        <Modal open={true}>
          <ModalContent>
            <div>Content</div>
          </ModalContent>
        </Modal>
      );
      
      const closeButton = screen.getByTestId('modal-close');
      expect(closeButton).toBeInTheDocument();
      expect(closeButton).toHaveClass(
        'absolute',
        'right-4',
        'top-4',
        'rounded-sm',
        'opacity-70'
      );
    });

    it('renders overlay with correct classes', () => {
      render(
        <Modal open={true}>
          <ModalContent>
            <div>Content</div>
          </ModalContent>
        </Modal>
      );
      
      const overlay = screen.getByTestId('modal-overlay');
      expect(overlay).toHaveClass(
        'fixed',
        'inset-0',
        'z-50',
        'bg-background/80',
        'backdrop-blur-sm'
      );
    });
  });

  describe('ModalHeader', () => {
    it('renders modal header with correct classes', () => {
      render(
        <Modal open={true}>
          <ModalContent>
            <ModalHeader>
              <div>Header content</div>
            </ModalHeader>
          </ModalContent>
        </Modal>
      );
      
      const header = screen.getByText('Header content').parentElement;
      expect(header).toHaveClass('flex', 'flex-col', 'space-y-1.5', 'text-center', 'sm:text-left');
    });

    it('applies custom className', () => {
      render(
        <Modal open={true}>
          <ModalContent>
            <ModalHeader className="custom-header">
              <div>Header</div>
            </ModalHeader>
          </ModalContent>
        </Modal>
      );
      
      const header = screen.getByText('Header').parentElement;
      expect(header).toHaveClass('custom-header');
    });
  });

  describe('ModalTitle', () => {
    it('renders modal title with correct classes', () => {
      render(
        <Modal open={true}>
          <ModalContent>
            <ModalHeader>
              <ModalTitle>Modal Title</ModalTitle>
            </ModalHeader>
          </ModalContent>
        </Modal>
      );
      
      const title = screen.getByTestId('modal-title');
      expect(title).toHaveClass('text-lg', 'font-semibold', 'leading-none', 'tracking-tight');
      expect(title).toHaveTextContent('Modal Title');
    });

    it('applies custom className', () => {
      render(
        <Modal open={true}>
          <ModalContent>
            <ModalHeader>
              <ModalTitle className="custom-title">Title</ModalTitle>
            </ModalHeader>
          </ModalContent>
        </Modal>
      );
      
      const title = screen.getByTestId('modal-title');
      expect(title).toHaveClass('custom-title');
    });
  });

  describe('ModalDescription', () => {
    it('renders modal description with correct classes', () => {
      render(
        <Modal open={true}>
          <ModalContent>
            <ModalHeader>
              <ModalDescription>Modal description text</ModalDescription>
            </ModalHeader>
          </ModalContent>
        </Modal>
      );
      
      const description = screen.getByTestId('modal-description');
      expect(description).toHaveClass('text-sm', 'text-muted-foreground');
      expect(description).toHaveTextContent('Modal description text');
    });

    it('applies custom className', () => {
      render(
        <Modal open={true}>
          <ModalContent>
            <ModalHeader>
              <ModalDescription className="custom-description">Description</ModalDescription>
            </ModalHeader>
          </ModalContent>
        </Modal>
      );
      
      const description = screen.getByTestId('modal-description');
      expect(description).toHaveClass('custom-description');
    });
  });

  describe('ModalFooter', () => {
    it('renders modal footer with correct classes', () => {
      render(
        <Modal open={true}>
          <ModalContent>
            <ModalFooter>
              <div>Footer content</div>
            </ModalFooter>
          </ModalContent>
        </Modal>
      );
      
      const footer = screen.getByText('Footer content').parentElement;
      expect(footer).toHaveClass(
        'flex',
        'flex-col-reverse',
        'sm:flex-row',
        'sm:justify-end',
        'sm:space-x-2'
      );
    });

    it('applies custom className', () => {
      render(
        <Modal open={true}>
          <ModalContent>
            <ModalFooter className="custom-footer">
              <div>Footer</div>
            </ModalFooter>
          </ModalContent>
        </Modal>
      );
      
      const footer = screen.getByText('Footer').parentElement;
      expect(footer).toHaveClass('custom-footer');
    });
  });

  describe('Complete Modal Structure', () => {
    it('renders complete modal with all components', () => {
      render(
        <Modal open={true}>
          <ModalContent>
            <ModalHeader>
              <ModalTitle>Complete Modal</ModalTitle>
              <ModalDescription>This is a complete modal example</ModalDescription>
            </ModalHeader>
            <div className="py-4">
              <p>Modal body content goes here</p>
            </div>
            <ModalFooter>
              <button>Cancel</button>
              <button>Confirm</button>
            </ModalFooter>
          </ModalContent>
        </Modal>
      );
      
      expect(screen.getByText('Complete Modal')).toBeInTheDocument();
      expect(screen.getByText('This is a complete modal example')).toBeInTheDocument();
      expect(screen.getByText('Modal body content goes here')).toBeInTheDocument();
      expect(screen.getByRole('button', { name: 'Cancel' })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: 'Confirm' })).toBeInTheDocument();
    });

    it('handles close button click', () => {
      const onOpenChange = jest.fn();
      render(
        <Modal open={true} onOpenChange={onOpenChange}>
          <ModalContent>
            <ModalHeader>
              <ModalTitle>Test Modal</ModalTitle>
            </ModalHeader>
          </ModalContent>
        </Modal>
      );
      
      const closeButton = screen.getByTestId('modal-close');
      fireEvent.click(closeButton);
      
      // The actual close behavior would be handled by Radix UI
      expect(closeButton).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    it('supports ARIA attributes', () => {
      render(
        <Modal open={true}>
          <ModalContent>
            <ModalHeader>
              <ModalTitle id="modal-title">Accessible Modal</ModalTitle>
              <ModalDescription id="modal-desc">Description</ModalDescription>
            </ModalHeader>
          </ModalContent>
        </Modal>
      );
      
      const title = screen.getByTestId('modal-title');
      const description = screen.getByTestId('modal-description');
      
      expect(title).toHaveAttribute('id', 'modal-title');
      expect(description).toHaveAttribute('id', 'modal-desc');
    });

    it('maintains focus management', () => {
      render(
        <Modal open={true}>
          <ModalContent>
            <ModalHeader>
              <ModalTitle>Focus Test</ModalTitle>
            </ModalHeader>
            <div>
              <input type="text" placeholder="Test input" />
              <button>Test button</button>
            </div>
          </ModalContent>
        </Modal>
      );
      
      const input = screen.getByPlaceholderText('Test input');
      const button = screen.getByRole('button', { name: 'Test button' });
      
      expect(input).toBeInTheDocument();
      expect(button).toBeInTheDocument();
    });
  });

  describe('Responsive Design', () => {
    it('applies responsive classes correctly', () => {
      render(
        <Modal open={true}>
          <ModalContent className="sm:max-w-md md:max-w-lg">
            <ModalHeader>
              <ModalTitle>Responsive Modal</ModalTitle>
            </ModalHeader>
          </ModalContent>
        </Modal>
      );
      
      const content = screen.getByTestId('modal-content');
      expect(content).toHaveClass('sm:max-w-md', 'md:max-w-lg');
    });
  });
});
