import React, { useState, useMemo } from 'react';
import { <PERSON><PERSON>, Container, Alert } from 'react-bootstrap';
import { Calendar, momentLocalizer } from 'react-big-calendar';
import moment from 'moment';
import 'react-big-calendar/lib/css/react-big-calendar.css';
import { useEvents } from '../../hooks/useEvents';
import { EvaEvent } from '../../types/event';
import EventsTable from './EventsTable';
import EditEventModal from './EditEventModal';

export const departmentsOptions = [
    { value: 'Catering', label: 'Catering' },
    { value: 'Security', label: 'Security' },
    { value: 'AV Support', label: 'AV Support' },
    { value: 'Cleaning', label: 'Cleaning' },
];

export const emptyEvent: Omit<EvaEvent, 'id'> = {
    eventName: '',
    date: '',
    time: '',
    venue: '',
    organizer: '',
    departments: [],
    duration: 30,
};

const localizer = momentLocalizer(moment);

const Planner: React.FC = () => {
    const [showEditModal, setShowEditModal] = useState(false);
    const [editEventId, setEditEventId] = useState<number | string | null>(null);
    const [eventDetails, setEventDetails] = useState<Omit<EvaEvent, 'id'>>(emptyEvent);

    // Use the useEvents hook for all data operations
    const {
        events = [],
        isLoading,
        error,
        createEvent: createEventMutation,
        updateEvent: updateEventMutation,
        deleteEvent: deleteEventMutation,
    } = useEvents();

    // Handle changes for both form fields and departments
    const handleEventChange = (name: string, value: any) => {
        setEventDetails((prev) => ({ ...prev, [name]: value }));
    };

    // Handle form submission
    const handleFormSubmit = async (e: React.FormEvent, isEdit: boolean) => {
        e.preventDefault();

        try {
            if (isEdit && editEventId) {
                await updateEventMutation.mutateAsync({ ...eventDetails, id: editEventId });
            } else {
                await createEventMutation.mutateAsync(eventDetails);
            }
            resetForm();
        } catch (error) {
            console.error('Error saving event:', error);
        }
    };

    // Handle event deletion
    const handleDeleteEvent = async (id: number | string) => {
        if (window.confirm('Are you sure you want to delete this event?')) {
            try {
                await deleteEventMutation.mutateAsync(id);
            } catch (error) {
                console.error('Error deleting event:', error);
            }
        }
    };

    // Reset form and modal state
    const resetForm = () => {
        setEventDetails(emptyEvent);
        setShowEditModal(false);
        setEditEventId(null);
    };

    // Prepare events for the calendar
    const calendarEvents = useMemo(() => {
        return events.map((event) => ({
            ...event,
            start: new Date(`${event.date}T${event.time}`),
            end: new Date(new Date(`${event.date}T${event.time}`).getTime() + event.duration * 60 * 1000),
            title: `${event.eventName} at ${event.venue}`,
        }));
    }, [events]);

    // Handle editing an event
    const handleEditEvent = (event: EvaEvent) => {
        setEventDetails({
            eventName: event.eventName,
            date: event.date,
            time: event.time,
            venue: event.venue,
            organizer: event.organizer,
            departments: [...event.departments],
            duration: event.duration,
        });
        setEditEventId(event.id);
        setShowEditModal(true);
    };

    // Handle creating a new event
    const handleCreateEvent = () => {
        setEventDetails(emptyEvent);
        setEditEventId(null);
        setShowEditModal(true);
    };

    if (isLoading) {
        return <div className="text-center py-5">Loading events...</div>;
    }

    if (error) {
        return (
            <Alert variant="danger" className="m-3">
                Error loading events: {error instanceof Error ? error.message : 'Unknown error'}
            </Alert>
        );
    }

    return (
        <Container className="py-4">
            <h1 className="mb-4">Event Management</h1>

            <div className="mb-4">
                <Button variant="primary" onClick={handleCreateEvent}>
                    Create New Event
                </Button>
            </div>

            <div className="mb-4" style={{ height: 500 }}>
                <Calendar
                    localizer={localizer}
                    events={calendarEvents}
                    startAccessor="start"
                    endAccessor="end"
                    style={{ height: '100%' }}
                    onSelectEvent={(event: any) => handleEditEvent(event as unknown as EvaEvent)}
                />
            </div>

            <EventsTable
                events={events}
                onDelete={handleDeleteEvent}
                onEdit={handleEditEvent}
            />

            <EditEventModal
                show={showEditModal}
                onHide={resetForm}
                event={eventDetails}
                onChange={handleEventChange}
                onSubmit={(e: React.FormEvent) => handleFormSubmit(e, !!editEventId)}
                isEdit={!!editEventId}
                isLoading={createEventMutation.isPending || updateEventMutation.isPending}
                error={createEventMutation.error || updateEventMutation.error}
            />
        </Container>
    );
};

export default Planner;
