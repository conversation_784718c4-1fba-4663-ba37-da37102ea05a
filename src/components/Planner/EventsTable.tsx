import React from 'react';
import { Table, Button } from 'react-bootstrap';
import { EvaEvent } from '../../types/event';

interface EventsTableProps {
    events: EvaEvent[];
    onDelete: (id: number | string) => void;
    onEdit: (event: EvaEvent) => void;
}

const EventsTable: React.FC<EventsTableProps> = ({ events, onDelete, onEdit }) => {
    if (events.length === 0) {
        return <div className="text-muted text-center my-4">No events found. Create one to get started!</div>;
    }

    return (
        <div className="mt-4">
            <h3>Upcoming Events</h3>
            <Table striped bordered hover responsive>
                <thead>
                    <tr>
                        <th>Event Name</th>
                        <th>Date</th>
                        <th>Time</th>
                        <th>Venue</th>
                        <th>Organizer</th>
                        <th>Departments</th>
                        <th>Duration (min)</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {events.map((event) => (
                        <tr key={event.id}>
                            <td>{event.eventName}</td>
                            <td>{event.date}</td>
                            <td>{event.time}</td>
                            <td>{event.venue}</td>
                            <td>{event.organizer}</td>
                            <td>{event.departments.join(', ')}</td>
                            <td>{event.duration}</td>
                            <td>
                                <Button
                                    variant="outline-primary"
                                    size="sm"
                                    className="me-2"
                                    onClick={() => onEdit(event)}
                                >
                                    Edit
                                </Button>
                                <Button
                                    variant="outline-danger"
                                    size="sm"
                                    onClick={() => onDelete(event.id)}
                                >
                                    Delete
                                </Button>
                            </td>
                        </tr>
                    ))}
                </tbody>
            </Table>
        </div>
    );
};

export default EventsTable;
