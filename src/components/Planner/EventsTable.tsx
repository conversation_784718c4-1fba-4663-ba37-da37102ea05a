import React, { useState, useMemo } from 'react';
import { <PERSON>, But<PERSON>, Badge, Form, Row, Col } from 'react-bootstrap';
import { Edit, Trash2, Calendar, Clock, MapPin } from 'lucide-react';
import { EvaEvent, EventStatus, EventPriority } from '../../types/event';
import { formatDate, formatTime } from '../../utils/dateUtils';
import Pagination from '../Pagination';

interface EventsTableProps {
    events: EvaEvent[];
    onDelete: (id: number | string) => void;
    onEdit: (event: EvaEvent) => void;
    title?: string;
    itemsPerPage?: number;
}

const EventsTable: React.FC<EventsTableProps> = ({
    events,
    onDelete,
    onEdit,
    title = "Events",
    itemsPerPage = 10
}) => {
    const [currentPage, setCurrentPage] = useState(1);
    const [searchTerm, setSearchTerm] = useState('');
    const [statusFilter, setStatusFilter] = useState<string>('all');

    // Filter events based on search term and status
    const filteredEvents = useMemo(() => {
        return events.filter(event => {
            const matchesSearch =
                event.eventName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                event.venue.toLowerCase().includes(searchTerm.toLowerCase()) ||
                event.organizer.toLowerCase().includes(searchTerm.toLowerCase());

            const matchesStatus =
                statusFilter === 'all' ||
                event.status.toLowerCase() === statusFilter.toLowerCase();

            return matchesSearch && matchesStatus;
        });
    }, [events, searchTerm, statusFilter]);

    // Calculate pagination
    const totalPages = Math.ceil(filteredEvents.length / itemsPerPage);
    const startIndex = (currentPage - 1) * itemsPerPage;
    const paginatedEvents = filteredEvents.slice(startIndex, startIndex + itemsPerPage);

    const handlePageChange = (page: number) => {
        setCurrentPage(page);
    };

    // Get status badge
    const getStatusBadge = (status: EventStatus) => {
        const variants = {
            [EventStatus.DRAFT]: 'secondary',
            [EventStatus.PENDING]: 'warning',
            [EventStatus.CONFIRMED]: 'success',
            [EventStatus.REJECTED]: 'danger',
            [EventStatus.CANCELLED]: 'dark'
        };
        return <Badge bg={variants[status]}>{status.toUpperCase()}</Badge>;
    };

    // Get priority badge
    const getPriorityBadge = (priority: EventPriority) => {
        const variants = {
            [EventPriority.LOW]: 'info',
            [EventPriority.MEDIUM]: 'primary',
            [EventPriority.HIGH]: 'warning',
            [EventPriority.CRITICAL]: 'danger'
        };
        return <Badge bg={variants[priority]}>{priority.toUpperCase()}</Badge>;
    };

    if (events.length === 0) {
        return <div className="text-muted text-center my-4">No events found. Create one to get started!</div>;
    }

    return (
        <div className="mt-4">
            <div className="d-flex justify-content-between align-items-center mb-3">
                <h3>{title}</h3>

                <div className="d-flex gap-2">
                    <Form.Control
                        type="text"
                        placeholder="Search events..."
                        value={searchTerm}
                        onChange={(e) => {
                            setSearchTerm(e.target.value);
                            setCurrentPage(1); // Reset to first page on search
                        }}
                        style={{ maxWidth: '250px' }}
                    />

                    <Form.Select
                        value={statusFilter}
                        onChange={(e) => {
                            setStatusFilter(e.target.value);
                            setCurrentPage(1); // Reset to first page on filter change
                        }}
                        style={{ maxWidth: '150px' }}
                    >
                        <option value="all">All Statuses</option>
                        <option value={EventStatus.DRAFT}>Draft</option>
                        <option value={EventStatus.PENDING}>Pending</option>
                        <option value={EventStatus.CONFIRMED}>Confirmed</option>
                        <option value={EventStatus.REJECTED}>Rejected</option>
                        <option value={EventStatus.CANCELLED}>Cancelled</option>
                    </Form.Select>
                </div>
            </div>

            <Table hover responsive className="align-middle">
                <thead className="bg-light">
                    <tr>
                        <th>Event Name</th>
                        <th>Date & Time</th>
                        <th>Venue</th>
                        <th>Organizer</th>
                        <th>Status</th>
                        <th>Priority</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {paginatedEvents.map((event) => (
                        <tr key={event.id}>
                            <td>
                                <div className="fw-semibold">{event.eventName}</div>
                                {event.description && (
                                    <div className="text-muted small text-truncate" style={{ maxWidth: '200px' }}>
                                        {event.description}
                                    </div>
                                )}
                            </td>
                            <td>
                                <div className="d-flex align-items-center">
                                    <Calendar size={16} className="me-1 text-muted" />
                                    {formatDate(event.date)}
                                </div>
                                <div className="d-flex align-items-center">
                                    <Clock size={16} className="me-1 text-muted" />
                                    {formatTime(event.time)} ({event.duration} min)
                                </div>
                            </td>
                            <td>
                                <div className="d-flex align-items-center">
                                    <MapPin size={16} className="me-1 text-muted" />
                                    {event.venue}
                                </div>
                            </td>
                            <td>{event.organizer}</td>
                            <td>{getStatusBadge(event.status)}</td>
                            <td>{getPriorityBadge(event.priority)}</td>
                            <td>
                                <Button
                                    variant="outline-primary"
                                    size="sm"
                                    className="me-2 d-inline-flex align-items-center"
                                    onClick={() => onEdit(event)}
                                >
                                    <Edit size={14} className="me-1" />
                                    Edit
                                </Button>
                                <Button
                                    variant="outline-danger"
                                    size="sm"
                                    className="d-inline-flex align-items-center"
                                    onClick={() => onDelete(event.id)}
                                >
                                    <Trash2 size={14} className="me-1" />
                                    Delete
                                </Button>
                            </td>
                        </tr>
                    ))}
                </tbody>
            </Table>

            {/* Pagination */}
            <Pagination
                currentPage={currentPage}
                totalPages={totalPages}
                onPageChange={handlePageChange}
                itemsPerPage={itemsPerPage}
                totalItems={filteredEvents.length}
            />
        </div>
    );
};

export default EventsTable;
