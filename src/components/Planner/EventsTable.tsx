import React, { useState, useMemo } from 'react';
import { Edit, Trash2, Calendar, Clock, MapPin } from 'lucide-react';
import { EvaEvent, EventStatus, EventPriority } from '../../types/event';
import { formatDate, formatTime } from '../../utils/dateUtils';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { Input } from '../ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import TailwindPagination from '../TailwindPagination';

interface EventsTableProps {
    events: EvaEvent[];
    onDelete: (id: number | string) => void;
    onEdit: (event: EvaEvent) => void;
    title?: string;
    itemsPerPage?: number;
}

const EventsTable: React.FC<EventsTableProps> = ({
    events,
    onDelete,
    onEdit,
    title = "Events",
    itemsPerPage = 10
}) => {
    const [currentPage, setCurrentPage] = useState(1);
    const [searchTerm, setSearchTerm] = useState('');
    const [statusFilter, setStatusFilter] = useState<string>('all');

    // Filter events based on search term and status
    const filteredEvents = useMemo(() => {
        return events.filter(event => {
            const matchesSearch =
                event.eventName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                event.venue.toLowerCase().includes(searchTerm.toLowerCase()) ||
                event.organizer.toLowerCase().includes(searchTerm.toLowerCase());

            const matchesStatus =
                statusFilter === 'all' ||
                event.status.toLowerCase() === statusFilter.toLowerCase();

            return matchesSearch && matchesStatus;
        });
    }, [events, searchTerm, statusFilter]);

    // Calculate pagination
    const totalPages = Math.ceil(filteredEvents.length / itemsPerPage);
    const startIndex = (currentPage - 1) * itemsPerPage;
    const paginatedEvents = filteredEvents.slice(startIndex, startIndex + itemsPerPage);

    const handlePageChange = (page: number) => {
        setCurrentPage(page);
    };

    // Get status badge
    const getStatusBadge = (status: EventStatus) => {
        const variants = {
            [EventStatus.DRAFT]: 'bg-gray-100 text-gray-800',
            [EventStatus.PENDING]: 'bg-yellow-100 text-yellow-800',
            [EventStatus.CONFIRMED]: 'bg-green-100 text-green-800',
            [EventStatus.REJECTED]: 'bg-red-100 text-red-800',
            [EventStatus.CANCELLED]: 'bg-gray-100 text-gray-800'
        };
        return (
            <span className={`${variants[status]} px-2 py-1 rounded-full text-xs font-medium`}>
                {status.toUpperCase()}
            </span>
        );
    };

    // Get priority badge
    const getPriorityBadge = (priority: EventPriority) => {
        const variants = {
            [EventPriority.LOW]: 'bg-green-100 text-green-800',
            [EventPriority.MEDIUM]: 'bg-yellow-100 text-yellow-800',
            [EventPriority.HIGH]: 'bg-orange-100 text-orange-800',
            [EventPriority.CRITICAL]: 'bg-red-100 text-red-800'
        };
        return (
            <span className={`${variants[priority]} px-2 py-1 rounded-full text-xs font-medium`}>
                {priority.toUpperCase()}
            </span>
        );
    };

    if (events.length === 0) {
        return (
            <div className="bg-white rounded-lg border border-gray-200 shadow-sm p-8 text-center">
                <div className="text-gray-500">No events found. Create one to get started!</div>
            </div>
        );
    }

    return (
        <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
            <div className="p-6 border-b border-gray-200">
                <div className="flex justify-between items-center mb-4">
                    <h3 className="text-lg font-semibold text-brand-secondary">{title}</h3>
                </div>

                <div className="flex flex-col md:flex-row gap-4">
                    <div className="flex-1">
                        <Input
                            type="text"
                            placeholder="Search events..."
                            value={searchTerm}
                            onChange={(e) => {
                                setSearchTerm(e.target.value);
                                setCurrentPage(1); // Reset to first page on search
                            }}
                            className="w-full"
                        />
                    </div>
                    <div className="w-full md:w-48">
                        <Select value={statusFilter} onValueChange={(value) => {
                            setStatusFilter(value);
                            setCurrentPage(1); // Reset to first page on filter change
                        }}>
                            <SelectTrigger>
                                <SelectValue placeholder="All Statuses" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="all">All Statuses</SelectItem>
                                <SelectItem value={EventStatus.DRAFT}>Draft</SelectItem>
                                <SelectItem value={EventStatus.PENDING}>Pending</SelectItem>
                                <SelectItem value={EventStatus.CONFIRMED}>Confirmed</SelectItem>
                                <SelectItem value={EventStatus.REJECTED}>Rejected</SelectItem>
                                <SelectItem value={EventStatus.CANCELLED}>Cancelled</SelectItem>
                            </SelectContent>
                        </Select>
                    </div>
                </div>
            </div>

            <div className="overflow-x-auto">
                <table className="w-full">
                    <thead className="bg-gray-50">
                        <tr>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Event Name</th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date & Time</th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Venue</th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Organizer</th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Priority</th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                        {paginatedEvents.map((event) => (
                            <tr key={event.id} className="hover:bg-gray-50">
                                <td className="px-6 py-4 whitespace-nowrap">
                                    <div className="text-sm font-medium text-gray-900">{event.eventName}</div>
                                    {event.description && (
                                        <div className="text-sm text-gray-500 truncate max-w-xs">
                                            {event.description}
                                        </div>
                                    )}
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap">
                                    <div className="flex items-center text-sm text-gray-900">
                                        <Calendar size={16} className="mr-1 text-gray-400" />
                                        {formatDate(event.date)}
                                    </div>
                                    <div className="flex items-center text-sm text-gray-500">
                                        <Clock size={16} className="mr-1 text-gray-400" />
                                        {formatTime(event.time)} ({event.duration} min)
                                    </div>
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap">
                                    <div className="flex items-center text-sm text-gray-900">
                                        <MapPin size={16} className="mr-1 text-gray-400" />
                                        {event.venue}
                                    </div>
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{event.organizer}</td>
                                <td className="px-6 py-4 whitespace-nowrap">{getStatusBadge(event.status)}</td>
                                <td className="px-6 py-4 whitespace-nowrap">{getPriorityBadge(event.priority)}</td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div className="flex space-x-2">
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            className="text-brand-primary border-brand-primary hover:bg-brand-primary hover:text-white"
                                            onClick={() => onEdit(event)}
                                        >
                                            <Edit size={14} className="mr-1" />
                                            Edit
                                        </Button>
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            className="text-red-600 border-red-600 hover:bg-red-600 hover:text-white"
                                            onClick={() => onDelete(event.id)}
                                        >
                                            <Trash2 size={14} className="mr-1" />
                                            Delete
                                        </Button>
                                    </div>
                                </td>
                            </tr>
                        ))}
                    </tbody>
                </table>
            </div>

            {/* Pagination */}
            <div className="px-6 py-3 border-t border-gray-200">
                <TailwindPagination
                    currentPage={currentPage}
                    totalPages={totalPages}
                    onPageChange={handlePageChange}
                    itemsPerPage={itemsPerPage}
                    totalItems={filteredEvents.length}
                />
            </div>
        </div>
    );
};

export default EventsTable;
