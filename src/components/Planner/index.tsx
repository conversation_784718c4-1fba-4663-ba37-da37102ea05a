import React, { useState, useMemo } from 'react';
import { But<PERSON>, Container, Form, Row, Col } from 'react-bootstrap';
import { Calendar, momentLocalizer } from 'react-big-calendar';
import moment from 'moment';
import Select from 'react-select';
import 'react-big-calendar/lib/css/react-big-calendar.css';
import { useEvents } from '../../hooks/useEvents';
import { EvaEvent, EventStatus, EventPriority } from '../../types/event';
import { useAuth } from '../../contexts/AuthContext';
import EventsTable from './EventsTable';
import EditEventModal from './EditEventModal';
import CalendarExport from '../CalendarExport';
import AdminDashboard from '../AdminDashboard';
import EventSubmission from '../EventSubmission';

const localizer = momentLocalizer(moment);

export const departmentsOptions = [
    { value: 'Catering', label: 'Catering' },
    { value: 'Security', label: 'Security' },
    { value: 'AV Support', label: 'AV Support' },
    { value: 'Cleaning', label: 'Cleaning' },
];

export const emptyEvent: Omit<EvaEvent, 'id'> = {
    eventName: '',
    description: '',
    date: '',
    time: '',
    venue: '',
    organizer: '',
    organizerId: '',
    departmentId: '',
    departmentName: '',
    departments: [],
    duration: 30,
    status: EventStatus.DRAFT,
    priority: EventPriority.MEDIUM,
    maxAttendees: undefined,
    estimatedAttendees: undefined,
    budget: undefined,
    resources: [],
    conflicts: [],
    approvals: [],
    submissionDeadline: undefined,
    isRecurring: false,
    recurringPattern: undefined,
    tags: [],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    submittedAt: undefined,
    approvedAt: undefined,
    rejectedAt: undefined,
};

const Planner: React.FC = () => {
    const { user, permissions } = useAuth();
    const [showEditModal, setShowEditModal] = useState(false);
    const [editEventId, setEditEventId] = useState<number | string | null>(null);
    const [eventDetails, setEventDetails] = useState<Omit<EvaEvent, 'id'>>(emptyEvent);
    const [showExportModal, setShowExportModal] = useState(false);
    const [showAdminView, setShowAdminView] = useState(false);
    const [selectedEventForSubmission, setSelectedEventForSubmission] = useState<EvaEvent | null>(null);

    // Use the useEvents hook for all data operations
    const {
        events = [],
        isLoading, // eslint-disable-line @typescript-eslint/no-unused-vars
        error, // eslint-disable-line @typescript-eslint/no-unused-vars
        createEvent: createEventMutation,
        updateEvent: updateEventMutation,
        deleteEvent: deleteEventMutation,
    } = useEvents();

    // Handle changes for both form fields and departments
    const handleEventChange = (name: string, value: any) => {
        setEventDetails((prev) => ({ ...prev, [name]: value }));
    };

    // Handle form submission
    const handleFormSubmit = async (e: React.FormEvent, isEdit: boolean) => {
        e.preventDefault();

        try {
            if (isEdit && editEventId) {
                await updateEventMutation.mutateAsync({ ...eventDetails, id: editEventId });
            } else {
                await createEventMutation.mutateAsync(eventDetails);
            }
            resetForm();
        } catch (error) {
            console.error('Error saving event:', error);
        }
    };

    // Handle event deletion
    const handleDeleteEvent = async (id: number | string) => {
        const event = events.find(e => e.id === id);
        if (!event) return;

        if (!permissions.canDeleteEvent(event)) {
            alert('You do not have permission to delete this event.');
            return;
        }

        if (window.confirm('Are you sure you want to delete this event?')) {
            try {
                await deleteEventMutation.mutateAsync(id);
            } catch (error) {
                console.error('Error deleting event:', error);
            }
        }
    };

    // Reset form and modal state
    const resetForm = () => {
        setEventDetails(emptyEvent);
        setShowEditModal(false);
        setEditEventId(null);
    };

    // Prepare events for the calendar
    const calendarEvents = useMemo(() => {
        return events.map((event) => ({
            ...event,
            start: new Date(`${event.date}T${event.time}`),
            end: new Date(new Date(`${event.date}T${event.time}`).getTime() + event.duration * 60 * 1000),
            title: `${event.eventName} at ${event.venue}`,
        }));
    }, [events]);

    // Handle editing an event
    const handleEditEvent = (event: EvaEvent) => {
        // Check permissions
        if (!permissions.canEditEvent(event)) {
            alert('You do not have permission to edit this event.');
            return;
        }

        const { id, start, end, title, ...eventWithoutId } = event;
        setEventDetails(eventWithoutId);
        setEditEventId(event.id);
        setShowEditModal(true);
    };

    // Handle creating a new event
    const handleCreateEvent = () => {
        if (!permissions.canCreateEvent) {
            alert('You do not have permission to create events.');
            return;
        }

        const newEvent = {
            ...emptyEvent,
            organizer: user ? `${user.firstName} ${user.lastName}` : '',
            organizerId: user?.id?.toString() || '',
            departmentId: user?.departmentId || '',
            departmentName: user?.department?.name || '',
        };

        setEventDetails(newEvent);
        setEditEventId(null);
        setShowEditModal(true);
    };

    // Handle selecting a time slot on the calendar
    const handleSelectSlot = (slotInfo: any) => {
        if (!permissions.canCreateEvent) {
            alert('You do not have permission to create events.');
            return;
        }

        const startDate = new Date(slotInfo.start);
        const endDate = new Date(slotInfo.end);
        const duration = Math.round((endDate.getTime() - startDate.getTime()) / (1000 * 60));

        const newEvent = {
            ...emptyEvent,
            date: startDate.toISOString().split('T')[0],
            time: startDate.toTimeString().slice(0, 5),
            duration: duration > 0 ? duration : 60, // Default to 1 hour if same time slot
            organizer: user ? `${user.firstName} ${user.lastName}` : '',
            organizerId: user?.id?.toString() || '',
            departmentId: user?.departmentId || '',
            departmentName: user?.department?.name || '',
        };

        setEventDetails(newEvent);
        setEditEventId(null);
        setShowEditModal(true);
    };

    // Style events based on their status and priority
    const eventStyleGetter = (event: any) => {
        let backgroundColor = '#3174ad';
        let borderColor = '#3174ad';

        // Color by status
        switch (event.status) {
            case EventStatus.DRAFT:
                backgroundColor = '#6c757d';
                borderColor = '#6c757d';
                break;
            case EventStatus.PENDING:
                backgroundColor = '#ffc107';
                borderColor = '#ffc107';
                break;
            case EventStatus.CONFIRMED:
                backgroundColor = '#198754';
                borderColor = '#198754';
                break;
            case EventStatus.REJECTED:
                backgroundColor = '#dc3545';
                borderColor = '#dc3545';
                break;
            default:
                backgroundColor = '#3174ad';
                borderColor = '#3174ad';
        }

        // Add priority indicators
        if (event.priority === EventPriority.HIGH) {
            borderColor = '#fd7e14';
        } else if (event.priority === EventPriority.CRITICAL) {
            borderColor = '#dc3545';
        }

        return {
            style: {
                backgroundColor,
                borderColor,
                borderWidth: '2px',
                borderStyle: 'solid',
                color: 'white',
                borderRadius: '4px',
                opacity: event.status === EventStatus.REJECTED ? 0.6 : 1
            }
        };
    };

    // Style days based on events
    const dayStyleGetter = (date: Date) => {
        const dayEvents = events.filter(event => {
            const eventDate = new Date(event.date);
            return eventDate.toDateString() === date.toDateString();
        });

        if (dayEvents.length > 0) {
            const hasConflicts = dayEvents.some(event => event.conflicts && event.conflicts.length > 0);
            if (hasConflicts) {
                return {
                    style: {
                        backgroundColor: '#fff3cd',
                        border: '1px solid #ffeaa7'
                    }
                };
            }
        }

        return {};
    };

    // Style time slots
    const slotStyleGetter = (date: Date) => {
        const now = new Date();
        if (date < now) {
            return {
                style: {
                    backgroundColor: '#f8f9fa',
                    color: '#6c757d'
                }
            };
        }
        return {};
    };


    // const handleFormSubmit = (e: React.FormEvent, isEdit: boolean) => {
    //     e.preventDefault();
    //     const newEvent = { ...eventDetails, id: isEdit ? editEventId! : events.length + 1 };
    //     const updatedEvents = isEdit
    //         ? events.map((ev) => (ev.id === editEventId ? newEvent : ev))
    //         : [...events, newEvent];

    //     const updatedCalendarEvents = updatedEvents.map((ev) => {
    //         const start = new Date(`${ev.date}T${ev.time}`);
    //         const end = new Date(start.getTime() + ev.duration * 60 * 1000); // calculate end time
    //         console.log(`END TIME: ${end}`);
    //         return {
    //             ...ev,
    //             start,
    //             end,
    //             title: `${ev.eventName} at ${ev.venue}`,
    //         };
    //     });

    //     setEvents(updatedEvents);
    //     setCalendarEvents(updatedCalendarEvents);
    //     resetForm();
    // };

    // Show admin dashboard if user is admin and admin view is selected
    if (showAdminView && permissions.canApproveEvent) {
        return <AdminDashboard />;
    }

    return (
        <Container className="py-4">
            <div className="d-flex justify-content-between align-items-center mb-4">
                <h1 className="mb-0">Event Management</h1>
                <div className="d-flex gap-2">
                    {permissions.canApproveEvent && (
                        <Button
                            variant="outline-primary"
                            onClick={() => setShowAdminView(true)}
                        >
                            Admin Dashboard
                        </Button>
                    )}
                    {permissions.canExportCalendar && (
                        <Button
                            variant="outline-secondary"
                            onClick={() => setShowExportModal(true)}
                        >
                            Export Calendar
                        </Button>
                    )}
                </div>
            </div>

            <div className="mb-4">
                {permissions.canCreateEvent && (
                    <Button variant="primary" onClick={handleCreateEvent}>
                        Create New Event
                    </Button>
                )}
            </div>

            <div className="mb-4" style={{ height: 600 }}>
                <Calendar
                    localizer={localizer}
                    events={calendarEvents}
                    startAccessor="start"
                    endAccessor="end"
                    style={{ height: '100%' }}
                    onSelectEvent={(event: any) => handleEditEvent(event as unknown as EvaEvent)}
                    onSelectSlot={handleSelectSlot}
                    selectable={permissions.canCreateEvent}
                    popup
                    views={['month', 'week', 'day', 'agenda']}
                    defaultView="month"
                    step={30}
                    showMultiDayTimes
                    eventPropGetter={eventStyleGetter}
                    dayPropGetter={dayStyleGetter}
                    slotPropGetter={slotStyleGetter}
                    messages={{
                        next: "Next",
                        previous: "Previous",
                        today: "Today",
                        month: "Month",
                        week: "Week",
                        day: "Day",
                        agenda: "Agenda",
                        date: "Date",
                        time: "Time",
                        event: "Event",
                        noEventsInRange: "No events in this range",
                        showMore: (total: number) => `+${total} more`
                    }}
                />
            </div>

            <EventsTable
                events={events}
                onDelete={handleDeleteEvent}
                onEdit={handleEditEvent}
            />

            <EditEventModal
                show={showEditModal}
                onHide={resetForm}
                event={eventDetails}
                onChange={handleEventChange}
                onSubmit={(e: React.FormEvent) => handleFormSubmit(e, !!editEventId)}
                isEdit={!!editEventId}
                isLoading={createEventMutation.isPending || updateEventMutation.isPending}
                error={createEventMutation.error || updateEventMutation.error}
            />

            {/* Calendar Export Modal */}
            <CalendarExport
                show={showExportModal}
                onHide={() => setShowExportModal(false)}
            />

            {/* Event Submission Component */}
            {selectedEventForSubmission && (
                <EventSubmission
                    event={selectedEventForSubmission}
                    onSubmitted={() => setSelectedEventForSubmission(null)}
                />
            )}
        </Container>
    );
};

export default Planner;
export const EventForm = ({ eventDetails, handleEventChange, handleFormSubmit, departmentsOptions, isEdit }: any) => {
    return (
        <div className="d-flex justify-content-center rounded-4 p-5 my-4" style={{ backgroundColor: '#FFF100' }}>
            <h1 className="text-center my-3 fw-bolder display-6 text-dark">WithKalenda - Plan Together</h1>
            <Form onSubmit={(e) => handleFormSubmit(e, isEdit)}>
                <Row className="mb-3">
                    <Form.Group as={Col} controlId="formEventName">
                        <Form.Label className="d-flex justify-content-start fw-bolder fs-4 text-dark text-opacity-50">Event Name</Form.Label>
                        <Form.Control
                            type="text"
                            placeholder="Enter event name"
                            name="eventName"
                            value={eventDetails.eventName}
                            onChange={(e) => handleEventChange(e.target.name, e.target.value)}
                            required
                        />
                    </Form.Group>
                </Row>
                {/* Additional Fields */}
                <Row className="mb-3">
                    <Form.Group as={Col} controlId="formDate">
                        <Form.Label className="d-flex justify-content-start fw-bolder fs-4 text-dark text-opacity-50">Date</Form.Label>
                        <Form.Control
                            type="date"
                            name="date"
                            value={eventDetails.date}
                            onChange={(e) => handleEventChange(e.target.name, e.target.value)}
                            required
                        />
                    </Form.Group>
                    <Form.Group as={Col} controlId="formTime">
                        <Form.Label className="d-flex justify-content-start fw-bolder fs-4 text-dark text-opacity-50">Time</Form.Label>
                        <Form.Control
                            type="time"
                            name="time"
                            value={eventDetails.time}
                            onChange={(e) => handleEventChange(e.target.name, e.target.value)}
                            required
                        />
                    </Form.Group>
                </Row>
                {/* Venue and Organizer */}
                <Row className="mb-3">
                    <Form.Group as={Col} controlId="formVenue">
                        <Form.Label className="d-flex justify-content-start fw-bolder fs-4 text-dark text-opacity-50">Venue</Form.Label>
                        <Form.Control
                            type="text"
                            placeholder="Enter venue"
                            name="venue"
                            value={eventDetails.venue}
                            onChange={(e) => handleEventChange(e.target.name, e.target.value)}
                            required
                        />
                    </Form.Group>
                    <Form.Group as={Col} controlId="formOrganizer">
                        <Form.Label className="d-flex justify-content-start fw-bolder fs-4 text-dark text-opacity-50">Organizer</Form.Label>
                        <Form.Control
                            type="text"
                            placeholder="Enter organizer name"
                            name="organizer"
                            value={eventDetails.organizer}
                            onChange={(e) => handleEventChange(e.target.name, e.target.value)}
                            required
                        />
                    </Form.Group>
                </Row>
                <Row className="mb-3">
                    {/* Duration */}
                    <Form.Group as={Col} controlId="formDuration">
                        <Form.Label className="d-flex justify-content-start fw-bolder fs-4 text-dark text-opacity-50">Duration (Minutes)</Form.Label>
                        <Form.Control
                            type="number"
                            name="duration"
                            value={eventDetails.duration}
                            onChange={(e) => handleEventChange(e.target.name, e.target.value)}
                            required
                            min={1}
                        />
                    </Form.Group>
                    {/* Departments */}
                    <Form.Group as={Col} controlId="formDepartments">
                        <Form.Label className="d-flex justify-content-start fw-bolder fs-4 text-dark text-opacity-50">Departments</Form.Label>
                        <Select
                            isMulti
                            options={departmentsOptions}
                            value={departmentsOptions.filter((opt: { value: any; }) => eventDetails.departments.includes(opt.value))}
                            onChange={(selectedOptions) =>
                                handleEventChange('departments', selectedOptions.map((option) => option.value))
                            }
                        />
                    </Form.Group>
                </Row>

                <Button variant="dark" type="submit" className="mt-3 fw-bolder fs-6 w-25">
                    {isEdit ? 'Update Event' : 'Add Event'}
                </Button>
            </Form>
        </div>
    );
};
