import React, { useState, useMemo } from 'react';
import { Button, Container, Form, Row, Col } from 'react-bootstrap';
import { Calendar, momentLocalizer } from 'react-big-calendar';
import moment from 'moment';
import Select from 'react-select';
import 'react-big-calendar/lib/css/react-big-calendar.css';
import { useEvents } from '../../hooks/useEvents';
import { EvaEvent } from '../../types/event';
import EventsTable from './EventsTable';
import EditEventModal from './EditEventModal';

const localizer = momentLocalizer(moment);

export const departmentsOptions = [
    { value: 'Catering', label: 'Catering' },
    { value: 'Security', label: 'Security' },
    { value: 'AV Support', label: 'AV Support' },
    { value: 'Cleaning', label: 'Cleaning' },
];

export const emptyEvent: Omit<EvaEvent, 'id'> = {
    eventName: '',
    date: '',
    time: '',
    venue: '',
    organizer: '',
    departments: [],
    duration: 30, // default duration to 30 minutes
};

const Planner: React.FC = () => {
    const [showEditModal, setShowEditModal] = useState(false);
    const [editEventId, setEditEventId] = useState<number | null>(null);
    const [eventDetails, setEventDetails] = useState<Omit<EvaEvent, 'id'>>(emptyEvent);

    // Use the useEvents hook for all data operations
    const {
        events = [],
        isLoading,
        error,
        createEvent: createEventMutation,
        updateEvent: updateEventMutation,
        deleteEvent: deleteEventMutation,
    } = useEvents();

    // Handle changes for both form fields and departments
    const handleEventChange = (name: string, value: any) => {
        setEventDetails((prev) => ({ ...prev, [name]: value }));
    };

    // Handle form submission
    const handleFormSubmit = async (e: React.FormEvent, isEdit: boolean) => {
        e.preventDefault();

        try {
            if (isEdit && editEventId) {
                await updateEventMutation.mutateAsync({ ...eventDetails, id: editEventId });
            } else {
                await createEventMutation.mutateAsync(eventDetails);
            }
            resetForm();
        } catch (error) {
            console.error('Error saving event:', error);
        }
    };

    // Handle event deletion
    const handleDeleteEvent = async (id: number) => {
        if (window.confirm('Are you sure you want to delete this event?')) {
            try {
                await deleteEventMutation.mutateAsync(id);
            } catch (error) {
                console.error('Error deleting event:', error);
            }
        }
    };

    // Reset form and modal state
    const resetForm = () => {
        setEventDetails(emptyEvent);
        setShowEditModal(false);
        setEditEventId(null);
    };

    // Prepare events for the calendar
    const calendarEvents = useMemo(() => {
        return events.map((event) => ({
            ...event,
            start: new Date(`${event.date}T${event.time}`),
            end: new Date(new Date(`${event.date}T${event.time}`).getTime() + event.duration * 60 * 1000),
            title: `${event.eventName} at ${event.venue}`,
        }));
    }, [events]);

    // Handle editing an event
    const handleEditEvent = (event: EvaEvent) => {
        setEventDetails({
            eventName: event.eventName,
            date: event.date,
            time: event.time,
            venue: event.venue,
            organizer: event.organizer,
            departments: [...event.departments],
            duration: event.duration,
        });
        setEditEventId(event.id);
        setShowEditModal(true);
    };

    // Handle creating a new event
    const handleCreateEvent = () => {
        setEventDetails(emptyEvent);
        setEditEventId(null);
        setShowEditModal(true);
    };


    // const handleFormSubmit = (e: React.FormEvent, isEdit: boolean) => {
    //     e.preventDefault();
    //     const newEvent = { ...eventDetails, id: isEdit ? editEventId! : events.length + 1 };
    //     const updatedEvents = isEdit
    //         ? events.map((ev) => (ev.id === editEventId ? newEvent : ev))
    //         : [...events, newEvent];

    //     const updatedCalendarEvents = updatedEvents.map((ev) => {
    //         const start = new Date(`${ev.date}T${ev.time}`);
    //         const end = new Date(start.getTime() + ev.duration * 60 * 1000); // calculate end time
    //         console.log(`END TIME: ${end}`);
    //         return {
    //             ...ev,
    //             start,
    //             end,
    //             title: `${ev.eventName} at ${ev.venue}`,
    //         };
    //     });

    //     setEvents(updatedEvents);
    //     setCalendarEvents(updatedCalendarEvents);
    //     resetForm();
    // };

    return (
        <Container className="py-4">
            <h1 className="mb-4">Event Management</h1>

            <div className="mb-4">
                <Button variant="primary" onClick={handleCreateEvent}>
                    Create New Event
                </Button>
            </div>

            <div className="mb-4" style={{ height: 500 }}>
                <Calendar
                    localizer={localizer}
                    events={calendarEvents}
                    startAccessor="start"
                    endAccessor="end"
                    style={{ height: '100%' }}
                    onSelectEvent={(event: any) => handleEditEvent(event as unknown as EvaEvent)}
                />
            </div>

            <EventsTable
                events={events}
                onDelete={handleDeleteEvent}
                onEdit={handleEditEvent}
            />

            <EditEventModal
                show={showEditModal}
                onHide={resetForm}
                event={eventDetails}
                onChange={handleEventChange}
                onSubmit={(e: React.FormEvent) => handleFormSubmit(e, !!editEventId)}
                isEdit={!!editEventId}
                isLoading={createEventMutation.isPending || updateEventMutation.isPending}
                error={createEventMutation.error || updateEventMutation.error}
            />
        </Container>
    );
};

export default Planner;
export const EventForm = ({ eventDetails, handleEventChange, handleFormSubmit, departmentsOptions, isEdit }: any) => {
    return (
        <div className="d-flex justify-content-center rounded-4 p-5 my-4" style={{ backgroundColor: '#FFF100' }}>
            <h1 className="text-center my-3 fw-bolder display-6 text-dark">WithKalenda - Plan Together</h1>
            <Form onSubmit={(e) => handleFormSubmit(e, isEdit)}>
                <Row className="mb-3">
                    <Form.Group as={Col} controlId="formEventName">
                        <Form.Label className="d-flex justify-content-start fw-bolder fs-4 text-dark text-opacity-50">Event Name</Form.Label>
                        <Form.Control
                            type="text"
                            placeholder="Enter event name"
                            name="eventName"
                            value={eventDetails.eventName}
                            onChange={(e) => handleEventChange(e.target.name, e.target.value)}
                            required
                        />
                    </Form.Group>
                </Row>
                {/* Additional Fields */}
                <Row className="mb-3">
                    <Form.Group as={Col} controlId="formDate">
                        <Form.Label className="d-flex justify-content-start fw-bolder fs-4 text-dark text-opacity-50">Date</Form.Label>
                        <Form.Control
                            type="date"
                            name="date"
                            value={eventDetails.date}
                            onChange={(e) => handleEventChange(e.target.name, e.target.value)}
                            required
                        />
                    </Form.Group>
                    <Form.Group as={Col} controlId="formTime">
                        <Form.Label className="d-flex justify-content-start fw-bolder fs-4 text-dark text-opacity-50">Time</Form.Label>
                        <Form.Control
                            type="time"
                            name="time"
                            value={eventDetails.time}
                            onChange={(e) => handleEventChange(e.target.name, e.target.value)}
                            required
                        />
                    </Form.Group>
                </Row>
                {/* Venue and Organizer */}
                <Row className="mb-3">
                    <Form.Group as={Col} controlId="formVenue">
                        <Form.Label className="d-flex justify-content-start fw-bolder fs-4 text-dark text-opacity-50">Venue</Form.Label>
                        <Form.Control
                            type="text"
                            placeholder="Enter venue"
                            name="venue"
                            value={eventDetails.venue}
                            onChange={(e) => handleEventChange(e.target.name, e.target.value)}
                            required
                        />
                    </Form.Group>
                    <Form.Group as={Col} controlId="formOrganizer">
                        <Form.Label className="d-flex justify-content-start fw-bolder fs-4 text-dark text-opacity-50">Organizer</Form.Label>
                        <Form.Control
                            type="text"
                            placeholder="Enter organizer name"
                            name="organizer"
                            value={eventDetails.organizer}
                            onChange={(e) => handleEventChange(e.target.name, e.target.value)}
                            required
                        />
                    </Form.Group>
                </Row>
                <Row className="mb-3">
                    {/* Duration */}
                    <Form.Group as={Col} controlId="formDuration">
                        <Form.Label className="d-flex justify-content-start fw-bolder fs-4 text-dark text-opacity-50">Duration (Minutes)</Form.Label>
                        <Form.Control
                            type="number"
                            name="duration"
                            value={eventDetails.duration}
                            onChange={(e) => handleEventChange(e.target.name, e.target.value)}
                            required
                            min={1}
                        />
                    </Form.Group>
                    {/* Departments */}
                    <Form.Group as={Col} controlId="formDepartments">
                        <Form.Label className="d-flex justify-content-start fw-bolder fs-4 text-dark text-opacity-50">Departments</Form.Label>
                        <Select
                            isMulti
                            options={departmentsOptions}
                            value={departmentsOptions.filter((opt: { value: any; }) => eventDetails.departments.includes(opt.value))}
                            onChange={(selectedOptions) =>
                                handleEventChange('departments', selectedOptions.map((option) => option.value))
                            }
                        />
                    </Form.Group>
                </Row>

                <Button variant="dark" type="submit" className="mt-3 fw-bolder fs-6 w-25">
                    {isEdit ? 'Update Event' : 'Add Event'}
                </Button>
            </Form>
        </div>
    );
};
