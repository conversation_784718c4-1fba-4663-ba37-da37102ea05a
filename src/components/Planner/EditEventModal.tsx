import React, { useState, useEffect } from 'react';
import { Modal, Form, Button, Row, Col, Alert, Badge, InputGroup } from 'react-bootstrap';
import Select from 'react-select';
import { Calendar, Clock, MapPin, Users, AlertTriangle, Star } from 'lucide-react';
import { EvaEvent, EventPriority, EventStatus } from '../../types/event';
import { useDepartments } from '../../hooks/useDepartments';
import { useAuth } from '../../contexts/AuthContext';
import { departmentsOptions } from './index';

interface EditEventModalProps {
    show: boolean;
    onHide: () => void;
    event: Omit<EvaEvent, 'id'>;
    onChange: (name: string, value: any) => void;
    onSubmit: (e: React.FormEvent) => void;
    isEdit: boolean;
    isLoading: boolean;
    error: Error | null;
}

const EditEventModal: React.FC<EditEventModalProps> = ({
    show,
    onHide,
    event,
    onChange,
    onSubmit,
    isEdit,
    isLoading,
    error,
}) => {
    const { user, permissions } = useAuth();
    const { departments } = useDepartments();
    const [validationErrors, setValidationErrors] = useState<string[]>([]);

    // Validate form data
    useEffect(() => {
        const errors: string[] = [];

        if (!event.eventName.trim()) {
            errors.push('Event name is required');
        }

        if (!event.date) {
            errors.push('Date is required');
        } else {
            const eventDate = new Date(event.date);
            const today = new Date();
            today.setHours(0, 0, 0, 0);

            if (eventDate < today) {
                errors.push('Event date cannot be in the past');
            }
        }

        if (!event.time) {
            errors.push('Time is required');
        }

        if (!event.venue.trim()) {
            errors.push('Venue is required');
        }

        if (event.duration < 15) {
            errors.push('Duration must be at least 15 minutes');
        }

        if (event.maxAttendees && event.estimatedAttendees && event.estimatedAttendees > event.maxAttendees) {
            errors.push('Estimated attendees cannot exceed maximum attendees');
        }

        setValidationErrors(errors);
    }, [event]);

    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
        const { name, value, type } = e.target;
        const finalValue = type === 'number' ? (value ? parseInt(value, 10) : '') : value;
        onChange(name, finalValue);
    };

    const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { name, checked } = e.target;
        onChange(name, checked);
    };

    const handleDepartmentChange = (selectedOptions: any) => {
        onChange('departments', selectedOptions ? selectedOptions.map((option: any) => option.value) : []);
    };

    const handleResourcesChange = (selectedOptions: any) => {
        onChange('resources', selectedOptions ? selectedOptions.map((option: any) => option.value) : []);
    };

    const handleTagsChange = (selectedOptions: any) => {
        onChange('tags', selectedOptions ? selectedOptions.map((option: any) => option.value) : []);
    };

    const selectedDepartments = departmentsOptions.filter(option =>
        event.departments.includes(option.value)
    );

    const resourceOptions = [
        { value: 'projector', label: 'Projector' },
        { value: 'microphone', label: 'Microphone' },
        { value: 'speakers', label: 'Speakers' },
        { value: 'whiteboard', label: 'Whiteboard' },
        { value: 'catering', label: 'Catering' },
        { value: 'parking', label: 'Parking' },
        { value: 'wifi', label: 'WiFi' },
        { value: 'recording', label: 'Recording Equipment' }
    ];

    const selectedResources = resourceOptions.filter(option =>
        event.resources?.includes(option.value)
    );

    const tagOptions = [
        { value: 'meeting', label: 'Meeting' },
        { value: 'training', label: 'Training' },
        { value: 'workshop', label: 'Workshop' },
        { value: 'conference', label: 'Conference' },
        { value: 'social', label: 'Social Event' },
        { value: 'presentation', label: 'Presentation' },
        { value: 'team-building', label: 'Team Building' },
        { value: 'client', label: 'Client Event' }
    ];

    const selectedTags = tagOptions.filter(option =>
        event.tags?.includes(option.value)
    );

    const priorityOptions = [
        { value: EventPriority.LOW, label: 'Low', color: 'info' },
        { value: EventPriority.MEDIUM, label: 'Medium', color: 'primary' },
        { value: EventPriority.HIGH, label: 'High', color: 'warning' },
        { value: EventPriority.CRITICAL, label: 'Critical', color: 'danger' }
    ];

    const userDepartment = departments.find(dept => dept.id === user?.departmentId);

    return (
        <Modal show={show} onHide={onHide} size="xl" centered>
            <Modal.Header closeButton>
                <Modal.Title>
                    <Calendar className="me-2" />
                    {isEdit ? 'Edit' : 'Create New'} Event
                </Modal.Title>
            </Modal.Header>
            <Form onSubmit={onSubmit}>
                <Modal.Body style={{ maxHeight: '70vh', overflowY: 'auto' }}>
                    {/* Error Messages */}
                    {error && (
                        <Alert variant="danger">
                            <AlertTriangle className="me-2" size={16} />
                            {error.message || 'An error occurred while saving the event.'}
                        </Alert>
                    )}

                    {/* Validation Errors */}
                    {validationErrors.length > 0 && (
                        <Alert variant="warning">
                            <AlertTriangle className="me-2" size={16} />
                            <strong>Please fix the following issues:</strong>
                            <ul className="mb-0 mt-2">
                                {validationErrors.map((error, index) => (
                                    <li key={index}>{error}</li>
                                ))}
                            </ul>
                        </Alert>
                    )}

                    {/* Event Status */}
                    {isEdit && (
                        <Alert variant="info" className="mb-3">
                            <strong>Status:</strong>
                            <Badge
                                bg={
                                    event.status === EventStatus.DRAFT ? 'secondary' :
                                        event.status === EventStatus.PENDING ? 'warning' :
                                            event.status === EventStatus.CONFIRMED ? 'success' :
                                                'danger'
                                }
                                className="ms-2"
                            >
                                {event.status?.toUpperCase()}
                            </Badge>
                        </Alert>
                    )}

                    {/* Basic Information */}
                    <h6 className="mb-3 text-primary">Basic Information</h6>

                    <Row className="mb-3">
                        <Form.Group as={Col} controlId="formEventName">
                            <Form.Label>Event Name *</Form.Label>
                            <Form.Control
                                type="text"
                                name="eventName"
                                value={event.eventName}
                                onChange={handleChange}
                                required
                                placeholder="Enter event name"
                            />
                        </Form.Group>

                        <Form.Group as={Col} controlId="formPriority">
                            <Form.Label>Priority</Form.Label>
                            <Form.Select
                                name="priority"
                                value={event.priority}
                                onChange={handleChange}
                            >
                                {priorityOptions.map(option => (
                                    <option key={option.value} value={option.value}>
                                        {option.label}
                                    </option>
                                ))}
                            </Form.Select>
                        </Form.Group>
                    </Row>

                    <Form.Group className="mb-3" controlId="formDescription">
                        <Form.Label>Description</Form.Label>
                        <Form.Control
                            as="textarea"
                            rows={3}
                            name="description"
                            value={event.description || ''}
                            onChange={handleChange}
                            placeholder="Describe the event purpose, agenda, or any important details..."
                        />
                    </Form.Group>

                    {/* Date & Time Section */}
                    <h6 className="mb-3 text-primary">
                        <Clock className="me-2" size={16} />
                        Date & Time
                    </h6>

                    <Row className="mb-3">
                        <Form.Group as={Col} controlId="formDate">
                            <Form.Label>Date *</Form.Label>
                            <Form.Control
                                type="date"
                                name="date"
                                value={event.date}
                                onChange={handleChange}
                                required
                                min={new Date().toISOString().split('T')[0]}
                            />
                        </Form.Group>

                        <Form.Group as={Col} controlId="formTime">
                            <Form.Label>Time *</Form.Label>
                            <Form.Control
                                type="time"
                                name="time"
                                value={event.time}
                                onChange={handleChange}
                                required
                            />
                        </Form.Group>

                        <Form.Group as={Col} controlId="formDuration">
                            <Form.Label>Duration (minutes) *</Form.Label>
                            <Form.Control
                                type="number"
                                name="duration"
                                min="15"
                                max="1440"
                                value={event.duration}
                                onChange={handleChange}
                                required
                            />
                            <Form.Text className="text-muted">
                                Minimum 15 minutes, maximum 24 hours
                            </Form.Text>
                        </Form.Group>
                    </Row>

                    {/* Location & Organizer */}
                    <h6 className="mb-3 text-primary">
                        <MapPin className="me-2" size={16} />
                        Location & Organizer
                    </h6>

                    <Row className="mb-3">
                        <Form.Group as={Col} controlId="formVenue">
                            <Form.Label>Venue *</Form.Label>
                            <Form.Control
                                type="text"
                                name="venue"
                                value={event.venue}
                                onChange={handleChange}
                                required
                                placeholder="Conference Room A, Main Building, etc."
                            />
                        </Form.Group>

                        <Form.Group as={Col} controlId="formOrganizer">
                            <Form.Label>Organizer *</Form.Label>
                            <Form.Control
                                type="text"
                                name="organizer"
                                value={event.organizer}
                                onChange={handleChange}
                                required
                                disabled={false}
                            />
                        </Form.Group>
                    </Row>

                    {/* Departments & Attendees */}
                    <h6 className="mb-3 text-primary">
                        <Users className="me-2" size={16} />
                        Departments & Attendees
                    </h6>

                    <Form.Group className="mb-3" controlId="formDepartments">
                        <Form.Label>Departments Involved</Form.Label>
                        <Select
                            isMulti
                            name="departments"
                            options={departmentsOptions}
                            value={selectedDepartments}
                            onChange={handleDepartmentChange}
                            className="basic-multi-select"
                            classNamePrefix="select"
                            placeholder="Select departments..."
                        />
                        <Form.Text className="text-muted">
                            Select all departments that will be involved in this event
                        </Form.Text>
                    </Form.Group>

                    <Row className="mb-3">
                        <Form.Group as={Col} controlId="formEstimatedAttendees">
                            <Form.Label>Estimated Attendees</Form.Label>
                            <Form.Control
                                type="number"
                                name="estimatedAttendees"
                                min="1"
                                value={event.estimatedAttendees || ''}
                                onChange={handleChange}
                                placeholder="Expected number of attendees"
                            />
                        </Form.Group>

                        <Form.Group as={Col} controlId="formMaxAttendees">
                            <Form.Label>Maximum Attendees</Form.Label>
                            <Form.Control
                                type="number"
                                name="maxAttendees"
                                min="1"
                                value={event.maxAttendees || ''}
                                onChange={handleChange}
                                placeholder="Venue capacity limit"
                            />
                        </Form.Group>

                        <Form.Group as={Col} controlId="formBudget">
                            <Form.Label>Budget ($)</Form.Label>
                            <InputGroup>
                                <InputGroup.Text>$</InputGroup.Text>
                                <Form.Control
                                    type="number"
                                    name="budget"
                                    min="0"
                                    step="0.01"
                                    value={event.budget || ''}
                                    onChange={handleChange}
                                    placeholder="0.00"
                                />
                            </InputGroup>
                        </Form.Group>
                    </Row>

                    {/* Resources & Tags */}
                    <h6 className="mb-3 text-primary">
                        <Star className="me-2" size={16} />
                        Resources & Tags
                    </h6>

                    <Form.Group className="mb-3" controlId="formResources">
                        <Form.Label>Required Resources</Form.Label>
                        <Select
                            isMulti
                            name="resources"
                            options={resourceOptions}
                            value={selectedResources}
                            onChange={handleResourcesChange}
                            className="basic-multi-select"
                            classNamePrefix="select"
                            placeholder="Select required resources..."
                        />
                    </Form.Group>

                    <Form.Group className="mb-3" controlId="formTags">
                        <Form.Label>Tags</Form.Label>
                        <Select
                            isMulti
                            name="tags"
                            options={tagOptions}
                            value={selectedTags}
                            onChange={handleTagsChange}
                            className="basic-multi-select"
                            classNamePrefix="select"
                            placeholder="Add tags to categorize this event..."
                        />
                    </Form.Group>

                    {/* Recurring Event */}
                    <Form.Group className="mb-3">
                        <Form.Check
                            type="checkbox"
                            name="isRecurring"
                            label="This is a recurring event"
                            checked={event.isRecurring || false}
                            onChange={handleCheckboxChange}
                        />
                    </Form.Group>

                    {event.isRecurring && (
                        <Form.Group className="mb-3" controlId="formRecurringPattern">
                            <Form.Label>Recurring Pattern</Form.Label>
                            <Form.Select
                                name="recurringPattern"
                                value={event.recurringPattern || ''}
                                onChange={handleChange}
                            >
                                <option value="">Select pattern</option>
                                <option value="daily">Daily</option>
                                <option value="weekly">Weekly</option>
                                <option value="monthly">Monthly</option>
                                <option value="yearly">Yearly</option>
                            </Form.Select>
                        </Form.Group>
                    )}
                </Modal.Body>
                <Modal.Footer className="d-flex justify-content-between">
                    <div>
                        {validationErrors.length > 0 && (
                            <small className="text-danger">
                                <AlertTriangle size={14} className="me-1" />
                                Please fix {validationErrors.length} validation error{validationErrors.length > 1 ? 's' : ''}
                            </small>
                        )}
                    </div>
                    <div>
                        <Button variant="secondary" onClick={onHide} disabled={isLoading} className="me-2">
                            Cancel
                        </Button>
                        <Button
                            variant="primary"
                            type="submit"
                            disabled={isLoading || validationErrors.length > 0}
                        >
                            {isLoading ? 'Saving...' : (isEdit ? 'Update Event' : 'Create Event')}
                        </Button>
                    </div>
                </Modal.Footer>
            </Form>
        </Modal>
    );
};

export default EditEventModal;
