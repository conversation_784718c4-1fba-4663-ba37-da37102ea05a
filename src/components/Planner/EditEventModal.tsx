import React from 'react';
import { <PERSON><PERSON>, <PERSON>, Button, Row, Col } from 'react-bootstrap';
import Select from 'react-select';
import { EvaEvent } from '../../types/event';
import { departmentsOptions } from './index';

interface EditEventModalProps {
    show: boolean;
    onHide: () => void;
    event: Omit<EvaEvent, 'id'>;
    onChange: (name: string, value: any) => void;
    onSubmit: (e: React.FormEvent) => void;
    isEdit: boolean;
    isLoading: boolean;
    error: Error | null;
}

const EditEventModal: React.FC<EditEventModalProps> = ({
    show,
    onHide,
    event,
    onChange,
    onSubmit,
    isEdit,
    isLoading,
    error,
}) => {
    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
        const { name, value } = e.target;
        onChange(name, value);
    };

    const handleDepartmentChange = (selectedOptions: any) => {
        onChange('departments', selectedOptions ? selectedOptions.map((option: any) => option.value) : []);
    };

    const selectedDepartments = departmentsOptions.filter(option => 
        event.departments.includes(option.value)
    );

    return (
        <Modal show={show} onHide={onHide} size="lg" centered>
            <Modal.Header closeButton>
                <Modal.Title>{isEdit ? 'Edit' : 'Create New'} Event</Modal.Title>
            </Modal.Header>
            <Form onSubmit={onSubmit}>
                <Modal.Body>
                    {error && (
                        <div className="alert alert-danger" role="alert">
                            {error.message || 'An error occurred while saving the event.'}
                        </div>
                    )}
                    
                    <Row className="mb-3">
                        <Form.Group as={Col} controlId="formEventName">
                            <Form.Label>Event Name</Form.Label>
                            <Form.Control
                                type="text"
                                name="eventName"
                                value={event.eventName}
                                onChange={handleChange}
                                required
                            />
                        </Form.Group>

                        <Form.Group as={Col} controlId="formOrganizer">
                            <Form.Label>Organizer</Form.Label>
                            <Form.Control
                                type="text"
                                name="organizer"
                                value={event.organizer}
                                onChange={handleChange}
                                required
                            />
                        </Form.Group>
                    </Row>

                    <Row className="mb-3">
                        <Form.Group as={Col} controlId="formDate">
                            <Form.Label>Date</Form.Label>
                            <Form.Control
                                type="date"
                                name="date"
                                value={event.date}
                                onChange={handleChange}
                                required
                            />
                        </Form.Group>

                        <Form.Group as={Col} controlId="formTime">
                            <Form.Label>Time</Form.Label>
                            <Form.Control
                                type="time"
                                name="time"
                                value={event.time}
                                onChange={handleChange}
                                required
                            />
                        </Form.Group>

                        <Form.Group as={Col} controlId="formDuration">
                            <Form.Label>Duration (minutes)</Form.Label>
                            <Form.Control
                                type="number"
                                name="duration"
                                min="1"
                                value={event.duration}
                                onChange={handleChange}
                                required
                            />
                        </Form.Group>
                    </Row>

                    <Form.Group className="mb-3" controlId="formVenue">
                        <Form.Label>Venue</Form.Label>
                        <Form.Control
                            type="text"
                            name="venue"
                            value={event.venue}
                            onChange={handleChange}
                            required
                        />
                    </Form.Group>

                    <Form.Group className="mb-3" controlId="formDepartments">
                        <Form.Label>Departments</Form.Label>
                        <Select
                            isMulti
                            name="departments"
                            options={departmentsOptions}
                            value={selectedDepartments}
                            onChange={handleDepartmentChange}
                            className="basic-multi-select"
                            classNamePrefix="select"
                        />
                    </Form.Group>
                </Modal.Body>
                <Modal.Footer>
                    <Button variant="secondary" onClick={onHide} disabled={isLoading}>
                        Cancel
                    </Button>
                    <Button variant="primary" type="submit" disabled={isLoading}>
                        {isLoading ? 'Saving...' : 'Save Changes'}
                    </Button>
                </Modal.Footer>
            </Form>
        </Modal>
    );
};

export default EditEventModal;
