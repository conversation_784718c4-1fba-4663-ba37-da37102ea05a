import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
// Jest is used instead of vitest
import DeadlineManagement from '../index';
import { AuthProvider } from '../../../contexts/AuthContext';
import { sampleUsers } from '../../../data/sampleUsers';

// Mock the useAuth hook
const mockUseAuth = {
  user: sampleUsers[0], // Admin user
  permissions: {
    canSetDeadlines: true,
    canCreateEvent: true,
    canEditEvent: () => true,
    canDeleteEvent: () => true,
    canApproveEvent: true,
    canRejectEvent: true,
    canViewAllEvents: true,
    canManageDepartments: true,
    canManageUsers: true,
    canExportCalendar: true,
    canViewAnalytics: true
  },
  isAuthenticated: true,
  login: jest.fn(),
  logout: jest.fn(),
  isLoading: false
};

jest.mock('../../../contexts/AuthContext', () => ({
  useAuth: () => mockUseAuth,
  AuthProvider: ({ children }: { children: React.ReactNode }) => <div>{children}</div>
}));

// Mock window.confirm
const mockConfirm = jest.fn();
Object.defineProperty(window, 'confirm', {
  value: mockConfirm,
  writable: true
});

const createTestQueryClient = () => {
  return new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false }
    }
  });
};

const renderWithProviders = (component: React.ReactElement) => {
  const queryClient = createTestQueryClient();
  return render(
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        {component}
      </AuthProvider>
    </QueryClientProvider>
  );
};

describe('DeadlineManagement', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders deadline management component', () => {
    renderWithProviders(<DeadlineManagement />);
    
    expect(screen.getByText('Submission Deadlines')).toBeInTheDocument();
    expect(screen.getByText('Create New Deadline')).toBeInTheDocument();
  });

  it('displays existing deadlines in table', () => {
    renderWithProviders(<DeadlineManagement />);
    
    expect(screen.getByText('Q2 2024 Event Submissions')).toBeInTheDocument();
    expect(screen.getByText('Q3 2024 Event Submissions')).toBeInTheDocument();
  });

  it('shows active/inactive status badges', () => {
    renderWithProviders(<DeadlineManagement />);
    
    const activeBadges = screen.getAllByText('Active');
    const inactiveBadges = screen.getAllByText('Inactive');
    
    expect(activeBadges.length).toBeGreaterThan(0);
    expect(inactiveBadges.length).toBeGreaterThan(0);
  });

  it('opens create modal when create button is clicked', async () => {
    renderWithProviders(<DeadlineManagement />);
    
    const createButton = screen.getByText('Create New Deadline');
    fireEvent.click(createButton);
    
    await waitFor(() => {
      expect(screen.getByText('Create Submission Deadline')).toBeInTheDocument();
    });
  });

  it('opens edit modal when edit button is clicked', async () => {
    renderWithProviders(<DeadlineManagement />);
    
    const editButtons = screen.getAllByText('Edit');
    fireEvent.click(editButtons[0]);
    
    await waitFor(() => {
      expect(screen.getByText('Edit Submission Deadline')).toBeInTheDocument();
    });
  });

  it('fills form with existing data when editing', async () => {
    renderWithProviders(<DeadlineManagement />);
    
    const editButtons = screen.getAllByText('Edit');
    fireEvent.click(editButtons[0]);
    
    await waitFor(() => {
      expect(screen.getByDisplayValue('Q2 2024 Event Submissions')).toBeInTheDocument();
      expect(screen.getByDisplayValue('2024')).toBeInTheDocument();
    });
  });

  it('creates new deadline when form is submitted', async () => {
    renderWithProviders(<DeadlineManagement />);
    
    const createButton = screen.getByText('Create New Deadline');
    fireEvent.click(createButton);
    
    await waitFor(() => {
      expect(screen.getByText('Create Submission Deadline')).toBeInTheDocument();
    });
    
    // Fill form
    const yearInput = screen.getByLabelText('Year');
    fireEvent.change(yearInput, { target: { value: '2025' } });
    
    const descriptionInput = screen.getByLabelText('Description');
    fireEvent.change(descriptionInput, { target: { value: 'Q1 2025 Event Submissions' } });
    
    const deadlineInput = screen.getByLabelText('Deadline');
    fireEvent.change(deadlineInput, { target: { value: '2025-03-31T23:59' } });
    
    // Submit form
    const submitButton = screen.getByText('Create Deadline');
    fireEvent.click(submitButton);
    
    await waitFor(() => {
      expect(screen.getByText('Q1 2025 Event Submissions')).toBeInTheDocument();
    });
  });

  it('updates existing deadline when form is submitted', async () => {
    renderWithProviders(<DeadlineManagement />);
    
    const editButtons = screen.getAllByText('Edit');
    fireEvent.click(editButtons[0]);
    
    await waitFor(() => {
      expect(screen.getByText('Edit Submission Deadline')).toBeInTheDocument();
    });
    
    // Update description
    const descriptionInput = screen.getByDisplayValue('Q2 2024 Event Submissions');
    fireEvent.change(descriptionInput, { target: { value: 'Updated Q2 2024 Event Submissions' } });
    
    // Submit form
    const submitButton = screen.getByText('Update Deadline');
    fireEvent.click(submitButton);
    
    await waitFor(() => {
      expect(screen.getByText('Updated Q2 2024 Event Submissions')).toBeInTheDocument();
    });
  });

  it('deletes deadline when delete button is clicked and confirmed', async () => {
    mockConfirm.mockReturnValue(true);
    
    renderWithProviders(<DeadlineManagement />);
    
    const deleteButtons = screen.getAllByText('Delete');
    fireEvent.click(deleteButtons[0]);
    
    expect(mockConfirm).toHaveBeenCalledWith('Are you sure you want to delete this deadline?');
    
    await waitFor(() => {
      expect(screen.queryByText('Q2 2024 Event Submissions')).not.toBeInTheDocument();
    });
  });

  it('does not delete deadline when delete is cancelled', async () => {
    mockConfirm.mockReturnValue(false);
    
    renderWithProviders(<DeadlineManagement />);
    
    const deleteButtons = screen.getAllByText('Delete');
    fireEvent.click(deleteButtons[0]);
    
    expect(mockConfirm).toHaveBeenCalled();
    expect(screen.getByText('Q2 2024 Event Submissions')).toBeInTheDocument();
  });

  it('closes modal when cancel button is clicked', async () => {
    renderWithProviders(<DeadlineManagement />);
    
    const createButton = screen.getByText('Create New Deadline');
    fireEvent.click(createButton);
    
    await waitFor(() => {
      expect(screen.getByText('Create Submission Deadline')).toBeInTheDocument();
    });
    
    const cancelButton = screen.getByText('Cancel');
    fireEvent.click(cancelButton);
    
    await waitFor(() => {
      expect(screen.queryByText('Create Submission Deadline')).not.toBeInTheDocument();
    });
  });

  it('validates required fields', async () => {
    renderWithProviders(<DeadlineManagement />);
    
    const createButton = screen.getByText('Create New Deadline');
    fireEvent.click(createButton);
    
    await waitFor(() => {
      expect(screen.getByText('Create Submission Deadline')).toBeInTheDocument();
    });
    
    // Try to submit without filling required fields
    const submitButton = screen.getByText('Create Deadline');
    fireEvent.click(submitButton);
    
    // Form should not submit (modal should still be open)
    expect(screen.getByText('Create Submission Deadline')).toBeInTheDocument();
  });

  it('toggles active status checkbox', async () => {
    renderWithProviders(<DeadlineManagement />);
    
    const createButton = screen.getByText('Create New Deadline');
    fireEvent.click(createButton);
    
    await waitFor(() => {
      expect(screen.getByText('Create Submission Deadline')).toBeInTheDocument();
    });
    
    const activeCheckbox = screen.getByLabelText('Active (allow submissions until this deadline)');
    expect(activeCheckbox).toBeChecked(); // Should be checked by default
    
    fireEvent.click(activeCheckbox);
    expect(activeCheckbox).not.toBeChecked();
  });

  it('shows permission error for non-admin users', () => {
    const nonAdminAuth = {
      ...mockUseAuth,
      permissions: {
        ...mockUseAuth.permissions,
        canSetDeadlines: false
      }
    };

    jest.fn().mockImplementation(require('../../../contexts/AuthContext').useAuth).mockReturnValue(nonAdminAuth);
    
    renderWithProviders(<DeadlineManagement />);
    
    expect(screen.getByText('You do not have permission to manage submission deadlines.')).toBeInTheDocument();
    expect(screen.queryByText('Create New Deadline')).not.toBeInTheDocument();
  });

  it('displays formatted dates correctly', () => {
    renderWithProviders(<DeadlineManagement />);
    
    // Check that dates are displayed in a readable format
    expect(screen.getByText(/Mar 31, 2024/)).toBeInTheDocument();
    expect(screen.getByText(/Jun 30, 2024/)).toBeInTheDocument();
  });

  it('shows past deadline indicator', () => {
    renderWithProviders(<DeadlineManagement />);
    
    // The component should show some indication for past deadlines
    // This depends on the current date vs the deadline dates
    const tableRows = screen.getAllByRole('row');
    expect(tableRows.length).toBeGreaterThan(1); // Header + data rows
  });

  it('sets default year to current year for new deadlines', async () => {
    renderWithProviders(<DeadlineManagement />);
    
    const createButton = screen.getByText('Create New Deadline');
    fireEvent.click(createButton);
    
    await waitFor(() => {
      const yearInput = screen.getByLabelText('Year');
      expect(yearInput).toHaveValue(new Date().getFullYear());
    });
  });

  it('handles form input changes correctly', async () => {
    renderWithProviders(<DeadlineManagement />);
    
    const createButton = screen.getByText('Create New Deadline');
    fireEvent.click(createButton);
    
    await waitFor(() => {
      expect(screen.getByText('Create Submission Deadline')).toBeInTheDocument();
    });
    
    const yearInput = screen.getByLabelText('Year');
    const descriptionInput = screen.getByLabelText('Description');
    const deadlineInput = screen.getByLabelText('Deadline');
    
    fireEvent.change(yearInput, { target: { value: '2025' } });
    fireEvent.change(descriptionInput, { target: { value: 'Test Description' } });
    fireEvent.change(deadlineInput, { target: { value: '2025-12-31T23:59' } });
    
    expect(yearInput).toHaveValue(2025);
    expect(descriptionInput).toHaveValue('Test Description');
    expect(deadlineInput).toHaveValue('2025-12-31T23:59');
  });
});
