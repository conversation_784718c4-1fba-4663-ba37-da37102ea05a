import React, { useState } from 'react';
import { <PERSON>, <PERSON>, But<PERSON>, Alert, <PERSON>, Badge, Modal } from 'react-bootstrap';
import { Calendar, Clock, Plus, Edit, Trash2, AlertTriangle } from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';
import { SubmissionDeadline } from '../../types/event';
import { formatDate, formatDateTime, isPastDate } from '../../utils/dateUtils';

const DeadlineManagement: React.FC = () => {
  const { permissions } = useAuth();
  
  // Mock deadlines data - in real app, this would come from an API
  const [deadlines, setDeadlines] = useState<SubmissionDeadline[]>([
    {
      id: '1',
      year: 2024,
      deadline: '2024-03-31T23:59:59',
      description: 'Q2 2024 Event Submissions',
      isActive: true,
      createdBy: 'admin',
      createdAt: '2024-01-01T00:00:00'
    },
    {
      id: '2',
      year: 2024,
      deadline: '2024-06-30T23:59:59',
      description: 'Q3 2024 Event Submissions',
      isActive: false,
      createdBy: 'admin',
      createdAt: '2024-01-01T00:00:00'
    }
  ]);

  const [showModal, setShowModal] = useState(false);
  const [editingDeadline, setEditingDeadline] = useState<SubmissionDeadline | null>(null);
  const [formData, setFormData] = useState({
    year: new Date().getFullYear(),
    deadline: '',
    description: '',
    isActive: true
  });

  // Check permissions
  if (!permissions.canSetDeadlines) {
    return (
      <Card>
        <Card.Body>
          <Alert variant="danger">
            <AlertTriangle className="me-2" />
            You do not have permission to manage submission deadlines.
          </Alert>
        </Card.Body>
      </Card>
    );
  }

  const handleCreateDeadline = () => {
    setEditingDeadline(null);
    setFormData({
      year: new Date().getFullYear(),
      deadline: '',
      description: '',
      isActive: true
    });
    setShowModal(true);
  };

  const handleEditDeadline = (deadline: SubmissionDeadline) => {
    setEditingDeadline(deadline);
    setFormData({
      year: deadline.year,
      deadline: deadline.deadline.split('T')[0] + 'T' + deadline.deadline.split('T')[1].substring(0, 5),
      description: deadline.description || '',
      isActive: deadline.isActive
    });
    setShowModal(true);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    const newDeadline: SubmissionDeadline = {
      id: editingDeadline?.id || Date.now().toString(),
      year: formData.year,
      deadline: formData.deadline + ':59', // Add seconds
      description: formData.description,
      isActive: formData.isActive,
      createdBy: 'current-user',
      createdAt: editingDeadline?.createdAt || new Date().toISOString()
    };

    if (editingDeadline) {
      setDeadlines(prev => prev.map(d => d.id === editingDeadline.id ? newDeadline : d));
    } else {
      setDeadlines(prev => [...prev, newDeadline]);
    }

    setShowModal(false);
  };

  const handleDelete = (id: string) => {
    if (window.confirm('Are you sure you want to delete this deadline?')) {
      setDeadlines(prev => prev.filter(d => d.id !== id));
    }
  };

  const handleToggleActive = (id: string) => {
    setDeadlines(prev => prev.map(d => 
      d.id === id ? { ...d, isActive: !d.isActive } : d
    ));
  };

  const getDeadlineStatus = (deadline: SubmissionDeadline) => {
    if (!deadline.isActive) {
      return <Badge bg="secondary">Inactive</Badge>;
    }
    
    if (isPastDate(deadline.deadline)) {
      return <Badge bg="danger">Expired</Badge>;
    }
    
    return <Badge bg="success">Active</Badge>;
  };

  const activeDeadlines = deadlines.filter(d => d.isActive);
  const upcomingDeadlines = activeDeadlines.filter(d => !isPastDate(d.deadline));

  return (
    <div>
      {/* Current Active Deadlines Alert */}
      {upcomingDeadlines.length > 0 && (
        <Alert variant="info" className="mb-4">
          <Clock className="me-2" />
          <strong>Active Submission Deadlines:</strong>
          <ul className="mb-0 mt-2">
            {upcomingDeadlines.map(deadline => (
              <li key={deadline.id}>
                {deadline.description} - Due: {formatDateTime(deadline.deadline.split('T')[0], deadline.deadline.split('T')[1])}
              </li>
            ))}
          </ul>
        </Alert>
      )}

      <Card>
        <Card.Header>
          <div className="d-flex justify-content-between align-items-center">
            <h5 className="mb-0">
              <Calendar className="me-2" />
              Submission Deadlines
            </h5>
            <Button variant="primary" onClick={handleCreateDeadline}>
              <Plus className="me-2" size={16} />
              Add Deadline
            </Button>
          </div>
        </Card.Header>
        <Card.Body>
          {deadlines.length === 0 ? (
            <p className="text-muted mb-0">No submission deadlines configured.</p>
          ) : (
            <Table responsive>
              <thead>
                <tr>
                  <th>Year</th>
                  <th>Description</th>
                  <th>Deadline</th>
                  <th>Status</th>
                  <th>Created</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {deadlines.map(deadline => (
                  <tr key={deadline.id}>
                    <td>{deadline.year}</td>
                    <td>{deadline.description}</td>
                    <td>
                      {formatDateTime(deadline.deadline.split('T')[0], deadline.deadline.split('T')[1])}
                    </td>
                    <td>{getDeadlineStatus(deadline)}</td>
                    <td>{formatDate(deadline.createdAt)}</td>
                    <td>
                      <div className="d-flex gap-2">
                        <Button
                          size="sm"
                          variant="outline-primary"
                          onClick={() => handleEditDeadline(deadline)}
                        >
                          <Edit size={14} />
                        </Button>
                        <Button
                          size="sm"
                          variant={deadline.isActive ? "outline-warning" : "outline-success"}
                          onClick={() => handleToggleActive(deadline.id)}
                        >
                          {deadline.isActive ? 'Deactivate' : 'Activate'}
                        </Button>
                        <Button
                          size="sm"
                          variant="outline-danger"
                          onClick={() => handleDelete(deadline.id)}
                        >
                          <Trash2 size={14} />
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </Table>
          )}
        </Card.Body>
      </Card>

      {/* Add/Edit Deadline Modal */}
      <Modal show={showModal} onHide={() => setShowModal(false)}>
        <Modal.Header closeButton>
          <Modal.Title>
            {editingDeadline ? 'Edit Deadline' : 'Add New Deadline'}
          </Modal.Title>
        </Modal.Header>
        <Form onSubmit={handleSubmit}>
          <Modal.Body>
            <Form.Group className="mb-3">
              <Form.Label>Year</Form.Label>
              <Form.Control
                type="number"
                value={formData.year}
                onChange={(e) => setFormData(prev => ({ ...prev, year: parseInt(e.target.value) }))}
                min={new Date().getFullYear()}
                max={new Date().getFullYear() + 5}
                required
              />
            </Form.Group>

            <Form.Group className="mb-3">
              <Form.Label>Description</Form.Label>
              <Form.Control
                type="text"
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                placeholder="e.g., Q1 2024 Event Submissions"
                required
              />
            </Form.Group>

            <Form.Group className="mb-3">
              <Form.Label>Deadline Date & Time</Form.Label>
              <Form.Control
                type="datetime-local"
                value={formData.deadline}
                onChange={(e) => setFormData(prev => ({ ...prev, deadline: e.target.value }))}
                required
              />
            </Form.Group>

            <Form.Group className="mb-3">
              <Form.Check
                type="checkbox"
                label="Active (allow submissions until this deadline)"
                checked={formData.isActive}
                onChange={(e) => setFormData(prev => ({ ...prev, isActive: e.target.checked }))}
              />
            </Form.Group>
          </Modal.Body>
          <Modal.Footer>
            <Button variant="secondary" onClick={() => setShowModal(false)}>
              Cancel
            </Button>
            <Button variant="primary" type="submit">
              {editingDeadline ? 'Update Deadline' : 'Create Deadline'}
            </Button>
          </Modal.Footer>
        </Form>
      </Modal>
    </div>
  );
};

export default DeadlineManagement;
