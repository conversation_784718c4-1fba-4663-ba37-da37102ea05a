import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from '../ui/card';
import { But<PERSON> } from '../ui/button';
import { Alert, AlertDescription } from '../ui/alert';
import { Table, TableHeader, TableBody, TableHead, TableRow, TableCell } from '../ui/table';
import { Badge } from '../ui/badge';
import { <PERSON>dal, ModalContent, ModalHeader, ModalTitle, ModalFooter } from '../ui/modal';
import { Form, FormGroup, FormLabel, FormControl, FormCheckbox } from '../ui/form';
import { Calendar, Clock, Plus, Edit, Trash2, AlertTriangle } from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';
import { SubmissionDeadline } from '../../types/event';
import { formatDate, formatDateTime, isPastDate } from '../../utils/dateUtils';

const DeadlineManagement: React.FC = () => {
  const { permissions } = useAuth();

  // Mock deadlines data - in real app, this would come from an API
  const [deadlines, setDeadlines] = useState<SubmissionDeadline[]>([
    {
      id: '1',
      year: 2024,
      deadline: '2024-03-31T23:59:59',
      description: 'Q2 2024 Event Submissions',
      isActive: true,
      createdBy: 'admin',
      createdAt: '2024-01-01T00:00:00'
    },
    {
      id: '2',
      year: 2024,
      deadline: '2024-06-30T23:59:59',
      description: 'Q3 2024 Event Submissions',
      isActive: false,
      createdBy: 'admin',
      createdAt: '2024-01-01T00:00:00'
    }
  ]);

  const [showModal, setShowModal] = useState(false);
  const [editingDeadline, setEditingDeadline] = useState<SubmissionDeadline | null>(null);
  const [formData, setFormData] = useState({
    year: new Date().getFullYear(),
    deadline: '',
    description: '',
    isActive: true
  });

  // Check permissions
  if (!permissions.canSetDeadlines) {
    return (
      <Card>
        <CardContent>
          <Alert variant="destructive">
            <AlertTriangle className="mr-2" />
            <AlertDescription>
              You do not have permission to manage submission deadlines.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  const handleCreateDeadline = () => {
    setEditingDeadline(null);
    setFormData({
      year: new Date().getFullYear(),
      deadline: '',
      description: '',
      isActive: true
    });
    setShowModal(true);
  };

  const handleEditDeadline = (deadline: SubmissionDeadline) => {
    setEditingDeadline(deadline);
    setFormData({
      year: deadline.year,
      deadline: deadline.deadline.split('T')[0] + 'T' + deadline.deadline.split('T')[1].substring(0, 5),
      description: deadline.description || '',
      isActive: deadline.isActive
    });
    setShowModal(true);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    const newDeadline: SubmissionDeadline = {
      id: editingDeadline?.id || Date.now().toString(),
      year: formData.year,
      deadline: formData.deadline + ':59', // Add seconds
      description: formData.description,
      isActive: formData.isActive,
      createdBy: 'current-user',
      createdAt: editingDeadline?.createdAt || new Date().toISOString()
    };

    if (editingDeadline) {
      setDeadlines(prev => prev.map(d => d.id === editingDeadline.id ? newDeadline : d));
    } else {
      setDeadlines(prev => [...prev, newDeadline]);
    }

    setShowModal(false);
  };

  const handleDelete = (id: string) => {
    if (window.confirm('Are you sure you want to delete this deadline?')) {
      setDeadlines(prev => prev.filter(d => d.id !== id));
    }
  };

  const handleToggleActive = (id: string) => {
    setDeadlines(prev => prev.map(d =>
      d.id === id ? { ...d, isActive: !d.isActive } : d
    ));
  };

  const getDeadlineStatus = (deadline: SubmissionDeadline) => {
    if (!deadline.isActive) {
      return <Badge bg="secondary">Inactive</Badge>;
    }

    if (isPastDate(deadline.deadline)) {
      return <Badge bg="danger">Expired</Badge>;
    }

    return <Badge bg="success">Active</Badge>;
  };

  const activeDeadlines = deadlines.filter(d => d.isActive);
  const upcomingDeadlines = activeDeadlines.filter(d => !isPastDate(d.deadline));

  return (
    <div>
      {/* Current Active Deadlines Alert */}
      {upcomingDeadlines.length > 0 && (
        <Alert variant="info" className="mb-4">
          <Clock className="me-2" />
          <strong>Active Submission Deadlines:</strong>
          <ul className="mb-0 mt-2">
            {upcomingDeadlines.map(deadline => (
              <li key={deadline.id}>
                {deadline.description} - Due: {formatDateTime(deadline.deadline.split('T')[0], deadline.deadline.split('T')[1])}
              </li>
            ))}
          </ul>
        </Alert>
      )}

      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <h5 className="mb-0 flex items-center">
              <Calendar className="mr-2" />
              Submission Deadlines
            </h5>
            <Button variant="default" onClick={handleCreateDeadline}>
              <Plus className="mr-2" size={16} />
              Add Deadline
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {deadlines.length === 0 ? (
            <p className="text-muted mb-0">No submission deadlines configured.</p>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Year</TableHead>
                  <TableHead>Description</TableHead>
                  <TableHead>Deadline</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Created</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {deadlines.map(deadline => (
                  <TableRow key={deadline.id}>
                    <TableCell>{deadline.year}</TableCell>
                    <TableCell>{deadline.description}</TableCell>
                    <TableCell>
                      {formatDateTime(deadline.deadline.split('T')[0], deadline.deadline.split('T')[1])}
                    </TableCell>
                    <TableCell>{getDeadlineStatus(deadline)}</TableCell>
                    <TableCell>{formatDate(deadline.createdAt)}</TableCell>
                    <TableCell>
                      <div className="flex gap-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleEditDeadline(deadline)}
                        >
                          <Edit size={14} />
                        </Button>
                        <Button
                          size="sm"
                          variant={deadline.isActive ? "outline" : "outline"}
                          onClick={() => handleToggleActive(deadline.id)}
                          className={deadline.isActive ? "border-yellow-500 text-yellow-600" : "border-green-500 text-green-600"}
                        >
                          {deadline.isActive ? 'Deactivate' : 'Activate'}
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleDelete(deadline.id)}
                          className="border-red-500 text-red-600 hover:bg-red-50"
                        >
                          <Trash2 size={14} />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* Add/Edit Deadline Modal */}
      <Modal open={showModal} onOpenChange={setShowModal}>
        <ModalContent>
          <ModalHeader>
            <ModalTitle>
              {editingDeadline ? 'Edit Deadline' : 'Add New Deadline'}
            </ModalTitle>
          </ModalHeader>
          <Form onSubmit={handleSubmit}>
            <div className="p-6">
              <FormGroup className="mb-3">
                <FormLabel>Year</FormLabel>
                <FormControl
                  type="number"
                  value={formData.year}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => setFormData(prev => ({ ...prev, year: parseInt(e.target.value) }))}
                  min={new Date().getFullYear()}
                  max={new Date().getFullYear() + 5}
                  required
                />
              </FormGroup>

              <FormGroup className="mb-3">
                <FormLabel>Description</FormLabel>
                <FormControl
                  type="text"
                  value={formData.description}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="e.g., Q1 2024 Event Submissions"
                  required
                />
              </FormGroup>

              <FormGroup className="mb-3">
                <FormLabel>Deadline Date & Time</FormLabel>
                <FormControl
                  type="datetime-local"
                  value={formData.deadline}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => setFormData(prev => ({ ...prev, deadline: e.target.value }))}
                  required
                />
              </FormGroup>

              <FormGroup className="mb-3">
                <FormCheckbox
                  label="Active (allow submissions until this deadline)"
                  checked={formData.isActive}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => setFormData(prev => ({ ...prev, isActive: e.target.checked }))}
                />
              </FormGroup>
            </div>
            <ModalFooter>
              <Button variant="outline" onClick={() => setShowModal(false)}>
                Cancel
              </Button>
              <Button variant="default" type="submit">
                {editingDeadline ? 'Update Deadline' : 'Create Deadline'}
              </Button>
            </ModalFooter>
          </Form>
        </ModalContent>
      </Modal>
    </div>
  );
};

export default DeadlineManagement;
