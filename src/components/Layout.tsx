import React, { ReactNode } from 'react';
import Header from './Header';
import Footer from './Footer';

interface LayoutProps {
    children: ReactNode;
    showHeader?: boolean;
    showFooter?: boolean;
    className?: string;
}

const Layout: React.FC<LayoutProps> = ({
    children,
    showHeader = true,
    showFooter = true,
    className = ''
}) => {
    return (
        <div className={`min-h-screen flex flex-col ${className}`}>
            {/* Header Section */}
            {showHeader && <Header />}

            {/* Main Content Section */}
            <main className="flex-1">
                {children}
            </main>

            {/* Footer Section */}
            {showFooter && <Footer />}
        </div>
    );
};

export default Layout;
