import React, { ReactNode } from 'react';
import { Container, Navbar, Nav, Button, Row, Col } from 'react-bootstrap';
import { Link } from 'react-router-dom';
// import { Outlet } from 'react-router-dom';
import Footer from './Footer';
import Header from './Header';

interface LayoutProps {
    children: ReactNode;
}


const Layout: React.FC<LayoutProps> = ({ children }) => {
    return (
        <div>

            {/* Header Section */}
            <Header />

            {/* Main Content Section */}
            {/* <Container fluid className="py-4"> */}
            {/* Outlet is used to render child routes in react-router */}
            {/* <Outlet /> */}

            {/* </Container> */}
            {children}

            {/* Footer Section */}
            <Footer />
        </div>
    );
};

export default Layout;
