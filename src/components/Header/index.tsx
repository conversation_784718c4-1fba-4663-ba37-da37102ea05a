import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { User, LogOut, Settings, Calendar, Menu, X } from '../ui/icons';
import WithKalendaBrand from '../Brand';
import { useAuth } from '../../contexts/AuthContext';
import { Button } from '../ui/button';

const Header: React.FC = () => {
    const { isAuthenticated, user, logout } = useAuth();
    const navigate = useNavigate();

    const handleLogout = () => {
        logout();
        navigate('/');
    };

    const [isMenuOpen, setIsMenuOpen] = useState(false);
    const [isUserMenuOpen, setIsUserMenuOpen] = useState(false);

    const navLinks = [
        { to: '/', label: 'Home' },
        { to: '/features', label: 'Features' },
        { to: '/pricing', label: 'Pricing' },
        { to: '/demo', label: 'Demo' },
        { to: '/about', label: 'About Us' },
        { to: '/contact', label: 'Contact' },
    ];

    return (
        <header className="bg-white shadow-sm border-b">
            <div className="wk-container">
                <div className="flex items-center justify-between h-16">
                    <WithKalendaBrand />

                    {/* Desktop Navigation */}
                    <nav className="hidden lg:flex items-center space-x-8">
                        {navLinks.map((link) => (
                            <Link
                                key={link.to}
                                to={link.to}
                                className="text-brand-blue font-semibold hover:text-brand-blue/80 transition-colors"
                            >
                                {link.label}
                            </Link>
                        ))}
                        {isAuthenticated && (
                            <Link
                                to="/dashboard"
                                className="text-brand-blue font-semibold hover:text-brand-blue/80 transition-colors flex items-center"
                            >
                                <Calendar size={16} className="mr-1" />
                                Dashboard
                            </Link>
                        )}
                    </nav>

                    {/* Desktop Auth Buttons */}
                    <div className="hidden lg:flex items-center space-x-4">
                        {isAuthenticated ? (
                            <div className="relative">
                                <Button
                                    variant="outline"
                                    onClick={() => setIsUserMenuOpen(!isUserMenuOpen)}
                                    className="flex items-center space-x-2"
                                >
                                    <User size={16} />
                                    <span>{user?.firstName || 'User'}</span>
                                </Button>

                                {isUserMenuOpen && (
                                    <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg border z-50">
                                        <div className="px-4 py-3 border-b">
                                            <p className="text-sm font-medium text-gray-900">
                                                {user?.firstName} {user?.lastName}
                                            </p>
                                            <p className="text-sm text-gray-500">{user?.email}</p>
                                        </div>
                                        <div className="py-1">
                                            <Link
                                                to="/dashboard"
                                                className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                                                onClick={() => setIsUserMenuOpen(false)}
                                            >
                                                <Calendar size={16} className="mr-2" />
                                                Dashboard
                                            </Link>
                                            <Link
                                                to="/profile"
                                                className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                                                onClick={() => setIsUserMenuOpen(false)}
                                            >
                                                <Settings size={16} className="mr-2" />
                                                Profile Settings
                                            </Link>
                                            <button
                                                onClick={() => {
                                                    handleLogout();
                                                    setIsUserMenuOpen(false);
                                                }}
                                                className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                                            >
                                                <LogOut size={16} className="mr-2" />
                                                Logout
                                            </button>
                                        </div>
                                    </div>
                                )}
                            </div>
                        ) : (
                            <div className="flex space-x-2">
                                <Button asChild variant="outline">
                                    <Link to="/login">Login</Link>
                                </Button>
                                <Button asChild variant="brand-orange">
                                    <Link to="/signup">Sign Up</Link>
                                </Button>
                            </div>
                        )}
                    </div>

                    {/* Mobile menu button */}
                    <Button
                        variant="ghost"
                        size="icon"
                        className="lg:hidden"
                        onClick={() => setIsMenuOpen(!isMenuOpen)}
                    >
                        {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
                    </Button>
                </div>

                {/* Mobile Navigation */}
                {isMenuOpen && (
                    <div className="lg:hidden border-t bg-white">
                        <div className="px-2 pt-2 pb-3 space-y-1">
                            {navLinks.map((link) => (
                                <Link
                                    key={link.to}
                                    to={link.to}
                                    className="block px-3 py-2 text-brand-blue font-semibold hover:bg-gray-50 rounded-md"
                                    onClick={() => setIsMenuOpen(false)}
                                >
                                    {link.label}
                                </Link>
                            ))}
                            {isAuthenticated && (
                                <Link
                                    to="/dashboard"
                                    className="block px-3 py-2 text-brand-blue font-semibold hover:bg-gray-50 rounded-md flex items-center"
                                    onClick={() => setIsMenuOpen(false)}
                                >
                                    <Calendar size={16} className="mr-1" />
                                    Dashboard
                                </Link>
                            )}

                            {/* Mobile Auth */}
                            <div className="pt-4 border-t">
                                {isAuthenticated ? (
                                    <div className="space-y-2">
                                        <div className="px-3 py-2 text-sm text-gray-500">
                                            {user?.firstName} {user?.lastName}
                                        </div>
                                        <Link
                                            to="/profile"
                                            className="block px-3 py-2 text-gray-700 hover:bg-gray-50 rounded-md flex items-center"
                                            onClick={() => setIsMenuOpen(false)}
                                        >
                                            <Settings size={16} className="mr-2" />
                                            Profile Settings
                                        </Link>
                                        <button
                                            onClick={() => {
                                                handleLogout();
                                                setIsMenuOpen(false);
                                            }}
                                            className="block w-full text-left px-3 py-2 text-gray-700 hover:bg-gray-50 rounded-md flex items-center"
                                        >
                                            <LogOut size={16} className="mr-2" />
                                            Logout
                                        </button>
                                    </div>
                                ) : (
                                    <div className="space-y-2">
                                        <Button asChild variant="outline" className="w-full">
                                            <Link to="/login" onClick={() => setIsMenuOpen(false)}>Login</Link>
                                        </Button>
                                        <Button asChild variant="brand-orange" className="w-full">
                                            <Link to="/signup" onClick={() => setIsMenuOpen(false)}>Sign Up</Link>
                                        </Button>
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>
                )}
            </div>
        </header>
    );
};

export default Header;
