import React from 'react';
import { Container, Navbar, Nav, Button } from 'react-bootstrap';
import { <PERSON> } from 'react-router-dom';
import WithKalendaBrand from '../Brand';


const index = () => {
    return (
        <Navbar bg="light" expand="lg">
            <Container>
                <WithKalendaBrand />
                <Navbar.Toggle aria-controls="basic-navbar-nav" />
                <Navbar.Collapse id="basic-navbar-nav">
                    <Nav className="me-auto justify-content-evenly">
                        <Link to="/features" className={`text-decoration-none fw-bold`}>
                            <Nav.Link>Features</Nav.Link>
                        </Link>
                        <Link to="/pricing" className={`text-decoration-none fw-bold`}>
                            <Nav.Link>Pricing</Nav.Link>
                        </Link>
                        <Link to="/about" className={`text-decoration-none  fw-bold`}>
                            <Nav.Link>About</Nav.Link>
                        </Link>
                        <Link to="/contact" className={`text-decoration-none  fw-bold`}>
                            <Nav.Link>Contact</Nav.Link>
                        </Link>
                    </Nav>
                    <Nav>
                        <Link to="/login" className={`text-decoration-none fw-bold`}>
                            <Nav.Link>Login</Nav.Link>
                        </Link>
                        <Link to="/signup">
                            <Button variant="primary">Sign Up</Button>
                        </Link>
                    </Nav>
                </Navbar.Collapse>
            </Container>
        </Navbar>

    );
};

export default index;
