import React from 'react';
import { Container, Navbar, Nav, Button, Dropdown } from 'react-bootstrap';
import { Link, useNavigate } from 'react-router-dom';
import { User, LogOut, Settings, Calendar } from 'lucide-react';
import WithKalendaBrand from '../Brand';
import { useAuth } from '../../contexts/AuthContext';

const Header: React.FC = () => {
    const { isAuthenticated, user, logout } = useAuth();
    const navigate = useNavigate();

    const handleLogout = () => {
        logout();
        navigate('/');
    };

    return (
        <Navbar bg="white" expand="lg" className="py-3 shadow-sm">
            <Container>
                <WithKalendaBrand />
                <Navbar.Toggle aria-controls="basic-navbar-nav" />
                <Navbar.Collapse id="basic-navbar-nav">
                    <Nav className="me-auto">
                        <Nav.Link as={Link} to="/" className="fw-semibold text-dark mx-2">
                            Home
                        </Nav.Link>
                        <Nav.Link as={Link} to="/features" className="fw-semibold mx-2" style={{ color: '#3174ad' }}>
                            Features
                        </Nav.Link>
                        <Nav.Link as={Link} to="/pricing" className="fw-semibold mx-2" style={{ color: '#3174ad' }}>
                            Pricing
                        </Nav.Link>
                        <Nav.Link as={Link} to="/about" className="fw-semibold mx-2" style={{ color: '#3174ad' }}>
                            About Us
                        </Nav.Link>
                        <Nav.Link as={Link} to="/contact" className="fw-semibold mx-2" style={{ color: '#3174ad' }}>
                            Contact
                        </Nav.Link>
                        {isAuthenticated && (
                            <Nav.Link as={Link} to="/dashboard" className="fw-semibold text-primary mx-2">
                                <Calendar size={16} className="me-1" />
                                Dashboard
                            </Nav.Link>
                        )}
                    </Nav>

                    <Nav>
                        {isAuthenticated ? (
                            <Dropdown align="end">
                                <Dropdown.Toggle
                                    variant="outline-primary"
                                    id="user-dropdown"
                                    className="d-flex align-items-center"
                                >
                                    <User size={16} className="me-2" />
                                    {user?.firstName || 'User'}
                                </Dropdown.Toggle>
                                <Dropdown.Menu>
                                    <Dropdown.Header>
                                        {user?.firstName} {user?.lastName}
                                        <br />
                                        <small className="text-muted">{user?.email}</small>
                                    </Dropdown.Header>
                                    <Dropdown.Divider />
                                    <Dropdown.Item as={Link} to="/dashboard">
                                        <Calendar size={16} className="me-2" />
                                        Dashboard
                                    </Dropdown.Item>
                                    <Dropdown.Item as={Link} to="/profile">
                                        <Settings size={16} className="me-2" />
                                        Profile Settings
                                    </Dropdown.Item>
                                    <Dropdown.Divider />
                                    <Dropdown.Item onClick={handleLogout}>
                                        <LogOut size={16} className="me-2" />
                                        Logout
                                    </Dropdown.Item>
                                </Dropdown.Menu>
                            </Dropdown>
                        ) : (
                            <div className="d-flex gap-2">
                                <Link to="/login">
                                    <Button
                                        variant="outline-primary"
                                        className="fw-semibold"
                                    >
                                        Login
                                    </Button>
                                </Link>
                                <Link to="/signup">
                                    <Button
                                        className="fw-semibold"
                                        style={{ backgroundColor: '#ff6b35', borderColor: '#ff6b35', color: 'white' }}
                                    >
                                        Sign Up
                                    </Button>
                                </Link>
                            </div>
                        )}
                    </Nav>
                </Navbar.Collapse>
            </Container>
        </Navbar>
    );
};

export default Header;
