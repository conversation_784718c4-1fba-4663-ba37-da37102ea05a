import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import AdminDashboard from '../index';
import { AuthProvider } from '../../../contexts/AuthContext';
import { sampleUsers } from '../../../data/sampleUsers';
import { sampleEvents } from '../../../data/sampleEvents';
import { EventStatus } from '../../../types/event';

// Mock the useEvents hook
const mockUseEvents = {
  events: sampleEvents,
  getPendingEvents: jest.fn(() => sampleEvents.filter(e => e.status === EventStatus.PENDING)),
  getConflictedEvents: jest.fn(() => []),
  approveEvent: { mutateAsync: jest.fn(), isPending: false },
  rejectEvent: { mutateAsync: jest.fn(), isPending: false },
  isLoading: false
};

jest.mock('../../../hooks/useEvents', () => ({
  useEvents: () => mockUseEvents
}));

// Mock the useAuth hook
const mockUseAuth = {
  user: sampleUsers[0], // Admin user
  permissions: {
    canApproveEvent: true,
    canRejectEvent: true,
    canViewAllEvents: true,
    canManageDepartments: true,
    canManageUsers: true,
    canSetDeadlines: true,
    canExportCalendar: true,
    canViewAnalytics: true,
    canCreateEvent: true,
    canEditEvent: () => true,
    canDeleteEvent: () => true
  },
  isAuthenticated: true,
  login: jest.fn(),
  logout: jest.fn(),
  isLoading: false
};

jest.mock('../../../contexts/AuthContext', () => ({
  useAuth: () => mockUseAuth,
  AuthProvider: ({ children }: { children: React.ReactNode; }) => <div>{children}</div>
}));

const createTestQueryClient = () => {
  return new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false }
    }
  });
};

const renderWithProviders = (component: React.ReactElement) => {
  const queryClient = createTestQueryClient();
  return render(
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        {component}
      </AuthProvider>
    </QueryClientProvider>
  );
};

describe('AdminDashboard', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders admin dashboard with pending events', () => {
    renderWithProviders(<AdminDashboard />);

    expect(screen.getByText('Admin Dashboard')).toBeInTheDocument();
    expect(screen.getByText('Pending Events')).toBeInTheDocument();
    expect(screen.getByText('Event Conflicts')).toBeInTheDocument();
  });

  it('displays pending events count', () => {
    renderWithProviders(<AdminDashboard />);

    const pendingCount = sampleEvents.filter(e => e.status === EventStatus.PENDING).length;
    expect(screen.getByText(pendingCount.toString())).toBeInTheDocument();
  });

  it('shows approve and reject buttons for pending events', () => {
    renderWithProviders(<AdminDashboard />);

    const approveButtons = screen.getAllByText('Approve');
    const rejectButtons = screen.getAllByText('Reject');

    expect(approveButtons.length).toBeGreaterThan(0);
    expect(rejectButtons.length).toBeGreaterThan(0);
  });

  it('opens approval modal when approve button is clicked', async () => {
    renderWithProviders(<AdminDashboard />);

    const approveButton = screen.getAllByText('Approve')[0];
    fireEvent.click(approveButton);

    await waitFor(() => {
      expect(screen.getByText('Approve Event')).toBeInTheDocument();
    });
  });

  it('opens rejection modal when reject button is clicked', async () => {
    renderWithProviders(<AdminDashboard />);

    const rejectButton = screen.getAllByText('Reject')[0];
    fireEvent.click(rejectButton);

    await waitFor(() => {
      expect(screen.getByText('Reject Event')).toBeInTheDocument();
    });
  });

  it('calls approveEvent when approval is confirmed', async () => {
    renderWithProviders(<AdminDashboard />);

    const approveButton = screen.getAllByText('Approve')[0];
    fireEvent.click(approveButton);

    await waitFor(() => {
      expect(screen.getByText('Approve Event')).toBeInTheDocument();
    });

    const confirmButton = screen.getByText('Approve Event', { selector: 'button' });
    fireEvent.click(confirmButton);

    await waitFor(() => {
      expect(mockUseEvents.approveEvent.mutateAsync).toHaveBeenCalled();
    });
  });

  it('calls rejectEvent when rejection is confirmed with comments', async () => {
    renderWithProviders(<AdminDashboard />);

    const rejectButton = screen.getAllByText('Reject')[0];
    fireEvent.click(rejectButton);

    await waitFor(() => {
      expect(screen.getByText('Reject Event')).toBeInTheDocument();
    });

    const textarea = screen.getByPlaceholderText('Enter rejection reason...');
    fireEvent.change(textarea, { target: { value: 'Test rejection reason' } });

    const confirmButton = screen.getByText('Reject Event', { selector: 'button' });
    fireEvent.click(confirmButton);

    await waitFor(() => {
      expect(mockUseEvents.rejectEvent.mutateAsync).toHaveBeenCalledWith({
        eventId: expect.any(String),
        comments: 'Test rejection reason'
      });
    });
  });

  it('displays no access message for non-admin users', () => {
    const nonAdminAuth = {
      ...mockUseAuth,
      permissions: {
        ...mockUseAuth.permissions,
        canApproveEvent: false,
        canRejectEvent: false
      }
    };

    require('../../../contexts/AuthContext').useAuth = jest.fn().mockReturnValue(nonAdminAuth);

    renderWithProviders(<AdminDashboard />);

    expect(screen.getByText('You do not have permission to access the admin dashboard.')).toBeInTheDocument();
  });

  it('shows loading state when events are loading', () => {
    const loadingUseEvents = {
      ...mockUseEvents,
      isLoading: true
    };

    require('../../../hooks/useEvents').useEvents = jest.fn().mockReturnValue(loadingUseEvents);

    renderWithProviders(<AdminDashboard />);

    expect(screen.getByText('Loading...')).toBeInTheDocument();
  });

  it('displays event details in the table', () => {
    renderWithProviders(<AdminDashboard />);

    const pendingEvents = sampleEvents.filter(e => e.status === EventStatus.PENDING);
    if (pendingEvents.length > 0) {
      expect(screen.getByText(pendingEvents[0].eventName)).toBeInTheDocument();
      expect(screen.getByText(pendingEvents[0].organizer)).toBeInTheDocument();
    }
  });

  it('closes modal when cancel button is clicked', async () => {
    renderWithProviders(<AdminDashboard />);

    const approveButton = screen.getAllByText('Approve')[0];
    fireEvent.click(approveButton);

    await waitFor(() => {
      expect(screen.getByText('Approve Event')).toBeInTheDocument();
    });

    const cancelButton = screen.getByText('Cancel');
    fireEvent.click(cancelButton);

    await waitFor(() => {
      expect(screen.queryByText('Approve Event')).not.toBeInTheDocument();
    });
  });
});
