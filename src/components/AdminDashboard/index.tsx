import React, { useState } from 'react';
import { Container, Row, Col, Card, Badge, Button, Modal, Form, Alert, Table } from 'react-bootstrap';
import { Calendar, Clock, AlertTriangle, CheckCircle, XCircle, Users } from 'lucide-react';
import { useEvents } from '../../hooks/useEvents';
import { useAuth } from '../../contexts/AuthContext';
import { EvaEvent, EventStatus } from '../../types/event';
import { formatDate, formatTime } from '../../utils/dateUtils';

const AdminDashboard: React.FC = () => {
  const { user, permissions } = useAuth();
  const {
    events,
    getPendingEvents,
    getConflictedEvents,
    approveEvent,
    rejectEvent,
    isLoading
  } = useEvents();

  const [showReviewModal, setShowReviewModal] = useState(false);
  const [selectedEvent, setSelectedEvent] = useState<EvaEvent | null>(null);
  const [reviewComments, setReviewComments] = useState('');
  const [reviewAction, setReviewAction] = useState<'approve' | 'reject'>('approve');

  // Check if user has admin permissions
  if (!permissions.canApproveEvent) {
    return (
      <Container className="py-4">
        <Alert variant="danger">
          <AlertTriangle className="me-2" />
          You do not have permission to access the admin dashboard.
        </Alert>
      </Container>
    );
  }

  const pendingEvents = getPendingEvents();
  const conflictedEvents = getConflictedEvents();
  const confirmedEvents = events.filter(e => e.status === EventStatus.CONFIRMED);
  const rejectedEvents = events.filter(e => e.status === EventStatus.REJECTED);

  const handleReviewEvent = (event: EvaEvent, action: 'approve' | 'reject') => {
    setSelectedEvent(event);
    setReviewAction(action);
    setReviewComments('');
    setShowReviewModal(true);
  };

  const handleSubmitReview = async () => {
    if (!selectedEvent) return;

    try {
      if (reviewAction === 'approve') {
        await approveEvent.mutateAsync({
          eventId: selectedEvent.id.toString(),
          comments: reviewComments
        });
      } else {
        await rejectEvent.mutateAsync({
          eventId: selectedEvent.id.toString(),
          comments: reviewComments || 'Event rejected by administrator'
        });
      }
      setShowReviewModal(false);
      setSelectedEvent(null);
      setReviewComments('');
    } catch (error) {
      console.error('Error reviewing event:', error);
    }
  };

  const getStatusBadge = (status: EventStatus) => {
    const variants = {
      [EventStatus.DRAFT]: 'secondary',
      [EventStatus.PENDING]: 'warning',
      [EventStatus.CONFIRMED]: 'success',
      [EventStatus.REJECTED]: 'danger',
      [EventStatus.CANCELLED]: 'dark'
    };
    return <Badge bg={variants[status]}>{status.toUpperCase()}</Badge>;
  };

  const getConflictSeverityBadge = (severity: string) => {
    const variants = {
      low: 'info',
      medium: 'warning',
      high: 'danger'
    };
    return <Badge bg={variants[severity as keyof typeof variants] || 'secondary'}>{severity.toUpperCase()}</Badge>;
  };

  if (isLoading) {
    return (
      <Container className="py-4">
        <div className="text-center">Loading admin dashboard...</div>
      </Container>
    );
  }

  return (
    <Container className="py-4">
      <h1 className="mb-4">
        <Users className="me-2" />
        Admin Dashboard
      </h1>

      {/* Statistics Cards */}
      <Row className="mb-4">
        <Col md={3}>
          <Card className="text-center">
            <Card.Body>
              <Clock className="text-warning mb-2" size={32} />
              <h3 className="text-warning">{pendingEvents.length}</h3>
              <p className="mb-0">Pending Review</p>
            </Card.Body>
          </Card>
        </Col>
        <Col md={3}>
          <Card className="text-center">
            <Card.Body>
              <AlertTriangle className="text-danger mb-2" size={32} />
              <h3 className="text-danger">{conflictedEvents.length}</h3>
              <p className="mb-0">Conflicts</p>
            </Card.Body>
          </Card>
        </Col>
        <Col md={3}>
          <Card className="text-center">
            <Card.Body>
              <CheckCircle className="text-success mb-2" size={32} />
              <h3 className="text-success">{confirmedEvents.length}</h3>
              <p className="mb-0">Confirmed</p>
            </Card.Body>
          </Card>
        </Col>
        <Col md={3}>
          <Card className="text-center">
            <Card.Body>
              <XCircle className="text-secondary mb-2" size={32} />
              <h3 className="text-secondary">{rejectedEvents.length}</h3>
              <p className="mb-0">Rejected</p>
            </Card.Body>
          </Card>
        </Col>
      </Row>

      {/* Pending Events for Review */}
      <Card className="mb-4">
        <Card.Header>
          <h5 className="mb-0">
            <Clock className="me-2" />
            Events Pending Review ({pendingEvents.length})
          </h5>
        </Card.Header>
        <Card.Body>
          {pendingEvents.length === 0 ? (
            <p className="text-muted mb-0">No events pending review.</p>
          ) : (
            <Table responsive>
              <thead>
                <tr>
                  <th>Event</th>
                  <th>Department</th>
                  <th>Date & Time</th>
                  <th>Organizer</th>
                  <th>Status</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {pendingEvents.map(event => (
                  <tr key={event.id}>
                    <td>
                      <strong>{event.eventName}</strong>
                      {event.description && (
                        <div className="text-muted small">{event.description}</div>
                      )}
                    </td>
                    <td>{event.departmentName}</td>
                    <td>
                      {formatDate(event.date)}<br />
                      <small className="text-muted">{formatTime(event.time)}</small>
                    </td>
                    <td>{event.organizer}</td>
                    <td>{getStatusBadge(event.status)}</td>
                    <td>
                      <Button
                        size="sm"
                        variant="success"
                        className="me-2"
                        onClick={() => handleReviewEvent(event, 'approve')}
                      >
                        Approve
                      </Button>
                      <Button
                        size="sm"
                        variant="danger"
                        onClick={() => handleReviewEvent(event, 'reject')}
                      >
                        Reject
                      </Button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </Table>
          )}
        </Card.Body>
      </Card>

      {/* Events with Conflicts */}
      {conflictedEvents.length > 0 && (
        <Card className="mb-4">
          <Card.Header>
            <h5 className="mb-0">
              <AlertTriangle className="me-2" />
              Events with Conflicts ({conflictedEvents.length})
            </h5>
          </Card.Header>
          <Card.Body>
            <Table responsive>
              <thead>
                <tr>
                  <th>Event</th>
                  <th>Department</th>
                  <th>Date & Time</th>
                  <th>Conflicts</th>
                  <th>Status</th>
                </tr>
              </thead>
              <tbody>
                {conflictedEvents.map(event => (
                  <tr key={event.id}>
                    <td>
                      <strong>{event.eventName}</strong>
                    </td>
                    <td>{event.departmentName}</td>
                    <td>
                      {formatDate(event.date)}<br />
                      <small className="text-muted">{formatTime(event.time)}</small>
                    </td>
                    <td>
                      {event.conflicts?.map((conflict, index) => (
                        <div key={index} className="mb-1">
                          {getConflictSeverityBadge(conflict.severity)} {conflict.description}
                        </div>
                      ))}
                    </td>
                    <td>{getStatusBadge(event.status)}</td>
                  </tr>
                ))}
              </tbody>
            </Table>
          </Card.Body>
        </Card>
      )}

      {/* Review Modal */}
      <Modal show={showReviewModal} onHide={() => setShowReviewModal(false)}>
        <Modal.Header closeButton>
          <Modal.Title>
            {reviewAction === 'approve' ? 'Approve Event' : 'Reject Event'}
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {selectedEvent && (
            <>
              <h6>{selectedEvent.eventName}</h6>
              <p className="text-muted">
                {selectedEvent.departmentName} • {formatDate(selectedEvent.date)} at {formatTime(selectedEvent.time)}
              </p>
              <Form.Group>
                <Form.Label>
                  {reviewAction === 'approve' ? 'Approval Comments (Optional)' : 'Rejection Reason (Required)'}
                </Form.Label>
                <Form.Control
                  as="textarea"
                  rows={3}
                  value={reviewComments}
                  onChange={(e) => setReviewComments(e.target.value)}
                  placeholder={
                    reviewAction === 'approve'
                      ? 'Add any comments about the approval...'
                      : 'Please provide a reason for rejection...'
                  }
                  required={reviewAction === 'reject'}
                />
              </Form.Group>
            </>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowReviewModal(false)}>
            Cancel
          </Button>
          <Button
            variant={reviewAction === 'approve' ? 'success' : 'danger'}
            onClick={handleSubmitReview}
            disabled={reviewAction === 'reject' && !reviewComments.trim()}
          >
            {reviewAction === 'approve' ? 'Approve Event' : 'Reject Event'}
          </Button>
        </Modal.Footer>
      </Modal>
    </Container>
  );
};

export default AdminDashboard;
