import React, { useState } from 'react';
import { Clock, AlertTriangle, CheckCircle, XCircle, Users } from '../ui/icons';
import { Card, CardHeader, CardContent } from '../ui/card';
import { Badge } from '../ui/badge';
import { <PERSON><PERSON> } from '../ui/button';
import { Modal, ModalContent, ModalHeader, ModalTitle, ModalFooter } from '../ui/modal';
import { FormGroup, FormLabel, FormTextarea } from '../ui/form';
import { Alert, AlertDescription } from '../ui/alert';
import { Table, TableHeader, TableBody, TableHead, TableRow, TableCell } from '../ui/table';
import { useEvents } from '../../hooks/useEvents';
import { useAuth } from '../../contexts/AuthContext';
import { EvaEvent, EventStatus } from '../../types/event';
import { formatDate, formatTime } from '../../utils/dateUtils';

const AdminDashboard: React.FC = () => {
  const { permissions } = useAuth();
  const {
    events,
    getPendingEvents,
    getConflictedEvents,
    approveEvent,
    rejectEvent,
    isLoading
  } = useEvents();

  const [showReviewModal, setShowReviewModal] = useState(false);
  const [selectedEvent, setSelectedEvent] = useState<EvaEvent | null>(null);
  const [reviewComments, setReviewComments] = useState('');
  const [reviewAction, setReviewAction] = useState<'approve' | 'reject'>('approve');

  // Check if user has admin permissions
  if (!permissions.canApproveEvent) {
    return (
      <div className="max-w-7xl mx-auto px-4 py-8">
        <Alert variant="destructive">
          <AlertTriangle className="mr-2" />
          <AlertDescription>
            You do not have permission to access the admin dashboard.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  const pendingEvents = getPendingEvents();
  const conflictedEvents = getConflictedEvents();
  const confirmedEvents = events.filter(e => e.status === EventStatus.CONFIRMED);
  const rejectedEvents = events.filter(e => e.status === EventStatus.REJECTED);

  const handleReviewEvent = (event: EvaEvent, action: 'approve' | 'reject') => {
    setSelectedEvent(event);
    setReviewAction(action);
    setReviewComments('');
    setShowReviewModal(true);
  };

  const handleSubmitReview = async () => {
    if (!selectedEvent) return;

    try {
      if (reviewAction === 'approve') {
        await approveEvent.mutateAsync({
          eventId: selectedEvent.id.toString(),
          comments: reviewComments
        });
      } else {
        await rejectEvent.mutateAsync({
          eventId: selectedEvent.id.toString(),
          comments: reviewComments || 'Event rejected by administrator'
        });
      }
      setShowReviewModal(false);
      setSelectedEvent(null);
      setReviewComments('');
    } catch (error) {
      console.error('Error reviewing event:', error);
    }
  };

  const getStatusBadge = (status: EventStatus) => {
    const variants: Record<EventStatus, "default" | "secondary" | "destructive" | "outline"> = {
      [EventStatus.DRAFT]: 'secondary',
      [EventStatus.PENDING]: 'default',
      [EventStatus.CONFIRMED]: 'secondary',
      [EventStatus.REJECTED]: 'destructive',
      [EventStatus.CANCELLED]: 'outline'
    };
    return <Badge variant={variants[status]}>{status.toUpperCase()}</Badge>;
  };

  const getConflictSeverityBadge = (severity: string) => {
    const variants: Record<string, "default" | "secondary" | "destructive"> = {
      low: 'secondary',
      medium: 'default',
      high: 'destructive'
    };
    return <Badge variant={variants[severity] || 'secondary'}>{severity.toUpperCase()}</Badge>;
  };

  if (isLoading) {
    return (
      <div className="max-w-7xl mx-auto px-4 py-8">
        <div className="text-center">Loading admin dashboard...</div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto px-4 py-8">
      <h1 className="mb-6 flex items-center text-3xl font-bold">
        <Users className="mr-2" />
        Admin Dashboard
      </h1>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
        <Card className="text-center">
          <CardContent className="p-6">
            <Clock className="text-yellow-500 mb-2 mx-auto" size={32} />
            <h3 className="text-yellow-500 text-2xl font-bold">{pendingEvents.length}</h3>
            <p className="mb-0 text-gray-600">Pending Review</p>
          </CardContent>
        </Card>
        <Card className="text-center">
          <CardContent className="p-6">
            <AlertTriangle className="text-red-500 mb-2 mx-auto" size={32} />
            <h3 className="text-red-500 text-2xl font-bold">{conflictedEvents.length}</h3>
            <p className="mb-0 text-gray-600">Conflicts</p>
          </CardContent>
        </Card>
        <Card className="text-center">
          <CardContent className="p-6">
            <CheckCircle className="text-green-500 mb-2 mx-auto" size={32} />
            <h3 className="text-green-500 text-2xl font-bold">{confirmedEvents.length}</h3>
            <p className="mb-0 text-gray-600">Confirmed</p>
          </CardContent>
        </Card>
        <Card className="text-center">
          <CardContent className="p-6">
            <XCircle className="text-gray-500 mb-2 mx-auto" size={32} />
            <h3 className="text-gray-500 text-2xl font-bold">{rejectedEvents.length}</h3>
            <p className="mb-0 text-gray-600">Rejected</p>
          </CardContent>
        </Card>
      </div>

      {/* Pending Events for Review */}
      <Card className="mb-6">
        <CardHeader>
          <h3 className="flex items-center text-xl font-semibold">
            <Clock className="mr-2" />
            Events Pending Review ({pendingEvents.length})
          </h3>
        </CardHeader>
        <CardContent>
          {pendingEvents.length === 0 ? (
            <p className="text-gray-500">No events pending review.</p>
          ) : (
            <Table responsive>
              <TableHeader>
                <TableRow>
                  <TableHead>Event</TableHead>
                  <TableHead>Department</TableHead>
                  <TableHead>Date & Time</TableHead>
                  <TableHead>Organizer</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {pendingEvents.map(event => (
                  <TableRow key={event.id}>
                    <TableCell>
                      <strong>{event.eventName}</strong>
                      {event.description && (
                        <div className="text-gray-500 text-sm">{event.description}</div>
                      )}
                    </TableCell>
                    <TableCell>{event.departmentName}</TableCell>
                    <TableCell>
                      {formatDate(event.date)}<br />
                      <small className="text-gray-500">{formatTime(event.time)}</small>
                    </TableCell>
                    <TableCell>{event.organizer}</TableCell>
                    <TableCell>{getStatusBadge(event.status)}</TableCell>
                    <TableCell>
                      <div className="flex gap-2">
                        <Button
                          size="sm"
                          className="bg-green-600 hover:bg-green-700 text-white"
                          onClick={() => handleReviewEvent(event, 'approve')}
                        >
                          Approve
                        </Button>
                        <Button
                          size="sm"
                          variant="destructive"
                          onClick={() => handleReviewEvent(event, 'reject')}
                        >
                          Reject
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* Events with Conflicts */}
      {conflictedEvents.length > 0 && (
        <Card className="mb-6">
          <CardHeader>
            <h3 className="flex items-center text-xl font-semibold">
              <AlertTriangle className="mr-2" />
              Events with Conflicts ({conflictedEvents.length})
            </h3>
          </CardHeader>
          <CardContent>
            <Table responsive>
              <TableHeader>
                <TableRow>
                  <TableHead>Event</TableHead>
                  <TableHead>Department</TableHead>
                  <TableHead>Date & Time</TableHead>
                  <TableHead>Conflicts</TableHead>
                  <TableHead>Status</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {conflictedEvents.map(event => (
                  <TableRow key={event.id}>
                    <TableCell>
                      <strong>{event.eventName}</strong>
                    </TableCell>
                    <TableCell>{event.departmentName}</TableCell>
                    <TableCell>
                      {formatDate(event.date)}<br />
                      <small className="text-gray-500">{formatTime(event.time)}</small>
                    </TableCell>
                    <TableCell>
                      {event.conflicts?.map((conflict, index) => (
                        <div key={index} className="mb-1">
                          {getConflictSeverityBadge(conflict.severity)} {conflict.description}
                        </div>
                      ))}
                    </TableCell>
                    <TableCell>{getStatusBadge(event.status)}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      )}

      {/* Review Modal */}
      <Modal open={showReviewModal} onOpenChange={setShowReviewModal}>
        <ModalContent>
          <ModalHeader>
            <ModalTitle>
              {reviewAction === 'approve' ? 'Approve Event' : 'Reject Event'}
            </ModalTitle>
          </ModalHeader>
          <div className="space-y-4">
            {selectedEvent && (
              <>
                <div>
                  <h6 className="font-semibold">{selectedEvent.eventName}</h6>
                  <p className="text-gray-500">
                    {selectedEvent.departmentName} • {formatDate(selectedEvent.date)} at {formatTime(selectedEvent.time)}
                  </p>
                </div>
                <FormGroup>
                  <FormLabel>
                    {reviewAction === 'approve' ? 'Approval Comments (Optional)' : 'Rejection Reason (Required)'}
                  </FormLabel>
                  <FormTextarea
                    rows={3}
                    value={reviewComments}
                    onChange={(e) => setReviewComments(e.target.value)}
                    placeholder={
                      reviewAction === 'approve'
                        ? 'Add any comments about the approval...'
                        : 'Please provide a reason for rejection...'
                    }
                    required={reviewAction === 'reject'}
                  />
                </FormGroup>
              </>
            )}
          </div>
          <ModalFooter>
            <Button variant="outline" onClick={() => setShowReviewModal(false)}>
              Cancel
            </Button>
            <Button
              variant={reviewAction === 'approve' ? 'default' : 'destructive'}
              onClick={handleSubmitReview}
              disabled={reviewAction === 'reject' && !reviewComments.trim()}
              className={reviewAction === 'approve' ? 'bg-green-600 hover:bg-green-700 text-white' : ''}
            >
              {reviewAction === 'approve' ? 'Approve Event' : 'Reject Event'}
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </div>
  );
};

export default AdminDashboard;
