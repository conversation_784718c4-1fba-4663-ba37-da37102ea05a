import React, { useState } from 'react';
import { Container, Row, Col, Card, Badge, Button, Table, Alert, ProgressBar } from 'react-bootstrap';
import { Calendar, Users, Clock, TrendingUp, AlertTriangle, CheckCircle } from 'lucide-react';
import { useEvents } from '../../hooks/useEvents';
import { useAuth } from '../../contexts/AuthContext';
import { useDepartments } from '../../hooks/useDepartments';
import { EventStatus, EventPriority } from '../../types/event';
import { formatDate, formatTime, getRelativeTime } from '../../utils/dateUtils';
import CalendarExport from '../CalendarExport';

const DepartmentDashboard: React.FC = () => {
  const { user, permissions } = useAuth();
  const { getEventsByDepartment } = useEvents();
  const { getDepartmentById } = useDepartments();
  const [showExportModal, setShowExportModal] = useState(false);

  // Get user's department events
  const departmentEvents = user?.departmentId ? getEventsByDepartment(user.departmentId) : [];
  const userDepartment = user?.departmentId ? getDepartmentById(user.departmentId) : null;

  // Calculate statistics
  const totalEvents = departmentEvents.length;
  const draftEvents = departmentEvents.filter(e => e.status === EventStatus.DRAFT);
  const pendingEvents = departmentEvents.filter(e => e.status === EventStatus.PENDING);
  const confirmedEvents = departmentEvents.filter(e => e.status === EventStatus.CONFIRMED);
  const rejectedEvents = departmentEvents.filter(e => e.status === EventStatus.REJECTED);

  // Upcoming events (next 30 days)
  const now = new Date();
  const thirtyDaysFromNow = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);
  const upcomingEvents = confirmedEvents.filter(event => {
    const eventDate = new Date(event.date);
    return eventDate >= now && eventDate <= thirtyDaysFromNow;
  });

  // Events by priority
  const highPriorityEvents = departmentEvents.filter(e => e.priority === EventPriority.HIGH);
  const criticalEvents = departmentEvents.filter(e => e.priority === EventPriority.CRITICAL);

  // Conflicts
  const conflictedEvents = departmentEvents.filter(e => e.conflicts && e.conflicts.length > 0);

  const getStatusBadge = (status: EventStatus) => {
    const variants = {
      [EventStatus.DRAFT]: 'secondary',
      [EventStatus.PENDING]: 'warning',
      [EventStatus.CONFIRMED]: 'success',
      [EventStatus.REJECTED]: 'danger',
      [EventStatus.CANCELLED]: 'dark'
    };
    return <Badge bg={variants[status]}>{status.toUpperCase()}</Badge>;
  };

  const getPriorityBadge = (priority: EventPriority) => {
    const variants = {
      [EventPriority.LOW]: 'info',
      [EventPriority.MEDIUM]: 'primary',
      [EventPriority.HIGH]: 'warning',
      [EventPriority.CRITICAL]: 'danger'
    };
    return <Badge bg={variants[priority]}>{priority.toUpperCase()}</Badge>;
  };

  return (
    <Container className="py-4">
      <div className="d-flex justify-content-between align-items-center mb-4">
        <div>
          <h1 className="mb-1">
            <Users className="me-2" />
            Department Dashboard
          </h1>
          {userDepartment && (
            <p className="text-muted mb-0">{userDepartment.name}</p>
          )}
        </div>
        <div className="d-flex gap-2">
          {permissions.canExportCalendar && (
            <Button
              variant="outline-primary"
              onClick={() => setShowExportModal(true)}
            >
              Export Calendar
            </Button>
          )}
        </div>
      </div>

      {/* Statistics Overview */}
      <Row className="mb-4">
        <Col md={3}>
          <Card className="text-center h-100">
            <Card.Body>
              <Calendar className="text-primary mb-2" size={32} />
              <h3 className="text-primary">{totalEvents}</h3>
              <p className="mb-0">Total Events</p>
            </Card.Body>
          </Card>
        </Col>
        <Col md={3}>
          <Card className="text-center h-100">
            <Card.Body>
              <Clock className="text-warning mb-2" size={32} />
              <h3 className="text-warning">{pendingEvents.length}</h3>
              <p className="mb-0">Pending Approval</p>
            </Card.Body>
          </Card>
        </Col>
        <Col md={3}>
          <Card className="text-center h-100">
            <Card.Body>
              <CheckCircle className="text-success mb-2" size={32} />
              <h3 className="text-success">{confirmedEvents.length}</h3>
              <p className="mb-0">Confirmed</p>
            </Card.Body>
          </Card>
        </Col>
        <Col md={3}>
          <Card className="text-center h-100">
            <Card.Body>
              <TrendingUp className="text-info mb-2" size={32} />
              <h3 className="text-info">{upcomingEvents.length}</h3>
              <p className="mb-0">Upcoming (30 days)</p>
            </Card.Body>
          </Card>
        </Col>
      </Row>

      {/* Event Status Progress */}
      <Card className="mb-4">
        <Card.Header>
          <h5 className="mb-0">Event Status Overview</h5>
        </Card.Header>
        <Card.Body>
          <Row>
            <Col md={6}>
              <div className="mb-3">
                <div className="d-flex justify-content-between">
                  <span>Draft Events</span>
                  <span>{draftEvents.length}/{totalEvents}</span>
                </div>
                <ProgressBar
                  variant="secondary"
                  now={totalEvents > 0 ? (draftEvents.length / totalEvents) * 100 : 0}
                />
              </div>
              <div className="mb-3">
                <div className="d-flex justify-content-between">
                  <span>Pending Approval</span>
                  <span>{pendingEvents.length}/{totalEvents}</span>
                </div>
                <ProgressBar
                  variant="warning"
                  now={totalEvents > 0 ? (pendingEvents.length / totalEvents) * 100 : 0}
                />
              </div>
            </Col>
            <Col md={6}>
              <div className="mb-3">
                <div className="d-flex justify-content-between">
                  <span>Confirmed Events</span>
                  <span>{confirmedEvents.length}/{totalEvents}</span>
                </div>
                <ProgressBar
                  variant="success"
                  now={totalEvents > 0 ? (confirmedEvents.length / totalEvents) * 100 : 0}
                />
              </div>
              <div className="mb-3">
                <div className="d-flex justify-content-between">
                  <span>Rejected Events</span>
                  <span>{rejectedEvents.length}/{totalEvents}</span>
                </div>
                <ProgressBar
                  variant="danger"
                  now={totalEvents > 0 ? (rejectedEvents.length / totalEvents) * 100 : 0}
                />
              </div>
            </Col>
          </Row>
        </Card.Body>
      </Card>

      {/* Alerts and Notifications */}
      {(conflictedEvents.length > 0 || criticalEvents.length > 0 || highPriorityEvents.length > 0) && (
        <Row className="mb-4">
          {conflictedEvents.length > 0 && (
            <Col md={4}>
              <Alert variant="warning">
                <AlertTriangle className="me-2" size={16} />
                <strong>{conflictedEvents.length}</strong> events have conflicts
              </Alert>
            </Col>
          )}
          {criticalEvents.length > 0 && (
            <Col md={4}>
              <Alert variant="danger">
                <AlertTriangle className="me-2" size={16} />
                <strong>{criticalEvents.length}</strong> critical priority events
              </Alert>
            </Col>
          )}
          {highPriorityEvents.length > 0 && (
            <Col md={4}>
              <Alert variant="warning">
                <TrendingUp className="me-2" size={16} />
                <strong>{highPriorityEvents.length}</strong> high priority events
              </Alert>
            </Col>
          )}
        </Row>
      )}

      {/* Upcoming Events */}
      <Card className="mb-4">
        <Card.Header>
          <h5 className="mb-0">Upcoming Events (Next 30 Days)</h5>
        </Card.Header>
        <Card.Body>
          {upcomingEvents.length === 0 ? (
            <p className="text-muted mb-0">No upcoming events in the next 30 days.</p>
          ) : (
            <Table responsive>
              <thead>
                <tr>
                  <th>Event</th>
                  <th>Date & Time</th>
                  <th>Venue</th>
                  <th>Priority</th>
                  <th>Status</th>
                  <th>Time Until</th>
                </tr>
              </thead>
              <tbody>
                {upcomingEvents.slice(0, 10).map(event => (
                  <tr key={event.id}>
                    <td>
                      <strong>{event.eventName}</strong>
                      {event.description && (
                        <div className="text-muted small">{event.description}</div>
                      )}
                    </td>
                    <td>
                      {formatDate(event.date)}<br />
                      <small className="text-muted">{formatTime(event.time)}</small>
                    </td>
                    <td>{event.venue}</td>
                    <td>{getPriorityBadge(event.priority)}</td>
                    <td>{getStatusBadge(event.status)}</td>
                    <td>
                      <small className="text-muted">
                        {getRelativeTime(event.date, event.time)}
                      </small>
                    </td>
                  </tr>
                ))}
              </tbody>
            </Table>
          )}
        </Card.Body>
      </Card>

      {/* Recent Activity */}
      <Card>
        <Card.Header>
          <h5 className="mb-0">Recent Department Events</h5>
        </Card.Header>
        <Card.Body>
          {departmentEvents.length === 0 ? (
            <p className="text-muted mb-0">No events found for your department.</p>
          ) : (
            <Table responsive>
              <thead>
                <tr>
                  <th>Event</th>
                  <th>Date</th>
                  <th>Organizer</th>
                  <th>Status</th>
                  <th>Priority</th>
                  <th>Last Updated</th>
                </tr>
              </thead>
              <tbody>
                {departmentEvents.slice(0, 10).map(event => (
                  <tr key={event.id}>
                    <td>
                      <strong>{event.eventName}</strong>
                    </td>
                    <td>{formatDate(event.date)}</td>
                    <td>{event.organizer}</td>
                    <td>{getStatusBadge(event.status)}</td>
                    <td>{getPriorityBadge(event.priority)}</td>
                    <td>
                      <small className="text-muted">
                        {formatDate(event.updatedAt)}
                      </small>
                    </td>
                  </tr>
                ))}
              </tbody>
            </Table>
          )}
        </Card.Body>
      </Card>

      {/* Calendar Export Modal */}
      <CalendarExport
        show={showExportModal}
        onHide={() => setShowExportModal(false)}
      />
    </Container>
  );
};

export default DepartmentDashboard;
