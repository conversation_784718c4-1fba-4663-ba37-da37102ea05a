/* Landing Page Styles */
.landing-page {
  background: linear-gradient(135deg, #f8f9ff 0%, #e8f0ff 100%);
  min-height: 100vh;
}

/* Header Styles */
.header-section {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 1000;
}

.nav-link {
  transition: color 0.3s ease;
}

.nav-link:hover {
  color: #6366f1 !important;
}

.try-free-btn {
  background: #ff6b35;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.try-free-btn:hover {
  background: #e55a2b;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(255, 107, 53, 0.3);
}

/* Hero Section */
.hero-section {
  padding: 80px 0;
  background: linear-gradient(135deg, #f8f9ff 0%, #e8f0ff 100%);
  position: relative;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 800;
  line-height: 1.1;
  margin-bottom: 2rem;
}

.hero-subtitle {
  font-size: 1.25rem;
  color: #6b7280;
  margin-bottom: 2.5rem;
  max-width: 500px;
}

.cta-button {
  background: #ff6b35;
  border: none;
  border-radius: 12px;
  font-weight: 600;
  padding: 12px 32px;
  font-size: 1.1rem;
  transition: all 0.3s ease;
}

.cta-button:hover {
  background: #e55a2b;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(255, 107, 53, 0.3);
}

.demo-link {
  font-size: 1.1rem;
  transition: all 0.3s ease;
}

.demo-link:hover {
  transform: translateX(5px);
}

/* Device Mockups */
.hero-devices {
  height: 600px;
}

.device-mockup {
  position: absolute;
  border-radius: 20px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
  transition: transform 0.3s ease;
}

.device-mockup:hover {
  transform: translateY(-10px);
}

.device-desktop {
  width: 400px;
  height: 250px;
  background: white;
  top: 50px;
  right: 0;
  z-index: 3;
}

.device-tablet {
  width: 200px;
  height: 280px;
  background: white;
  top: 200px;
  right: 300px;
  z-index: 2;
  border-radius: 25px;
}

.device-phone {
  width: 120px;
  height: 240px;
  background: white;
  top: 100px;
  right: 150px;
  z-index: 1;
  border-radius: 30px;
}

.device-frame {
  width: 100%;
  height: 100%;
  padding: 20px;
  border-radius: inherit;
}

.device-screen {
  width: 100%;
  height: 100%;
  background: #f8f9fa;
  border-radius: 12px;
  overflow: hidden;
}

.mockup-content {
  padding: 15px;
  height: 100%;
}

.mockup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #e9ecef;
}

.mockup-logo {
  font-weight: bold;
  color: #6366f1;
  font-size: 1.2rem;
}

.mockup-nav {
  display: flex;
  gap: 15px;
  font-size: 0.9rem;
  color: #6b7280;
}

.event-card {
  background: white;
  border-radius: 8px;
  padding: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 12px;
}

.event-avatar {
  width: 40px;
  height: 40px;
  background: linear-gradient(45deg, #6366f1, #8b5cf6);
  border-radius: 50%;
}

.event-details {
  flex: 1;
}

.event-title {
  height: 12px;
  background: #e5e7eb;
  border-radius: 4px;
  margin-bottom: 8px;
}

.event-time {
  height: 8px;
  background: #f3f4f6;
  border-radius: 4px;
  width: 60%;
}

/* Trusted By Section */
.trusted-by-section {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
}

.trusted-text {
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.logo-item {
  font-size: 1.2rem;
  opacity: 0.6;
  transition: opacity 0.3s ease;
}

.logo-item:hover {
  opacity: 1;
}

/* Features Section */
.features-section {
  background: white;
  padding: 100px 0;
}

.section-title {
  font-weight: 800;
  line-height: 1.1;
}

.section-subtitle {
  color: #6b7280;
  max-width: 600px;
  margin: 0 auto;
}

.feature-card {
  height: 100%;
  transition: all 0.3s ease;
  border: 2px solid rgba(99, 102, 241, 0.1) !important;
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(99, 102, 241, 0.15);
  border-color: rgba(99, 102, 241, 0.3) !important;
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero-title {
    font-size: 2.5rem;
  }

  .hero-devices {
    display: none;
  }

  .device-mockup {
    position: relative;
    margin: 20px auto;
  }

  .section-title {
    font-size: 2.5rem;
  }
}
