import React from 'react';
import { Link } from 'react-router-dom';
import { Clock, Users, Building, ArrowRight, Check } from 'lucide-react';
import { Button, Col, Row, Container } from 'react-bootstrap';
import { <PERSON>, CardHeader, CardTitle, CardFooter } from 'react-bootstrap';
import WithKalendaBrand from '../Brand';
import './Landing.css';

interface PricingTierProps {
    title: string;
    price: number;
    features: string[];
    recommended?: boolean;
}

const PricingTier: React.FC<PricingTierProps> = ({ title, price, features, recommended = false }) => (
    <Col md={4}>
        <Card className={`h-100  shadow-sm ${recommended ? 'border-2 border-light m-2 h-100' : ''}`}>
            <CardHeader>
                {recommended && <span className="bg-primary text-white px-2 py-1 rounded-3 text-sm fw-semibold self-start mb-2">Recommended</span>}
                <CardTitle>{title} £{price}<span className="text-xl text-muted-foreground font-normal">/mo</span></CardTitle>
                <Card.Text className="text-3xl fw-bold">

                </Card.Text>
            </CardHeader>
            <Card.Body>
                <ul className="space-y-2">
                    {features.map((feature, index) => (
                        <li key={index} className="flex items-center">
                            <Check className="h-5 w-5 text-green-500 mr-2" />
                            <span>{feature}</span>
                        </li>
                    ))}
                </ul>
            </Card.Body>
            <CardFooter>
                <Button className="w-100 fw-bolder fs-6" variant={recommended ? "default" : "outline"}>
                    Choose Plan
                </Button>
            </CardFooter>
        </Card>
    </Col>
);

const LandingPage: React.FC = () => {
    return (
        <Container className="min-h-screen bg-background">
            <Row className="h-100 my-5 rounded-5">

                <header className="d-flex flex-row mx-auto p-3 flex justify-content-between items-center rounded-5 my-2">
                    <div className="flex items-center space-x-2">
                        <WithKalendaBrand />
                    </div>
                    <nav>
                        <ul className="nav flex-row">
                            <li><Link to="/features" className="nav-item nav-link ms-2 text-dark fs-5 fw-semibold">Features</Link></li>
                            <li><Link to="/pricing" className="nav-item nav-link ms-2 text-dark fs-5 fw-semibold">Pricing</Link></li>
                            <li><Link to="/contact" className="nav-item nav-link ms-2 text-dark fs-5 fw-semibold">Contact</Link></li>
                        </ul>
                    </nav>
                </header>
            </Row>
            <Container className="mx-auto px-4 py-3">
                <Row className="h-100 my-5 rounded-5 p-5" style={{ backgroundColor: '#E9EFEC' }}>
                    <section className="text-center my-2">
                        <h1 className="display-4 fw-bolder text-foreground mb-4">Streamline Your Organization's Calendar</h1>
                        <p className="display-6 text-muted-foreground mb-8">Effortlessly manage events across all departments with WithKalenda</p>
                        <Button variant='dark' size="lg" className="fw-bolder fs-6">Get Started Free</Button>
                    </section>

                </Row>

                <Row id="features" className="bg-white p-5 rounded-5 my-5">
                    <Container>
                        <Row>
                            <h2 className="text-center text-dark text-opacity-50 my-3" style={{ fontSize: '4.5rem', fontWeight: '700', letterSpacing: '0.05em' }}>Features</h2>
                        </Row>
                        <Row>

                            <Col>
                                <Card>
                                    <CardHeader>
                                        <CardTitle className="fs-5 fw-bold p-2"><Clock className="text-dark mx-2" />
                                            Centralized Scheduling</CardTitle>
                                    </CardHeader>
                                    <Card.Body>
                                        <p>All departments can access and update a single, unified calendar.</p>
                                    </Card.Body>
                                </Card>
                            </Col>
                            <Col>
                                <Card>
                                    <CardHeader>
                                        <CardTitle className="fs-5 fw-bold p-2"><Users className="text-dark mx-2" /> Multi-Depart. Coordination
                                        </CardTitle>

                                    </CardHeader>
                                    <Card.Body>
                                        <p>Easily coordinate events across different teams and departments.</p>
                                    </Card.Body>
                                </Card>
                            </Col>
                            <Col>
                                <Card>
                                    <CardHeader>
                                        <CardTitle className="fs-5 fw-bold p-2"><Building className="text-dark mx-2" /> Company-Wide Overview
                                        </CardTitle>
                                    </CardHeader>
                                    <Card.Body>
                                        <p>Get a birds-eye view of all organizational events and activities.</p>
                                    </Card.Body>
                                </Card>
                            </Col>
                        </Row>
                    </Container>
                </Row>

                <Row id="pricing" className="rounded-5 my-5 p-5" style={{ backgroundColor: '#E9EFEC' }}>
                    <h2 className="display-5 fw-bold text-center text-dark mb-3">Choose the Perfect Plan for Your Organization</h2>
                    <div className="d-flex">
                        <PricingTier
                            title="Standard"
                            price={4.99}
                            features={[
                                "5 events per month",
                                "Basic calendar features",
                                "Email support"
                            ]}
                        />
                        <PricingTier
                            title="Essentials"
                            price={7.99}
                            features={[
                                "25 events per month",
                                "Advanced scheduling tools",
                                "Department-specific views",
                                "Priority email support"
                            ]}
                            recommended={true}
                        />
                        <PricingTier
                            title="Premium"
                            price={12.99}
                            features={[
                                "Unlimited events",
                                "Unlimited shares",
                                "Custom integrations",
                                "Dedicated account manager",
                                "24/7 phone support"
                            ]}
                        />
                    </div>
                </Row>

                <Row id="cta" className="justify-content-center text-center my-3 p-5 bg-light rounded-4">
                    <h2 className="text-3xl fw-bold text-dark text-opacity-75 mb-3">Ready to Optimize Your Organization's Calendar?</h2>
                    <p className="text-xl text-muted-foreground mb-8">Join thousands of companies already using OrganiCal to streamline their scheduling.</p>
                    <Button size="lg" variant={`dark`} className="d-flex justify-content-center align-items-center w-25 fw-bolder">
                        Start Your Free Trial <ArrowRight className="ml-2 h-5 w-5" />
                    </Button>
                </Row>


                <footer className="bg-muted py-8">
                    <div className="container mx-auto px-4 text-center text-muted-foreground">
                        <p className="p-2 text-dark text-opacity-50 fw-bold"><span className='p-3'> <WithKalendaBrand /></span> &copy; {new Date().getFullYear()}  WithKalenda. All rights reserved.</p>
                    </div>
                </footer>
            </Container>
        </Container >
    );
};

export default LandingPage;;;