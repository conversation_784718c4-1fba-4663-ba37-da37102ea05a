import React from 'react';
import { Link } from 'react-router-dom';
import {
    Users,
    BarChart3,
    ArrowRight,
    Zap,
    Calendar,
    Bell,
    Download
} from 'lucide-react';
import { Button } from '../ui/button';
import Layout from '../Layout';

// Feature card component for the "Experience the Future" section
interface FeatureCardProps {
    icon: React.ReactNode;
    title: string;
    description: string;
}

const FeatureCard: React.FC<FeatureCardProps> = ({ icon, title, description }) => (
    <div className="p-6 rounded-xl border border-brand-blue/20 bg-brand-blue/5 hover:border-brand-blue/30 hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
        <div className="mb-4 text-brand-blue">
            {icon}
        </div>
        <h4 className="font-bold mb-3 text-lg">{title}</h4>
        <p className="text-gray-600 mb-0">{description}</p>
    </div>
);

// Device mockup component
const DeviceMockup: React.FC<{ type: 'phone' | 'tablet' | 'desktop'; className?: string; }> = ({ type, className = '' }) => {
    const getDeviceClasses = () => {
        switch (type) {
            case 'desktop':
                return 'w-96 h-60 top-12 right-0 z-30';
            case 'tablet':
                return 'w-48 h-72 top-48 right-72 z-20 rounded-3xl';
            case 'phone':
                return 'w-28 h-56 top-24 right-36 z-10 rounded-3xl';
            default:
                return '';
        }
    };

    return (
        <div className={`absolute bg-white rounded-2xl shadow-2xl transition-transform hover:-translate-y-2 ${getDeviceClasses()} ${className}`}>
            <div className="w-full h-full p-4 rounded-2xl">
                <div className="w-full h-full bg-gray-50 rounded-xl overflow-hidden">
                    <div className="p-3 h-full">
                        <div className="flex justify-between items-center mb-4 pb-2 border-b border-gray-200">
                            <div className="font-bold text-brand-blue text-sm">WithKalenda</div>
                            <div className="flex gap-3 text-xs text-gray-500">
                                <span>Features</span>
                                <span>Pricing</span>
                                <span>Resources</span>
                            </div>
                        </div>
                        <div className="bg-white rounded-lg p-3 shadow-sm flex items-center gap-3">
                            <div className="w-8 h-8 bg-gradient-to-br from-brand-blue to-purple-600 rounded-full"></div>
                            <div className="flex-1">
                                <div className="h-3 bg-gray-200 rounded mb-2"></div>
                                <div className="h-2 bg-gray-100 rounded w-3/5"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

const LandingPage: React.FC = () => {
    return (
        <Layout>
            <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
                {/* Hero Section */}
                <section className="relative overflow-hidden">
                    <div className="wk-container">
                        <div className="flex items-center min-h-screen">
                            <div className="w-full lg:w-1/2 hero-content">
                                <h1 className="text-5xl lg:text-6xl font-bold mb-6 leading-tight">
                                    Coordinate Events.
                                    <br />
                                    <span className="text-brand-blue">Unite Communities.</span>
                                </h1>
                                <p className="text-xl text-gray-600 mb-8 max-w-lg">
                                    <span className='font-bold'>WithKalenda</span> brings teams, companies, religious organizations, and charities together
                                    through seamless collaborative event planning and calendar management.
                                </p>
                                <div className="flex gap-4 items-center">
                                    <Link to="/demo">
                                        <Button variant="brand-orange" size="lg" className="px-8">
                                            Request a Demo
                                        </Button>
                                    </Link>
                                </div>
                            </div>
                            <div className="hidden lg:block w-1/2 relative">
                                <DeviceMockup type="desktop" className="device-desktop" />
                                <DeviceMockup type="tablet" className="device-tablet" />
                                <DeviceMockup type="phone" className="device-phone" />
                            </div>
                        </div>
                    </div>

                    {/* Trusted by logos */}
                    <div className="py-12 bg-white/80 backdrop-blur-sm">
                        <div className="wk-container">
                            <p className="text-center mb-8 text-gray-500 text-sm uppercase tracking-wider">Trusted by Event Professionals Worldwide</p>
                            <div className="flex justify-center items-center gap-8 flex-wrap">
                                <div className="font-bold text-gray-400 text-lg opacity-60 hover:opacity-100 transition-opacity">tetra</div>
                                <div className="font-bold text-gray-400 text-lg opacity-60 hover:opacity-100 transition-opacity">orblong</div>
                                <div className="font-bold text-gray-400 text-lg opacity-60 hover:opacity-100 transition-opacity">cyclex</div>
                                <div className="font-bold text-gray-400 text-lg opacity-60 hover:opacity-100 transition-opacity">bombas</div>
                                <div className="font-bold text-gray-400 text-lg opacity-60 hover:opacity-100 transition-opacity">dimos</div>
                            </div>
                        </div>
                    </div>
                </section>

                {/* Experience the Future Section */}
                <section className="py-20 bg-white">
                    <div className="wk-container">
                        <div className="text-center mb-16">
                            <h2 className="text-4xl lg:text-5xl font-bold mb-6">
                                Experience the Future of Event
                                <br />
                                <span className="text-brand-blue">Planning</span>
                            </h2>
                            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                                Discover how our comprehensive event management solution can transform your planning process
                                and deliver exceptional experiences every time.
                            </p>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
                            <FeatureCard
                                icon={<Zap size={32} />}
                                title="Corporate Event Management"
                                description="Perfect for companies managing events across HR, Marketing, Sales, and Operations departments with intelligent conflict detection."
                            />
                            <FeatureCard
                                icon={<Users size={32} />}
                                title="Community & Religious Events"
                                description="Ideal for religious organizations coordinating worship services, community outreach, youth programs, and special celebrations."
                            />
                            <FeatureCard
                                icon={<BarChart3 size={32} />}
                                title="Charity & Fundraising Events"
                                description="Streamline charity events, fundraisers, volunteer coordination, and donor appreciation events with approval workflows."
                            />
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
                            <FeatureCard
                                icon={<Calendar size={32} />}
                                title="Team Project Coordination"
                                description="Help teams coordinate project milestones, meetings, training sessions, and team-building activities efficiently."
                            />
                            <FeatureCard
                                icon={<Bell size={32} />}
                                title="Smart Conflict Resolution"
                                description="Automatically detect scheduling conflicts across all your organization's events and suggest optimal alternatives."
                            />
                            <FeatureCard
                                icon={<Download size={32} />}
                                title="Universal Calendar Integration"
                                description="Export to Google Calendar, Outlook, Apple Calendar, and more. Share events seamlessly across all platforms."
                            />
                        </div>

                        <div className="text-center">
                            <Link to="/signup">
                                <Button variant="brand-orange" size="lg" className="px-8 mb-4">
                                    Start Coordinating Today
                                </Button>
                            </Link>
                            <div>
                                <Link to="/demo" className="text-brand-blue font-semibold hover:text-brand-blue/80 transition-colors">
                                    See WithKalenda in Action →
                                </Link>
                            </div>
                        </div>
                    </div>
                </section>
            </div>
        </Layout>
    );
};

export default LandingPage;
