import React from 'react';
import { Link } from 'react-router-dom';
import {
    <PERSON>,
    <PERSON><PERSON>hart3,
    Arrow<PERSON><PERSON>,
    Zap
} from 'lucide-react';
import { Button, Col, Row, Container } from 'react-bootstrap';
import With<PERSON>alendaBrand from '../Brand';
import './Landing.css';

// Feature card component for the "Experience the Future" section
interface FeatureCardProps {
    icon: React.ReactNode;
    title: string;
    description: string;
}

const FeatureCard: React.FC<FeatureCardProps> = ({ icon, title, description }) => (
    <div className="feature-card p-4 rounded-4 border border-primary" style={{ backgroundColor: 'rgba(99, 102, 241, 0.05)' }}>
        <div className="mb-3 text-primary">
            {icon}
        </div>
        <h4 className="fw-bold mb-3">{title}</h4>
        <p className="text-muted mb-0">{description}</p>
    </div>
);

// Device mockup component
const DeviceMockup: React.FC<{ type: 'phone' | 'tablet' | 'desktop'; className?: string; }> = ({ type, className = '' }) => (
    <div className={`device-mockup ${type} ${className}`}>
        <div className="device-frame">
            <div className="device-screen">
                <div className="mockup-content">
                    <div className="mockup-header">
                        <div className="mockup-logo">Planpal</div>
                        <div className="mockup-nav">
                            <span>Features</span>
                            <span>Pricing</span>
                            <span>Resources</span>
                        </div>
                    </div>
                    <div className="mockup-body">
                        <div className="event-card">
                            <div className="event-avatar"></div>
                            <div className="event-details">
                                <div className="event-title"></div>
                                <div className="event-time"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
);

const LandingPage: React.FC = () => {
    return (
        <div className="landing-page">
            {/* Header */}
            <header className="header-section">
                <Container>
                    <div className="d-flex justify-content-between align-items-center py-3">
                        <WithKalendaBrand />
                        <nav className="d-flex gap-4">
                            <Link to="/features" className="nav-link text-decoration-none text-dark fw-semibold">Features</Link>
                            <Link to="/pricing" className="nav-link text-decoration-none text-dark fw-semibold">Pricing</Link>
                            <Link to="/resources" className="nav-link text-decoration-none text-dark fw-semibold">Resources</Link>
                        </nav>
                        <div className="d-flex gap-2">
                            <Link to="/login">
                                <Button variant="outline-primary">Log In</Button>
                            </Link>
                            <Link to="/signup">
                                <Button variant="primary" className="try-free-btn">Try for Free</Button>
                            </Link>
                        </div>
                    </div>
                </Container>
            </header>

            {/* Hero Section with Floating Devices */}
            <section className="hero-section position-relative overflow-hidden">
                <Container>
                    <Row className="align-items-center min-vh-100">
                        <Col lg={6} className="hero-content">
                            <h1 className="hero-title display-3 fw-bold mb-4">
                                Simplify Event Planning.
                                <br />
                                <span className="text-primary">Amplify Success.</span>
                            </h1>
                            <p className="hero-subtitle lead text-muted mb-4">
                                Our all-in-one Event Planning Platform streamlines every aspect of event management,
                                from initial planning to post-event analysis.
                            </p>
                            <div className="hero-actions d-flex gap-3">
                                <Link to="/signup">
                                    <Button size="lg" className="cta-button px-4" style={{ backgroundColor: '#ff6b35', border: 'none' }}>
                                        Try for Free
                                    </Button>
                                </Link>
                                <Link to="/demo" className="demo-link text-decoration-none text-primary fw-semibold d-flex align-items-center">
                                    Request a Demo <ArrowRight className="ms-2" size={16} />
                                </Link>
                            </div>
                        </Col>
                        <Col lg={6} className="hero-devices position-relative">
                            <DeviceMockup type="desktop" className="device-desktop" />
                            <DeviceMockup type="tablet" className="device-tablet" />
                            <DeviceMockup type="phone" className="device-phone" />
                        </Col>
                    </Row>
                </Container>

                {/* Trusted by logos */}
                <div className="trusted-by-section py-5">
                    <Container>
                        <p className="trusted-text text-center mb-4 text-muted">Trusted by Event Professionals Worldwide</p>
                        <div className="logos-container d-flex justify-content-center align-items-center gap-5 flex-wrap">
                            <div className="logo-item fw-bold text-muted">tetra</div>
                            <div className="logo-item fw-bold text-muted">orblong</div>
                            <div className="logo-item fw-bold text-muted">cyclex</div>
                            <div className="logo-item fw-bold text-muted">bombas</div>
                            <div className="logo-item fw-bold text-muted">dimos</div>
                        </div>
                    </Container>
                </div>
            </section>

            {/* Experience the Future Section */}
            <section className="features-section py-5">
                <Container>
                    <Row className="text-center mb-5">
                        <Col>
                            <h2 className="section-title display-4 fw-bold mb-4">
                                Experience the Future of Event
                                <br />
                                <span className="text-primary">Planning</span>
                            </h2>
                            <p className="section-subtitle lead text-muted">
                                Discover how our comprehensive event management solution can transform your planning process
                                and deliver exceptional experiences every time.
                            </p>
                        </Col>
                    </Row>

                    <Row className="g-4 mb-5">
                        <Col md={4}>
                            <FeatureCard
                                icon={<Zap size={32} />}
                                title="Streamlined Event Planning"
                                description="Simplify your event planning process with our intuitive tools and automated workflows that save time and reduce complexity."
                            />
                        </Col>
                        <Col md={4}>
                            <FeatureCard
                                icon={<Users size={32} />}
                                title="Enhanced Collaboration"
                                description="Bring your team together with real-time collaboration features, task management, and seamless communication tools."
                            />
                        </Col>
                        <Col md={4}>
                            <FeatureCard
                                icon={<BarChart3 size={32} />}
                                title="Data-Driven Insights"
                                description="Make informed decisions with comprehensive analytics and reporting that help you understand event performance and ROI."
                            />
                        </Col>
                    </Row>

                    <div className="text-center">
                        <Link to="/signup">
                            <Button size="lg" className="cta-button px-4" style={{ backgroundColor: '#ff6b35', border: 'none' }}>
                                Try for Free
                            </Button>
                        </Link>
                        <div className="mt-3">
                            <Link to="/demo" className="demo-link text-decoration-none text-primary fw-semibold">
                                Request a Demo →
                            </Link>
                        </div>
                    </div>
                </Container>
            </section>
        </div>
    );
};

export default LandingPage;
