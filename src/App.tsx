import React, { Fragment, Suspense } from 'react';
import { BrowserRouter as Router, Route, Routes } from 'react-router-dom';
import Layout from './components/Layout';
import DashHome from './pages/Dashboard/Home';
import Features from './pages/Features';
import About from './pages/About';
import Contact from './pages/Contact';
import Login from './pages/Login';
import Signup from './pages/Signup';
import Privacy from "./pages/Privacy";
import Terms from "./pages/Terms";
import Planner from "./components/Planner";
import LandingPage from "./components/Landing";
import PricingPage from './pages/Pricing';
import HomePage from './components/Home';

// Remove the commented out duplicate import of LandingPage
// import LandingPage from './components/Landing';

const App: React.FC = () => {
  return (
    <Fragment>
      <Suspense fallback={<div>Loading...</div>}>
        <Router>
          <Routes>
            <Route path="/" element={<HomePage />} />  {/* Homepage as a standalone route */}
            <Route path="landing" element={<LandingPage />} />
            <Route path="planner" element={<Planner />} />
            <Route path="features" element={<Features />} />
            <Route path="pricing" element={<PricingPage />} />
            <Route path="about" element={<About />} />
            <Route path="contact" element={<Contact />} />
            <Route path="login" element={<Login />} />
            <Route path="signup" element={<Signup />} />
            <Route path="privacy" element={<Privacy />} />
            <Route path="terms" element={<Terms />} />

            {/* Dashboard Routes */}
            <Route path="/dashboard" element={<DashHome />}>
              <Route path="new" element={<Planner />} />
            </Route>
          </Routes>
        </Router>
      </Suspense>
    </Fragment>
  );
};

export default App;
