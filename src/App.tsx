import React, { Suspense } from 'react';
import { BrowserRouter as Router, Route, Routes } from 'react-router-dom';
import './styles/global.css';
import { AuthProvider } from './contexts/AuthContext';
import Layout from './components/Layout';
import ProtectedRoute from './components/ProtectedRoute';
import DashHome from './pages/Dashboard/Home';
import Features from './pages/Features';
import About from './pages/About';
import Contact from './pages/Contact';
import Demo from './pages/Demo';
import Login from './pages/Login';
import Signup from './pages/Signup';
import ForgotPassword from './pages/ForgotPassword';
import Profile from './pages/Profile';
import Privacy from "./pages/Privacy";
import Terms from "./pages/Terms";
import Planner from "./components/Planner";
import LandingPage from "./components/Landing";
import Pricing from './pages/Pricing';
import HomePage from './components/Home';

const App: React.FC = () => {
  return (
    <AuthProvider>
      <Router>
        <Suspense fallback={<div>Loading...</div>}>
          <Routes>
            {/* Public routes with layout */}
            <Route path="/" element={<LandingPage />} />
            <Route path="landing" element={
              <HomePage />
            } />
            <Route path="features" element={
              <Layout>
                <Features />
              </Layout>
            } />
            <Route path="pricing" element={
              <Pricing />
            } />
            <Route path="demo" element={
              <Demo />
            } />
            <Route path="about" element={
              <Layout>
                <About />
              </Layout>
            } />
            <Route path="contact" element={
              <Layout>
                <Contact />
              </Layout>
            } />
            <Route path="privacy" element={
              <Layout>
                <Privacy />
              </Layout>
            } />
            <Route path="terms" element={
              <Layout>
                <Terms />
              </Layout>
            } />

            {/* Auth routes without header/footer */}
            <Route path="login" element={
              <Layout showHeader={false} showFooter={false}>
                <Login />
              </Layout>
            } />
            <Route path="signup" element={
              <Layout showHeader={false} showFooter={false}>
                <Signup />
              </Layout>
            } />
            <Route path="forgot-password" element={
              <Layout showHeader={false} showFooter={false}>
                <ForgotPassword />
              </Layout>
            } />

            {/* Protected routes */}
            <Route path="planner" element={
              <ProtectedRoute>
                <Layout>
                  <Planner />
                </Layout>
              </ProtectedRoute>
            } />
            <Route path="/dashboard" element={
              <ProtectedRoute>
                <DashHome />
              </ProtectedRoute>
            }>
              <Route path="new" element={<Planner />} />
            </Route>
            <Route path="/profile" element={
              <ProtectedRoute>
                <Profile />
              </ProtectedRoute>
            } />
            <Route path="/admin" element={
              <ProtectedRoute>
                <Layout>
                  <div>Admin Dashboard will be here</div>
                </Layout>
              </ProtectedRoute>
            } />
          </Routes>
        </Suspense>
      </Router>
    </AuthProvider>
  );
};

export default App;
