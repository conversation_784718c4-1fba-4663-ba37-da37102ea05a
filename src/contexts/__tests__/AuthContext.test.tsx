import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
// Jest is used instead of vitest
import { AuthProvider, useAuth } from '../AuthContext';
import { sampleUsers } from '../../data/sampleUsers';
import { UserRole } from '../../types/user';

// Mock localStorage
const mockLocalStorage = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn()
};

Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage
});

// Mock getUserPermissions
jest.mock('../../utils/permissions', () => ({
  getUserPermissions: jest.fn((user) => ({
    canCreateEvent: user?.role === UserRole.ADMIN || user?.role === UserRole.TEAM_LEADER,
    canEditEvent: () => user?.role === UserRole.ADMIN,
    canDeleteEvent: () => user?.role === UserRole.ADMIN,
    canApproveEvent: user?.role === UserRole.ADMIN,
    canRejectEvent: user?.role === UserRole.ADMIN,
    canViewAllEvents: user?.role === UserRole.ADMIN,
    canManageDepartments: user?.role === UserRole.ADMIN,
    canManageUsers: user?.role === UserRole.ADMIN,
    canSetDeadlines: user?.role === UserRole.ADMIN,
    canExportCalendar: true,
    canViewAnalytics: user?.role === UserRole.ADMIN || user?.role === UserRole.TEAM_LEADER
  }))
}));

// Test component that uses the auth context
const TestComponent: React.FC = () => {
  const { isAuthenticated, user, permissions, login, logout, isLoading } = useAuth();

  if (isLoading) {
    return <div>Loading...</div>;
  }

  return (
    <div>
      <div data-testid="auth-status">
        {isAuthenticated ? 'Authenticated' : 'Not Authenticated'}
      </div>
      <div data-testid="user-info">
        {user ? `${user.firstName} ${user.lastName}` : 'No User'}
      </div>
      <div data-testid="permissions">
        {permissions.canCreateEvent ? 'Can Create' : 'Cannot Create'}
      </div>
      <button
        data-testid="login-btn"
        onClick={() => login(sampleUsers[0], 'test-token')}
      >
        Login
      </button>
      <button data-testid="logout-btn" onClick={logout}>
        Logout
      </button>
    </div>
  );
};

const renderWithAuthProvider = (component: React.ReactElement) => {
  return render(<AuthProvider>{component}</AuthProvider>);
};

describe('AuthContext', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockLocalStorage.getItem.mockReturnValue(null);
  });

  it('provides initial unauthenticated state', async () => {
    renderWithAuthProvider(<TestComponent />);

    await waitFor(() => {
      expect(screen.getByTestId('auth-status')).toHaveTextContent('Not Authenticated');
      expect(screen.getByTestId('user-info')).toHaveTextContent('No User');
      expect(screen.getByTestId('permissions')).toHaveTextContent('Cannot Create');
    });
  });

  it('shows loading state initially', () => {
    renderWithAuthProvider(<TestComponent />);
    
    expect(screen.getByText('Loading...')).toBeInTheDocument();
  });

  it('authenticates user on login', async () => {
    renderWithAuthProvider(<TestComponent />);

    await waitFor(() => {
      expect(screen.getByTestId('auth-status')).toHaveTextContent('Not Authenticated');
    });

    const loginBtn = screen.getByTestId('login-btn');
    fireEvent.click(loginBtn);

    await waitFor(() => {
      expect(screen.getByTestId('auth-status')).toHaveTextContent('Authenticated');
      expect(screen.getByTestId('user-info')).toHaveTextContent('Admin User');
      expect(screen.getByTestId('permissions')).toHaveTextContent('Can Create');
    });

    expect(mockLocalStorage.setItem).toHaveBeenCalledWith('authToken', 'test-token');
    expect(mockLocalStorage.setItem).toHaveBeenCalledWith('userData', JSON.stringify(sampleUsers[0]));
  });

  it('logs out user and clears storage', async () => {
    renderWithAuthProvider(<TestComponent />);

    // First login
    await waitFor(() => {
      expect(screen.getByTestId('auth-status')).toHaveTextContent('Not Authenticated');
    });

    const loginBtn = screen.getByTestId('login-btn');
    fireEvent.click(loginBtn);

    await waitFor(() => {
      expect(screen.getByTestId('auth-status')).toHaveTextContent('Authenticated');
    });

    // Then logout
    const logoutBtn = screen.getByTestId('logout-btn');
    fireEvent.click(logoutBtn);

    await waitFor(() => {
      expect(screen.getByTestId('auth-status')).toHaveTextContent('Not Authenticated');
      expect(screen.getByTestId('user-info')).toHaveTextContent('No User');
      expect(screen.getByTestId('permissions')).toHaveTextContent('Cannot Create');
    });

    expect(mockLocalStorage.removeItem).toHaveBeenCalledWith('authToken');
    expect(mockLocalStorage.removeItem).toHaveBeenCalledWith('userData');
  });

  it('restores authentication from localStorage on mount', async () => {
    const userData = sampleUsers[0];
    mockLocalStorage.getItem.mockImplementation((key) => {
      if (key === 'authToken') return 'stored-token';
      if (key === 'userData') return JSON.stringify(userData);
      return null;
    });

    renderWithAuthProvider(<TestComponent />);

    await waitFor(() => {
      expect(screen.getByTestId('auth-status')).toHaveTextContent('Authenticated');
      expect(screen.getByTestId('user-info')).toHaveTextContent('Admin User');
    });
  });

  it('handles invalid JSON in localStorage gracefully', async () => {
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
    
    mockLocalStorage.getItem.mockImplementation((key) => {
      if (key === 'authToken') return 'stored-token';
      if (key === 'userData') return 'invalid-json';
      return null;
    });

    renderWithAuthProvider(<TestComponent />);

    await waitFor(() => {
      expect(screen.getByTestId('auth-status')).toHaveTextContent('Not Authenticated');
    });

    expect(consoleSpy).toHaveBeenCalledWith('Error parsing user data:', expect.any(Error));
    expect(mockLocalStorage.removeItem).toHaveBeenCalledWith('authToken');
    expect(mockLocalStorage.removeItem).toHaveBeenCalledWith('userData');

    consoleSpy.mockRestore();
  });

  it('handles missing token in localStorage', async () => {
    mockLocalStorage.getItem.mockImplementation((key) => {
      if (key === 'userData') return JSON.stringify(sampleUsers[0]);
      return null; // No token
    });

    renderWithAuthProvider(<TestComponent />);

    await waitFor(() => {
      expect(screen.getByTestId('auth-status')).toHaveTextContent('Not Authenticated');
    });
  });

  it('handles missing userData in localStorage', async () => {
    mockLocalStorage.getItem.mockImplementation((key) => {
      if (key === 'authToken') return 'stored-token';
      return null; // No userData
    });

    renderWithAuthProvider(<TestComponent />);

    await waitFor(() => {
      expect(screen.getByTestId('auth-status')).toHaveTextContent('Not Authenticated');
    });
  });

  it('calculates permissions correctly for different user roles', async () => {
    const teamLeaderUser = sampleUsers.find(u => u.role === UserRole.TEAM_LEADER);
    
    const TestPermissionsComponent: React.FC = () => {
      const { permissions, login, isLoading } = useAuth();

      if (isLoading) return <div>Loading...</div>;

      return (
        <div>
          <div data-testid="can-create">{permissions.canCreateEvent ? 'Yes' : 'No'}</div>
          <div data-testid="can-approve">{permissions.canApproveEvent ? 'Yes' : 'No'}</div>
          <div data-testid="can-manage">{permissions.canManageDepartments ? 'Yes' : 'No'}</div>
          <button
            data-testid="login-team-leader"
            onClick={() => login(teamLeaderUser!, 'token')}
          >
            Login as Team Leader
          </button>
        </div>
      );
    };

    renderWithAuthProvider(<TestPermissionsComponent />);

    await waitFor(() => {
      expect(screen.getByTestId('can-create')).toHaveTextContent('No');
    });

    const loginBtn = screen.getByTestId('login-team-leader');
    fireEvent.click(loginBtn);

    await waitFor(() => {
      expect(screen.getByTestId('can-create')).toHaveTextContent('Yes');
      expect(screen.getByTestId('can-approve')).toHaveTextContent('No');
      expect(screen.getByTestId('can-manage')).toHaveTextContent('No');
    });
  });

  it('throws error when useAuth is used outside AuthProvider', () => {
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
    
    const TestComponentOutsideProvider: React.FC = () => {
      useAuth();
      return <div>Test</div>;
    };

    expect(() => {
      render(<TestComponentOutsideProvider />);
    }).toThrow('useAuth must be used within an AuthProvider');

    consoleSpy.mockRestore();
  });

  it('updates permissions when user changes', async () => {
    const TestUserChangeComponent: React.FC = () => {
      const { permissions, login, isLoading } = useAuth();

      if (isLoading) return <div>Loading...</div>;

      return (
        <div>
          <div data-testid="permissions-status">
            {permissions.canApproveEvent ? 'Admin' : 'Not Admin'}
          </div>
          <button
            data-testid="login-admin"
            onClick={() => login(sampleUsers[0], 'token')}
          >
            Login Admin
          </button>
          <button
            data-testid="login-employee"
            onClick={() => login(sampleUsers.find(u => u.role === UserRole.EMPLOYEE)!, 'token')}
          >
            Login Employee
          </button>
        </div>
      );
    };

    renderWithAuthProvider(<TestUserChangeComponent />);

    await waitFor(() => {
      expect(screen.getByTestId('permissions-status')).toHaveTextContent('Not Admin');
    });

    // Login as admin
    fireEvent.click(screen.getByTestId('login-admin'));

    await waitFor(() => {
      expect(screen.getByTestId('permissions-status')).toHaveTextContent('Admin');
    });

    // Switch to employee
    fireEvent.click(screen.getByTestId('login-employee'));

    await waitFor(() => {
      expect(screen.getByTestId('permissions-status')).toHaveTextContent('Not Admin');
    });
  });

  it('maintains authentication state across re-renders', async () => {
    const TestReRenderComponent: React.FC = () => {
      const { isAuthenticated, login } = useAuth();
      const [count, setCount] = React.useState(0);

      return (
        <div>
          <div data-testid="auth-status">
            {isAuthenticated ? 'Authenticated' : 'Not Authenticated'}
          </div>
          <div data-testid="count">{count}</div>
          <button
            data-testid="login-btn"
            onClick={() => login(sampleUsers[0], 'token')}
          >
            Login
          </button>
          <button
            data-testid="increment-btn"
            onClick={() => setCount(c => c + 1)}
          >
            Increment
          </button>
        </div>
      );
    };

    renderWithAuthProvider(<TestReRenderComponent />);

    await waitFor(() => {
      expect(screen.getByTestId('auth-status')).toHaveTextContent('Not Authenticated');
    });

    // Login
    fireEvent.click(screen.getByTestId('login-btn'));

    await waitFor(() => {
      expect(screen.getByTestId('auth-status')).toHaveTextContent('Authenticated');
    });

    // Trigger re-render
    fireEvent.click(screen.getByTestId('increment-btn'));

    await waitFor(() => {
      expect(screen.getByTestId('count')).toHaveTextContent('1');
      expect(screen.getByTestId('auth-status')).toHaveTextContent('Authenticated');
    });
  });
});
