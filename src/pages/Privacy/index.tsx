import React from 'react';
import { Contain<PERSON>, <PERSON>, <PERSON>, Card } from 'react-bootstrap';
import { Shield, Eye, Lock, Users, FileText, Mail } from 'lucide-react';

const Privacy = () => {
    const lastUpdated = "January 15, 2024";

    const sections = [
        {
            icon: <FileText size={24} />,
            title: "Information We Collect",
            content: [
                "Account Information: When you create an account, we collect your name, email address, organization details, and contact information.",
                "Event Data: Information about events you create, including event details, attendees, schedules, and related communications.",
                "Usage Data: Information about how you use our platform, including features accessed, time spent, and interaction patterns.",
                "Device Information: Technical information about your device, browser, IP address, and operating system.",
                "Communication Data: Messages, support requests, and feedback you send to us."
            ]
        },
        {
            icon: <Eye size={24} />,
            title: "How We Use Your Information",
            content: [
                "Provide and improve our event coordination services",
                "Process and manage your events and calendar data",
                "Send important notifications about your events and account",
                "Provide customer support and respond to your inquiries",
                "Analyze usage patterns to enhance platform functionality",
                "Ensure platform security and prevent unauthorized access",
                "Comply with legal obligations and enforce our terms of service"
            ]
        },
        {
            icon: <Users size={24} />,
            title: "Information Sharing",
            content: [
                "Team Members: Event information is shared with designated team members and collaborators within your organization.",
                "Service Providers: We work with trusted third-party providers for hosting, analytics, and customer support.",
                "Legal Requirements: We may disclose information when required by law or to protect our rights and users' safety.",
                "Business Transfers: In case of merger or acquisition, user data may be transferred as part of business assets.",
                "Consent: We may share information with your explicit consent for specific purposes."
            ]
        },
        {
            icon: <Lock size={24} />,
            title: "Data Security",
            content: [
                "Encryption: All data is encrypted in transit and at rest using industry-standard protocols.",
                "Access Controls: Strict access controls ensure only authorized personnel can access user data.",
                "Regular Audits: We conduct regular security audits and vulnerability assessments.",
                "Secure Infrastructure: Our platform is hosted on secure, SOC 2 compliant infrastructure.",
                "Incident Response: We have procedures in place to respond quickly to any security incidents."
            ]
        }
    ];

    return (
        <Container className="py-5">
            {/* Header */}
            <Row className="mb-5">
                <Col lg={8} className="mx-auto text-center">
                    <div className="mb-4">
                        <Shield size={48} style={{ color: '#3174ad' }} />
                    </div>
                    <h1 className="display-4 fw-bold mb-3" style={{ color: '#3174ad' }}>Privacy Policy</h1>
                    <p className="lead mb-3">
                        At WithKalenda, we take your privacy seriously. This policy explains how we collect,
                        use, and protect your personal information.
                    </p>
                    <p className="text-muted">
                        <strong>Last Updated:</strong> {lastUpdated}
                    </p>
                </Col>
            </Row>

            {/* Introduction */}
            <Row className="mb-5">
                <Col lg={10} className="mx-auto">
                    <Card className="border-0 shadow-sm">
                        <Card.Body className="p-4">
                            <h3 className="fw-bold mb-3" style={{ color: '#3174ad' }}>Our Commitment to Privacy</h3>
                            <p className="mb-3">
                                WithKalenda ("we," "our," or "us") is committed to protecting and respecting your privacy.
                                This Privacy Policy explains how we collect, use, disclose, and safeguard your information
                                when you use our event coordination platform and related services.
                            </p>
                            <p className="mb-0">
                                By using WithKalenda, you agree to the collection and use of information in accordance
                                with this policy. If you do not agree with our policies and practices, please do not use our services.
                            </p>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>

            {/* Main Sections */}
            <Row className="mb-5 g-4">
                {sections.map((section, index) => (
                    <Col lg={6} key={index}>
                        <Card className="h-100 border-0 shadow-sm">
                            <Card.Body className="p-4">
                                <div className="d-flex align-items-center mb-3">
                                    <div className="me-3 text-primary">{section.icon}</div>
                                    <h4 className="fw-bold mb-0" style={{ color: '#3174ad' }}>{section.title}</h4>
                                </div>
                                <ul className="list-unstyled">
                                    {section.content.map((item, idx) => (
                                        <li key={idx} className="mb-2">
                                            <span className="text-primary me-2">•</span>
                                            {item}
                                        </li>
                                    ))}
                                </ul>
                            </Card.Body>
                        </Card>
                    </Col>
                ))}
            </Row>

            {/* Additional Sections */}
            <Row className="mb-5">
                <Col lg={10} className="mx-auto">
                    <Row className="g-4">
                        <Col md={6}>
                            <Card className="border-0 shadow-sm h-100">
                                <Card.Body className="p-4">
                                    <h4 className="fw-bold mb-3" style={{ color: '#3174ad' }}>Your Rights</h4>
                                    <ul className="list-unstyled">
                                        <li className="mb-2"><span className="text-primary me-2">•</span>Access your personal data</li>
                                        <li className="mb-2"><span className="text-primary me-2">•</span>Correct inaccurate information</li>
                                        <li className="mb-2"><span className="text-primary me-2">•</span>Delete your account and data</li>
                                        <li className="mb-2"><span className="text-primary me-2">•</span>Export your data</li>
                                        <li className="mb-2"><span className="text-primary me-2">•</span>Opt-out of marketing communications</li>
                                        <li className="mb-0"><span className="text-primary me-2">•</span>File complaints with supervisory authorities</li>
                                    </ul>
                                </Card.Body>
                            </Card>
                        </Col>
                        <Col md={6}>
                            <Card className="border-0 shadow-sm h-100">
                                <Card.Body className="p-4">
                                    <h4 className="fw-bold mb-3" style={{ color: '#3174ad' }}>Data Retention</h4>
                                    <ul className="list-unstyled">
                                        <li className="mb-2"><span className="text-primary me-2">•</span>Account data: Retained while account is active</li>
                                        <li className="mb-2"><span className="text-primary me-2">•</span>Event data: Retained for 7 years after event date</li>
                                        <li className="mb-2"><span className="text-primary me-2">•</span>Usage logs: Retained for 2 years</li>
                                        <li className="mb-2"><span className="text-primary me-2">•</span>Support communications: Retained for 3 years</li>
                                        <li className="mb-0"><span className="text-primary me-2">•</span>Marketing data: Until you opt-out</li>
                                    </ul>
                                </Card.Body>
                            </Card>
                        </Col>
                    </Row>
                </Col>
            </Row>

            {/* Cookies and Tracking */}
            <Row className="mb-5">
                <Col lg={10} className="mx-auto">
                    <Card className="border-0 shadow-sm">
                        <Card.Body className="p-4">
                            <h4 className="fw-bold mb-3" style={{ color: '#3174ad' }}>Cookies and Tracking Technologies</h4>
                            <p className="mb-3">
                                We use cookies and similar tracking technologies to enhance your experience on our platform.
                                These technologies help us:
                            </p>
                            <Row>
                                <Col md={6}>
                                    <ul className="list-unstyled">
                                        <li className="mb-2"><span className="text-primary me-2">•</span>Remember your login preferences</li>
                                        <li className="mb-2"><span className="text-primary me-2">•</span>Analyze platform usage and performance</li>
                                        <li className="mb-2"><span className="text-primary me-2">•</span>Provide personalized content</li>
                                    </ul>
                                </Col>
                                <Col md={6}>
                                    <ul className="list-unstyled">
                                        <li className="mb-2"><span className="text-primary me-2">•</span>Ensure platform security</li>
                                        <li className="mb-2"><span className="text-primary me-2">•</span>Improve our services</li>
                                        <li className="mb-2"><span className="text-primary me-2">•</span>Comply with legal requirements</li>
                                    </ul>
                                </Col>
                            </Row>
                            <p className="mb-0">
                                You can control cookie settings through your browser preferences. However, disabling certain
                                cookies may limit platform functionality.
                            </p>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>

            {/* International Transfers */}
            <Row className="mb-5">
                <Col lg={10} className="mx-auto">
                    <Card className="border-0 shadow-sm">
                        <Card.Body className="p-4">
                            <h4 className="fw-bold mb-3" style={{ color: '#3174ad' }}>International Data Transfers</h4>
                            <p className="mb-3">
                                WithKalenda operates globally, and your information may be transferred to and processed in
                                countries other than your own. We ensure appropriate safeguards are in place for international
                                data transfers, including:
                            </p>
                            <ul className="list-unstyled">
                                <li className="mb-2"><span className="text-primary me-2">•</span>Standard Contractual Clauses approved by the European Commission</li>
                                <li className="mb-2"><span className="text-primary me-2">•</span>Adequacy decisions for countries with equivalent data protection laws</li>
                                <li className="mb-2"><span className="text-primary me-2">•</span>Binding Corporate Rules for intra-group transfers</li>
                                <li className="mb-0"><span className="text-primary me-2">•</span>Your explicit consent where required</li>
                            </ul>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>

            {/* Contact Information */}
            <Row className="mb-5">
                <Col lg={8} className="mx-auto">
                    <Card className="border-0 shadow-sm">
                        <Card.Body className="p-4 text-center">
                            <div className="mb-3">
                                <Mail size={32} style={{ color: '#3174ad' }} />
                            </div>
                            <h4 className="fw-bold mb-3" style={{ color: '#3174ad' }}>Questions About This Policy?</h4>
                            <p className="mb-3">
                                If you have any questions about this Privacy Policy or our data practices,
                                please don't hesitate to contact us.
                            </p>
                            <div className="mb-3">
                                <strong>Email:</strong> <EMAIL><br />
                                <strong>Address:</strong> 123 Innovation Drive, San Francisco, CA 94105<br />
                                <strong>Phone:</strong> +1 (555) 123-KALE
                            </div>
                            <p className="text-muted mb-0">
                                We will respond to your inquiry within 30 days.
                            </p>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>

            {/* Updates Notice */}
            <Row>
                <Col lg={8} className="mx-auto">
                    <div className="p-4 rounded-4 text-center" style={{ backgroundColor: 'rgba(49, 116, 173, 0.05)' }}>
                        <h5 className="fw-bold mb-2" style={{ color: '#3174ad' }}>Policy Updates</h5>
                        <p className="mb-0">
                            We may update this Privacy Policy from time to time. We will notify you of any material
                            changes by email or through our platform. Your continued use of WithKalenda after such
                            modifications constitutes acceptance of the updated policy.
                        </p>
                    </div>
                </Col>
            </Row>
        </Container>
    );
};

export default Privacy;
