import React from 'react';
import { Shield, Eye, Lock, Users, FileText, Mail } from 'lucide-react';

const Privacy = () => {
    const lastUpdated = "January 15, 2024";

    const sections = [
        {
            icon: <FileText size={24} />,
            title: "Information We Collect",
            content: [
                "Account Information: When you create an account, we collect your name, email address, organization details, and contact information.",
                "Event Data: Information about events you create, including event details, attendees, schedules, and related communications.",
                "Usage Data: Information about how you use our platform, including features accessed, time spent, and interaction patterns.",
                "Device Information: Technical information about your device, browser, IP address, and operating system.",
                "Communication Data: Messages, support requests, and feedback you send to us."
            ]
        },
        {
            icon: <Eye size={24} />,
            title: "How We Use Your Information",
            content: [
                "Provide and improve our event coordination services",
                "Process and manage your events and calendar data",
                "Send important notifications about your events and account",
                "Provide customer support and respond to your inquiries",
                "Analyze usage patterns to enhance platform functionality",
                "Ensure platform security and prevent unauthorized access",
                "Comply with legal obligations and enforce our terms of service"
            ]
        },
        {
            icon: <Users size={24} />,
            title: "Information Sharing",
            content: [
                "Team Members: Event information is shared with designated team members and collaborators within your organization.",
                "Service Providers: We work with trusted third-party providers for hosting, analytics, and customer support.",
                "Legal Requirements: We may disclose information when required by law or to protect our rights and users' safety.",
                "Business Transfers: In case of merger or acquisition, user data may be transferred as part of business assets.",
                "Consent: We may share information with your explicit consent for specific purposes."
            ]
        },
        {
            icon: <Lock size={24} />,
            title: "Data Security",
            content: [
                "Encryption: All data is encrypted in transit and at rest using industry-standard protocols.",
                "Access Controls: Strict access controls ensure only authorized personnel can access user data.",
                "Regular Audits: We conduct regular security audits and vulnerability assessments.",
                "Secure Infrastructure: Our platform is hosted on secure, SOC 2 compliant infrastructure.",
                "Incident Response: We have procedures in place to respond quickly to any security incidents."
            ]
        }
    ];

    return (
        <div className="wk-container py-12">
            {/* Header */}
            <div className="mb-12">
                <div className="max-w-4xl mx-auto text-center">
                    <div className="mb-6">
                        <Shield size={48} className="text-brand-primary mx-auto" />
                    </div>
                    <h1 className="text-4xl font-bold mb-4 text-brand-secondary">Privacy Policy</h1>
                    <p className="text-lg mb-4 text-gray-600">
                        At WithKalenda, we take your privacy seriously. This policy explains how we collect,
                        use, and protect your personal information.
                    </p>
                    <p className="text-gray-500">
                        <strong>Last Updated:</strong> {lastUpdated}
                    </p>
                </div>
            </div>

            {/* Introduction */}
            <div className="mb-12">
                <div className="max-w-4xl mx-auto">
                    <div className="wk-card">
                        <div className="p-6">
                            <h3 className="text-xl font-bold mb-4 text-brand-secondary">Our Commitment to Privacy</h3>
                            <p className="mb-4 text-gray-600">
                                WithKalenda ("we," "our," or "us") is committed to protecting and respecting your privacy.
                                This Privacy Policy explains how we collect, use, disclose, and safeguard your information
                                when you use our event coordination platform and related services.
                            </p>
                            <p className="mb-0 text-gray-600">
                                By using WithKalenda, you agree to the collection and use of information in accordance
                                with this policy. If you do not agree with our policies and practices, please do not use our services.
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            {/* Main Sections */}
            <div className="mb-12">
                <div className="max-w-6xl mx-auto grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {sections.map((section, index) => (
                        <div key={index} className="wk-card h-full">
                            <div className="p-6">
                                <div className="flex items-center mb-4">
                                    <div className="mr-3 text-brand-primary">{section.icon}</div>
                                    <h4 className="text-lg font-bold text-brand-secondary">{section.title}</h4>
                                </div>
                                <ul className="space-y-3">
                                    {section.content.map((item, idx) => (
                                        <li key={idx} className="text-gray-600">
                                            <span className="text-brand-primary mr-2">•</span>
                                            {item}
                                        </li>
                                    ))}
                                </ul>
                            </div>
                        </div>
                    ))}
                </div>
            </div>

            {/* Additional Sections */}
            <div className="mb-12">
                <div className="max-w-5xl mx-auto">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="wk-card h-full">
                            <div className="p-6">
                                <h4 className="text-lg font-bold mb-4 text-brand-secondary">Your Rights</h4>
                                <ul className="space-y-3">
                                    <li className="text-gray-600"><span className="text-brand-primary mr-2">•</span>Access your personal data</li>
                                    <li className="text-gray-600"><span className="text-brand-primary mr-2">•</span>Correct inaccurate information</li>
                                    <li className="text-gray-600"><span className="text-brand-primary mr-2">•</span>Delete your account and data</li>
                                    <li className="text-gray-600"><span className="text-brand-primary mr-2">•</span>Export your data</li>
                                    <li className="text-gray-600"><span className="text-brand-primary mr-2">•</span>Opt-out of marketing communications</li>
                                    <li className="text-gray-600"><span className="text-brand-primary mr-2">•</span>File complaints with supervisory authorities</li>
                                </ul>
                            </div>
                        </div>
                        <div className="wk-card h-full">
                            <div className="p-6">
                                <h4 className="text-lg font-bold mb-4 text-brand-secondary">Data Retention</h4>
                                <ul className="space-y-3">
                                    <li className="text-gray-600"><span className="text-brand-primary mr-2">•</span>Account data: Retained while account is active</li>
                                    <li className="text-gray-600"><span className="text-brand-primary mr-2">•</span>Event data: Retained for 7 years after event date</li>
                                    <li className="text-gray-600"><span className="text-brand-primary mr-2">•</span>Usage logs: Retained for 2 years</li>
                                    <li className="text-gray-600"><span className="text-brand-primary mr-2">•</span>Support communications: Retained for 3 years</li>
                                    <li className="text-gray-600"><span className="text-brand-primary mr-2">•</span>Marketing data: Until you opt-out</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {/* Cookies and Tracking */}
            <div className="mb-12">
                <div className="max-w-4xl mx-auto">
                    <div className="wk-card">
                        <div className="p-6">
                            <h4 className="text-lg font-bold mb-4 text-brand-secondary">Cookies and Tracking Technologies</h4>
                            <p className="mb-4 text-gray-600">
                                We use cookies and similar tracking technologies to enhance your experience on our platform.
                                These technologies help us:
                            </p>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <ul className="space-y-3">
                                        <li className="text-gray-600"><span className="text-brand-primary mr-2">•</span>Remember your login preferences</li>
                                        <li className="text-gray-600"><span className="text-brand-primary mr-2">•</span>Analyze platform usage and performance</li>
                                        <li className="text-gray-600"><span className="text-brand-primary mr-2">•</span>Provide personalized content</li>
                                    </ul>
                                </div>
                                <div>
                                    <ul className="space-y-3">
                                        <li className="text-gray-600"><span className="text-brand-primary mr-2">•</span>Ensure platform security</li>
                                        <li className="text-gray-600"><span className="text-brand-primary mr-2">•</span>Improve our services</li>
                                        <li className="text-gray-600"><span className="text-brand-primary mr-2">•</span>Comply with legal requirements</li>
                                    </ul>
                                </div>
                            </div>
                            <p className="mb-0 mt-4 text-gray-600">
                                You can control cookie settings through your browser preferences. However, disabling certain
                                cookies may limit platform functionality.
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            {/* International Transfers */}
            <div className="mb-12">
                <div className="max-w-4xl mx-auto">
                    <div className="wk-card">
                        <div className="p-6">
                            <h4 className="text-lg font-bold mb-4 text-brand-secondary">International Data Transfers</h4>
                            <p className="mb-4 text-gray-600">
                                WithKalenda operates globally, and your information may be transferred to and processed in
                                countries other than your own. We ensure appropriate safeguards are in place for international
                                data transfers, including:
                            </p>
                            <ul className="space-y-3">
                                <li className="text-gray-600"><span className="text-brand-primary mr-2">•</span>Standard Contractual Clauses approved by the European Commission</li>
                                <li className="text-gray-600"><span className="text-brand-primary mr-2">•</span>Adequacy decisions for countries with equivalent data protection laws</li>
                                <li className="text-gray-600"><span className="text-brand-primary mr-2">•</span>Binding Corporate Rules for intra-group transfers</li>
                                <li className="text-gray-600"><span className="text-brand-primary mr-2">•</span>Your explicit consent where required</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            {/* Contact Information */}
            <div className="mb-12">
                <div className="max-w-3xl mx-auto">
                    <div className="wk-card">
                        <div className="p-6 text-center">
                            <div className="mb-4">
                                <Mail size={32} className="text-brand-primary mx-auto" />
                            </div>
                            <h4 className="text-lg font-bold mb-4 text-brand-secondary">Questions About This Policy?</h4>
                            <p className="mb-4 text-gray-600">
                                If you have any questions about this Privacy Policy or our data practices,
                                please don't hesitate to contact us.
                            </p>
                            <div className="mb-4 text-gray-700">
                                <strong>Email:</strong> <EMAIL><br />
                                <strong>Address:</strong> 123 Innovation Drive, San Francisco, CA 94105<br />
                                <strong>Phone:</strong> +1 (555) 123-KALE
                            </div>
                            <p className="text-gray-500 mb-0">
                                We will respond to your inquiry within 30 days.
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            {/* Updates Notice */}
            <div>
                <div className="max-w-3xl mx-auto">
                    <div className="wk-card-accent p-6 text-center">
                        <h5 className="text-lg font-bold mb-3 text-brand-secondary">Policy Updates</h5>
                        <p className="mb-0 text-gray-600">
                            We may update this Privacy Policy from time to time. We will notify you of any material
                            changes by email or through our platform. Your continued use of WithKalenda after such
                            modifications constitutes acceptance of the updated policy.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default Privacy;
