import { useState } from 'react';
import { Container, Nav, Tab } from 'react-bootstrap';
import { Calendar, Users, Settings, BarChart3 } from 'lucide-react';
import Layout from '../../../components/Layout';
import EvaPlatform from '../../../components/Planner';
import DepartmentDashboard from '../../../components/DepartmentDashboard';
import AdminDashboard from '../../../components/AdminDashboard';
import DeadlineManagement from '../../../components/DeadlineManagement';
import { useAuth } from '../../../contexts/AuthContext';

const DashHome = () => {
    const { user, permissions } = useAuth();
    const [activeTab, setActiveTab] = useState('overview');

    return (
        <Layout>
            <Container className="py-4">
                <div className="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h1 className="mb-1">Dashboard</h1>
                        <p className="text-muted mb-0">
                            Welcome back, {user?.firstName} {user?.lastName}
                        </p>
                    </div>
                </div>

                <Tab.Container activeKey={activeTab} onSelect={(k) => setActiveTab(k || 'overview')}>
                    <Nav variant="tabs" className="mb-4">
                        <Nav.Item>
                            <Nav.Link eventKey="overview">
                                <BarChart3 className="me-2" size={16} />
                                Overview
                            </Nav.Link>
                        </Nav.Item>
                        <Nav.Item>
                            <Nav.Link eventKey="events">
                                <Calendar className="me-2" size={16} />
                                Event Management
                            </Nav.Link>
                        </Nav.Item>
                        <Nav.Item>
                            <Nav.Link eventKey="department">
                                <Users className="me-2" size={16} />
                                Department View
                            </Nav.Link>
                        </Nav.Item>
                        {permissions.canApproveEvent && (
                            <Nav.Item>
                                <Nav.Link eventKey="admin">
                                    <Settings className="me-2" size={16} />
                                    Admin Panel
                                </Nav.Link>
                            </Nav.Item>
                        )}
                        {permissions.canSetDeadlines && (
                            <Nav.Item>
                                <Nav.Link eventKey="deadlines">
                                    <Calendar className="me-2" size={16} />
                                    Deadlines
                                </Nav.Link>
                            </Nav.Item>
                        )}
                    </Nav>

                    <Tab.Content>
                        <Tab.Pane eventKey="overview">
                            <DepartmentDashboard />
                        </Tab.Pane>
                        <Tab.Pane eventKey="events">
                            <EvaPlatform />
                        </Tab.Pane>
                        <Tab.Pane eventKey="department">
                            <DepartmentDashboard />
                        </Tab.Pane>
                        {permissions.canApproveEvent && (
                            <Tab.Pane eventKey="admin">
                                <AdminDashboard />
                            </Tab.Pane>
                        )}
                        {permissions.canSetDeadlines && (
                            <Tab.Pane eventKey="deadlines">
                                <DeadlineManagement />
                            </Tab.Pane>
                        )}
                    </Tab.Content>
                </Tab.Container>
            </Container>
        </Layout>
    );
};

export default DashHome;
