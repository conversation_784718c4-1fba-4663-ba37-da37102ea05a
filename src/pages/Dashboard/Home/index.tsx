import { useState } from 'react';
import { Calendar, Users, Settings, BarChart3, ChevronLeft, ChevronRight, Download } from 'lucide-react';
import Layout from '../../../components/Layout';
import DepartmentDashboard from '../../../components/DepartmentDashboard';
import DepartmentEventView from '../../../components/DepartmentEventView';
import AdminDashboard from '../../../components/AdminDashboard';
import DeadlineManagement from '../../../components/DeadlineManagement';
import { useAuth } from '../../../contexts/AuthContext';

const DashHome = () => {
    const { user, permissions } = useAuth();
    const [activeTab, setActiveTab] = useState('events'); // Default to Event Management as shown in screenshot
    const [currentDate, setCurrentDate] = useState(new Date());
    const [calendarView, setCalendarView] = useState<'Month' | 'Week' | 'Day' | 'Agenda'>('Month');

    // Sample events data for the table
    const sampleEvents = [
        {
            id: '1',
            eventName: 'Q1 Marketing Campaign Launch',
            description: 'Launch of the new product...',
            date: 'Mar 15, 2024',
            time: '10:00 AM (120 min)',
            venue: 'Conference Room A, Marketing Floor',
            organizer: 'Marketing Lead',
            status: 'CONFIRMED',
            priority: 'HIGH'
        },
        {
            id: '2',
            eventName: 'IT Security Training',
            description: 'Mandatory cybersecurity training...',
            date: 'Mar 20, 2024',
            time: '2:00 PM (90 min)',
            venue: 'Main Auditorium',
            organizer: 'Michael Chen',
            status: 'CONFIRMED',
            priority: 'MEDIUM'
        },
        {
            id: '3',
            eventName: 'Monthly Sales Review',
            description: 'Monthly review of sales performance...',
            date: 'Mar 25, 2024',
            time: '9:00 AM (60 min)',
            venue: 'Sales Conference Room',
            organizer: 'Sales Lead',
            status: 'CONFIRMED',
            priority: 'MEDIUM'
        },
        {
            id: '4',
            eventName: 'Annual Company Retreat Planning',
            description: 'Planning session for the annual...',
            date: 'Apr 10, 2024',
            time: '3:00 PM (180 min)',
            venue: 'Executive Conference Room',
            organizer: 'Sarah Johnson',
            status: 'PENDING',
            priority: 'HIGH'
        },
        {
            id: '5',
            eventName: 'Budget Review Meeting',
            description: 'Quarterly budget review and...',
            date: 'Apr 15, 2024',
            time: '11:00 AM (90 min)',
            venue: 'Finance Department Meeting Room',
            organizer: 'Finance Lead',
            status: 'PENDING',
            priority: 'CRITICAL'
        }
    ];

    const formatMonthYear = (date: Date) => {
        return date.toLocaleDateString('en-US', { month: 'long', year: 'numeric' });
    };

    const navigateMonth = (direction: 'prev' | 'next') => {
        const newDate = new Date(currentDate);
        if (direction === 'prev') {
            newDate.setMonth(newDate.getMonth() - 1);
        } else {
            newDate.setMonth(newDate.getMonth() + 1);
        }
        setCurrentDate(newDate);
    };

    const getStatusBadgeClass = (status: string) => {
        switch (status) {
            case 'CONFIRMED':
                return 'bg-green-100 text-green-800 px-2 py-1 rounded text-xs font-medium';
            case 'PENDING':
                return 'bg-yellow-100 text-yellow-800 px-2 py-1 rounded text-xs font-medium';
            case 'CRITICAL':
                return 'bg-red-100 text-red-800 px-2 py-1 rounded text-xs font-medium';
            default:
                return 'bg-gray-100 text-gray-800 px-2 py-1 rounded text-xs font-medium';
        }
    };

    const getPriorityBadgeClass = (priority: string) => {
        switch (priority) {
            case 'HIGH':
                return 'bg-orange-100 text-orange-800 px-2 py-1 rounded text-xs font-medium';
            case 'CRITICAL':
                return 'bg-red-100 text-red-800 px-2 py-1 rounded text-xs font-medium';
            case 'MEDIUM':
                return 'bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs font-medium';
            default:
                return 'bg-gray-100 text-gray-800 px-2 py-1 rounded text-xs font-medium';
        }
    };

    return (
        <Layout>
            <div className="wk-container py-6">
                {/* Dashboard Header */}
                <div className="mb-6">
                    <h1 className="text-3xl font-bold text-gray-900 mb-2">Dashboard</h1>
                    <p className="text-gray-600">
                        Welcome back, {user?.firstName || 'Admin'} {user?.lastName || 'User'}
                    </p>
                </div>

                {/* Tab Navigation */}
                <div className="border-b border-gray-200 mb-6">
                    <nav className="flex space-x-8">
                        <button
                            onClick={() => setActiveTab('overview')}
                            className={`flex items-center py-2 px-1 border-b-2 font-medium text-sm ${activeTab === 'overview'
                                ? 'border-blue-500 text-blue-600'
                                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                                }`}
                        >
                            <BarChart3 className="mr-2" size={16} />
                            Overview
                        </button>
                        <button
                            onClick={() => setActiveTab('events')}
                            className={`flex items-center py-2 px-1 border-b-2 font-medium text-sm ${activeTab === 'events'
                                ? 'border-blue-500 text-blue-600'
                                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                                }`}
                        >
                            <Calendar className="mr-2" size={16} />
                            Event Management
                        </button>
                        <button
                            onClick={() => setActiveTab('department')}
                            className={`flex items-center py-2 px-1 border-b-2 font-medium text-sm ${activeTab === 'department'
                                ? 'border-blue-500 text-blue-600'
                                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                                }`}
                        >
                            <Users className="mr-2" size={16} />
                            Department View
                        </button>
                        {permissions.canApproveEvent && (
                            <button
                                onClick={() => setActiveTab('admin')}
                                className={`flex items-center py-2 px-1 border-b-2 font-medium text-sm ${activeTab === 'admin'
                                    ? 'border-blue-500 text-blue-600'
                                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                                    }`}
                            >
                                <Settings className="mr-2" size={16} />
                                Admin Panel
                            </button>
                        )}
                        {permissions.canSetDeadlines && (
                            <button
                                onClick={() => setActiveTab('deadlines')}
                                className={`flex items-center py-2 px-1 border-b-2 font-medium text-sm ${activeTab === 'deadlines'
                                    ? 'border-blue-500 text-blue-600'
                                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                                    }`}
                            >
                                <Calendar className="mr-2" size={16} />
                                Deadlines
                            </button>
                        )}
                    </nav>
                </div>

                {/* Tab Content */}
                {activeTab === 'overview' && (
                    <DepartmentDashboard />
                )}

                {activeTab === 'events' && (
                    <div>
                        {/* Event Management Header */}
                        <div className="flex justify-between items-center mb-6">
                            <h2 className="text-2xl font-bold text-gray-900">Event Management</h2>
                            <div className="flex space-x-3">
                                <button className="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors">
                                    Admin Dashboard
                                </button>
                                <button className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors flex items-center">
                                    <Download className="mr-2" size={16} />
                                    Export Calendar
                                </button>
                            </div>
                        </div>

                        {/* Calendar Navigation */}
                        <div className="bg-white rounded-lg border border-gray-200 mb-6">
                            <div className="flex justify-between items-center p-4 border-b border-gray-200">
                                <div className="flex items-center space-x-4">
                                    <button
                                        onClick={() => navigateMonth('prev')}
                                        className="p-2 hover:bg-gray-100 rounded-md"
                                    >
                                        <ChevronLeft size={20} />
                                    </button>
                                    <button
                                        onClick={() => setCurrentDate(new Date())}
                                        className="px-3 py-1 bg-gray-100 text-gray-700 rounded text-sm hover:bg-gray-200"
                                    >
                                        Today
                                    </button>
                                    <button
                                        onClick={() => navigateMonth('prev')}
                                        className="px-3 py-1 text-gray-600 hover:bg-gray-100 rounded text-sm"
                                    >
                                        Previous
                                    </button>
                                    <button
                                        onClick={() => navigateMonth('next')}
                                        className="px-3 py-1 text-gray-600 hover:bg-gray-100 rounded text-sm"
                                    >
                                        Next
                                    </button>
                                    <button
                                        onClick={() => navigateMonth('next')}
                                        className="p-2 hover:bg-gray-100 rounded-md"
                                    >
                                        <ChevronRight size={20} />
                                    </button>
                                </div>

                                <h3 className="text-lg font-semibold text-gray-900">
                                    {formatMonthYear(currentDate)}
                                </h3>

                                <div className="flex space-x-2">
                                    {(['Month', 'Week', 'Day', 'Agenda'] as const).map((view) => (
                                        <button
                                            key={view}
                                            onClick={() => setCalendarView(view)}
                                            className={`px-3 py-1 text-sm rounded ${calendarView === view
                                                ? 'bg-gray-200 text-gray-900'
                                                : 'text-gray-600 hover:bg-gray-100'
                                                }`}
                                        >
                                            {view}
                                        </button>
                                    ))}
                                </div>
                            </div>

                            {/* Calendar Grid */}
                            <div className="p-4">
                                <div className="grid grid-cols-7 gap-1 mb-2">
                                    {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map((day) => (
                                        <div key={day} className="p-2 text-center text-sm font-medium text-gray-500">
                                            {day}
                                        </div>
                                    ))}
                                </div>
                                <div className="grid grid-cols-7 gap-1">
                                    {/* Calendar days would be generated here */}
                                    {Array.from({ length: 35 }, (_, i) => {
                                        const dayNumber = i - 5; // Adjust for month start
                                        const isCurrentMonth = dayNumber > 0 && dayNumber <= 31;
                                        const isToday = dayNumber === 10; // Example: 10th is today
                                        const hasEvent = [10, 17].includes(dayNumber); // Example events on 10th and 17th

                                        return (
                                            <div
                                                key={i}
                                                className={`h-20 p-1 border border-gray-100 ${isCurrentMonth ? 'bg-white' : 'bg-gray-50'
                                                    } ${isToday ? 'bg-blue-50' : ''}`}
                                            >
                                                <div className={`text-sm ${isCurrentMonth ? 'text-gray-900' : 'text-gray-400'
                                                    } ${isToday ? 'font-bold text-blue-600' : ''}`}>
                                                    {isCurrentMonth ? (dayNumber < 10 ? `0${dayNumber}` : dayNumber) : ''}
                                                </div>
                                                {hasEvent && isCurrentMonth && (
                                                    <div className="mt-1">
                                                        <div className="w-full h-1 bg-blue-500 rounded"></div>
                                                    </div>
                                                )}
                                            </div>
                                        );
                                    })}
                                </div>
                            </div>
                        </div>

                        {/* Company Events Table */}
                        <div className="bg-white rounded-lg border border-gray-200">
                            <div className="flex justify-between items-center p-4 border-b border-gray-200">
                                <h3 className="text-lg font-semibold text-gray-900">Company Events</h3>
                                <div className="flex items-center space-x-4">
                                    <input
                                        type="text"
                                        placeholder="Search events..."
                                        className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    />
                                    <select className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        <option>All Statuses</option>
                                        <option>Confirmed</option>
                                        <option>Pending</option>
                                        <option>Critical</option>
                                    </select>
                                </div>
                            </div>

                            <div className="overflow-x-auto">
                                <table className="w-full">
                                    <thead className="bg-gray-50">
                                        <tr>
                                            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Event Name
                                            </th>
                                            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Date & Time
                                            </th>
                                            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Venue
                                            </th>
                                            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Organizer
                                            </th>
                                            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Status
                                            </th>
                                            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Priority
                                            </th>
                                            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Actions
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody className="bg-white divide-y divide-gray-200">
                                        {sampleEvents.map((event) => (
                                            <tr key={event.id} className="hover:bg-gray-50">
                                                <td className="px-4 py-4">
                                                    <div>
                                                        <div className="text-sm font-medium text-gray-900">
                                                            {event.eventName}
                                                        </div>
                                                        <div className="text-sm text-gray-500">
                                                            {event.description}
                                                        </div>
                                                    </div>
                                                </td>
                                                <td className="px-4 py-4">
                                                    <div className="text-sm text-gray-900 flex items-center">
                                                        <Calendar className="mr-1" size={14} />
                                                        {event.date}
                                                    </div>
                                                    <div className="text-sm text-gray-500 flex items-center">
                                                        <span className="ml-4">{event.time}</span>
                                                    </div>
                                                </td>
                                                <td className="px-4 py-4">
                                                    <div className="text-sm text-gray-900 flex items-center">
                                                        <span className="mr-1">📍</span>
                                                        {event.venue}
                                                    </div>
                                                </td>
                                                <td className="px-4 py-4">
                                                    <div className="text-sm text-gray-900">{event.organizer}</div>
                                                </td>
                                                <td className="px-4 py-4">
                                                    <span className={getStatusBadgeClass(event.status)}>
                                                        {event.status}
                                                    </span>
                                                </td>
                                                <td className="px-4 py-4">
                                                    <span className={getPriorityBadgeClass(event.priority)}>
                                                        {event.priority}
                                                    </span>
                                                </td>
                                                <td className="px-4 py-4">
                                                    <div className="flex space-x-2">
                                                        <button className="px-3 py-1 bg-blue-100 text-blue-800 rounded text-xs hover:bg-blue-200">
                                                            ✏️ Edit
                                                        </button>
                                                        <button className="px-3 py-1 bg-red-100 text-red-800 rounded text-xs hover:bg-red-200">
                                                            🗑️ Delete
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                        ))}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                )}

                {activeTab === 'department' && (
                    <DepartmentEventView />
                )}

                {activeTab === 'admin' && permissions.canApproveEvent && (
                    <AdminDashboard />
                )}

                {activeTab === 'deadlines' && permissions.canSetDeadlines && (
                    <DeadlineManagement />
                )}
            </div>
        </Layout>
    );
};

export default DashHome;
