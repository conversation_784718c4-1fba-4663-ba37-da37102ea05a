import { useState } from 'react';
import { Calendar, Users, Settings, BarChart3, ChevronLeft, ChevronRight, Download, Clock } from 'lucide-react';
import Layout from '../../../components/Layout';
import DepartmentEventsTable from '../../../components/DepartmentEventsTable';
import { useAuth } from '../../../contexts/AuthContext';

const DashHome = () => {
    const { user, permissions } = useAuth();
    const [activeTab, setActiveTab] = useState('overview'); // Default to Overview as shown in screenshot
    const [currentDate, setCurrentDate] = useState(new Date());
    const [calendarView, setCalendarView] = useState<'Month' | 'Week' | 'Day' | 'Agenda'>('Month');

    // Pagination state for Event Management
    const [eventsCurrentPage, setEventsCurrentPage] = useState(1);
    const [eventsSearchTerm, setEventsSearchTerm] = useState('');
    const [eventsStatusFilter, setEventsStatusFilter] = useState('All Statuses');
    const eventsPerPage = 10;

    // Sample events data for the table
    const sampleEvents = [
        {
            id: '1',
            eventName: 'Q1 Marketing Campaign Launch',
            description: 'Launch of the new product...',
            date: 'Mar 15, 2024',
            time: '10:00 AM (120 min)',
            venue: 'Conference Room A, Marketing Floor',
            organizer: 'Marketing Lead',
            status: 'CONFIRMED',
            priority: 'HIGH'
        },
        {
            id: '2',
            eventName: 'IT Security Training',
            description: 'Mandatory cybersecurity training...',
            date: 'Mar 20, 2024',
            time: '2:00 PM (90 min)',
            venue: 'Main Auditorium',
            organizer: 'Michael Chen',
            status: 'CONFIRMED',
            priority: 'MEDIUM'
        },
        {
            id: '3',
            eventName: 'Monthly Sales Review',
            description: 'Monthly review of sales performance...',
            date: 'Mar 25, 2024',
            time: '9:00 AM (60 min)',
            venue: 'Sales Conference Room',
            organizer: 'Sales Lead',
            status: 'CONFIRMED',
            priority: 'MEDIUM'
        },
        {
            id: '4',
            eventName: 'Annual Company Retreat Planning',
            description: 'Planning session for the annual...',
            date: 'Apr 10, 2024',
            time: '3:00 PM (180 min)',
            venue: 'Executive Conference Room',
            organizer: 'Sarah Johnson',
            status: 'PENDING',
            priority: 'HIGH'
        },
        {
            id: '5',
            eventName: 'Budget Review Meeting',
            description: 'Quarterly budget review and...',
            date: 'Apr 15, 2024',
            time: '11:00 AM (90 min)',
            venue: 'Finance Department Meeting Room',
            organizer: 'Finance Lead',
            status: 'PENDING',
            priority: 'CRITICAL'
        }
    ];

    const formatMonthYear = (date: Date) => {
        return date.toLocaleDateString('en-US', { month: 'long', year: 'numeric' });
    };

    const navigateMonth = (direction: 'prev' | 'next') => {
        const newDate = new Date(currentDate);
        if (direction === 'prev') {
            newDate.setMonth(newDate.getMonth() - 1);
        } else {
            newDate.setMonth(newDate.getMonth() + 1);
        }
        setCurrentDate(newDate);
    };

    const getStatusBadgeClass = (status: string) => {
        switch (status) {
            case 'CONFIRMED':
                return 'bg-green-100 text-green-800 px-2 py-1 rounded text-xs font-medium';
            case 'PENDING':
                return 'bg-yellow-100 text-yellow-800 px-2 py-1 rounded text-xs font-medium';
            case 'CRITICAL':
                return 'bg-red-100 text-red-800 px-2 py-1 rounded text-xs font-medium';
            default:
                return 'bg-gray-100 text-gray-800 px-2 py-1 rounded text-xs font-medium';
        }
    };

    const getPriorityBadgeClass = (priority: string) => {
        switch (priority) {
            case 'HIGH':
                return 'bg-orange-100 text-orange-800 px-2 py-1 rounded text-xs font-medium';
            case 'CRITICAL':
                return 'bg-red-100 text-red-800 px-2 py-1 rounded text-xs font-medium';
            case 'MEDIUM':
                return 'bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs font-medium';
            default:
                return 'bg-gray-100 text-gray-800 px-2 py-1 rounded text-xs font-medium';
        }
    };

    // Filter and paginate events for Event Management tab
    const filteredEvents = sampleEvents.filter(event => {
        const matchesSearch = event.eventName.toLowerCase().includes(eventsSearchTerm.toLowerCase()) ||
            event.organizer.toLowerCase().includes(eventsSearchTerm.toLowerCase()) ||
            event.venue.toLowerCase().includes(eventsSearchTerm.toLowerCase());
        const matchesStatus = eventsStatusFilter === 'All Statuses' || event.status === eventsStatusFilter;
        return matchesSearch && matchesStatus;
    });

    const eventsTotalPages = Math.ceil(filteredEvents.length / eventsPerPage);
    const eventsStartIndex = (eventsCurrentPage - 1) * eventsPerPage;
    const paginatedEvents = filteredEvents.slice(eventsStartIndex, eventsStartIndex + eventsPerPage);

    return (
        <Layout>
            <div className="wk-container py-6">
                {/* Dashboard Header */}
                <div className="mb-6">
                    <h1 className="text-3xl font-bold text-gray-900 mb-2">Dashboard</h1>
                    <p className="text-gray-600">
                        Welcome back, {user?.firstName || 'Admin'} {user?.lastName || 'User'}
                    </p>
                </div>

                {/* Tab Navigation */}
                <div className="border-b border-gray-200 mb-6">
                    <nav className="flex space-x-8">
                        <button
                            onClick={() => setActiveTab('overview')}
                            className={`flex items-center py-2 px-1 border-b-2 font-medium text-sm ${activeTab === 'overview'
                                ? 'border-blue-500 text-blue-600'
                                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                                }`}
                        >
                            <BarChart3 className="mr-2" size={16} />
                            Overview
                        </button>
                        <button
                            onClick={() => setActiveTab('events')}
                            className={`flex items-center py-2 px-1 border-b-2 font-medium text-sm ${activeTab === 'events'
                                ? 'border-blue-500 text-blue-600'
                                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                                }`}
                        >
                            <Calendar className="mr-2" size={16} />
                            Event Management
                        </button>
                        <button
                            onClick={() => setActiveTab('department')}
                            className={`flex items-center py-2 px-1 border-b-2 font-medium text-sm ${activeTab === 'department'
                                ? 'border-blue-500 text-blue-600'
                                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                                }`}
                        >
                            <Users className="mr-2" size={16} />
                            Department View
                        </button>
                        {permissions.canApproveEvent && (
                            <button
                                onClick={() => setActiveTab('admin')}
                                className={`flex items-center py-2 px-1 border-b-2 font-medium text-sm ${activeTab === 'admin'
                                    ? 'border-blue-500 text-blue-600'
                                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                                    }`}
                            >
                                <Settings className="mr-2" size={16} />
                                Admin Panel
                            </button>
                        )}
                        {permissions.canSetDeadlines && (
                            <button
                                onClick={() => setActiveTab('deadlines')}
                                className={`flex items-center py-2 px-1 border-b-2 font-medium text-sm ${activeTab === 'deadlines'
                                    ? 'border-blue-500 text-blue-600'
                                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                                    }`}
                            >
                                <Calendar className="mr-2" size={16} />
                                Deadlines
                            </button>
                        )}
                    </nav>
                </div>

                {/* Tab Content */}
                {activeTab === 'overview' && (
                    <div>
                        {/* Overview Dashboard */}
                        <div className="flex justify-between items-center mb-6">
                            <div>
                                <h2 className="text-2xl font-bold text-gray-900 flex items-center">
                                    <Users className="mr-2" />
                                    Department Dashboard
                                </h2>
                                <p className="text-gray-600">Administration</p>
                            </div>
                            <button className="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors border border-gray-300">
                                Export Calendar
                            </button>
                        </div>

                        {/* Statistics Cards */}
                        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
                            <div className="bg-white rounded-lg border border-gray-200 p-6 text-center">
                                <Calendar className="mx-auto mb-3 text-blue-500" size={32} />
                                <h3 className="text-2xl font-bold text-blue-500">0</h3>
                                <p className="text-gray-600">Total Events</p>
                            </div>
                            <div className="bg-white rounded-lg border border-gray-200 p-6 text-center">
                                <Clock className="mx-auto mb-3 text-yellow-500" size={32} />
                                <h3 className="text-2xl font-bold text-yellow-500">0</h3>
                                <p className="text-gray-600">Pending Approval</p>
                            </div>
                            <div className="bg-white rounded-lg border border-gray-200 p-6 text-center">
                                <div className="mx-auto mb-3 text-green-500 flex justify-center">
                                    <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                        <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                                        <polyline points="22,4 12,14.01 9,11.01"></polyline>
                                    </svg>
                                </div>
                                <h3 className="text-2xl font-bold text-green-500">0</h3>
                                <p className="text-gray-600">Confirmed</p>
                            </div>
                            <div className="bg-white rounded-lg border border-gray-200 p-6 text-center">
                                <div className="mx-auto mb-3 text-cyan-500 flex justify-center">
                                    <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                        <polyline points="22,12 18,12 15,21 9,3 6,12 2,12"></polyline>
                                    </svg>
                                </div>
                                <h3 className="text-2xl font-bold text-cyan-500">0</h3>
                                <p className="text-gray-600">Upcoming (30 days)</p>
                            </div>
                        </div>

                        {/* Event Status Overview */}
                        <div className="bg-white rounded-lg border border-gray-200 mb-6">
                            <div className="p-4 border-b border-gray-200">
                                <h3 className="text-lg font-semibold text-gray-900">Event Status Overview</h3>
                            </div>
                            <div className="p-6">
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div className="space-y-4">
                                        <div>
                                            <div className="flex justify-between mb-2">
                                                <span className="text-sm text-gray-600">Draft Events</span>
                                                <span className="text-sm text-gray-600">0/0</span>
                                            </div>
                                            <div className="w-full bg-gray-200 rounded-full h-2">
                                                <div className="bg-gray-400 h-2 rounded-full" style={{ width: '0%' }}></div>
                                            </div>
                                        </div>
                                        <div>
                                            <div className="flex justify-between mb-2">
                                                <span className="text-sm text-gray-600">Pending Approval</span>
                                                <span className="text-sm text-gray-600">0/0</span>
                                            </div>
                                            <div className="w-full bg-gray-200 rounded-full h-2">
                                                <div className="bg-yellow-400 h-2 rounded-full" style={{ width: '0%' }}></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div className="space-y-4">
                                        <div>
                                            <div className="flex justify-between mb-2">
                                                <span className="text-sm text-gray-600">Confirmed Events</span>
                                                <span className="text-sm text-gray-600">0/0</span>
                                            </div>
                                            <div className="w-full bg-gray-200 rounded-full h-2">
                                                <div className="bg-green-400 h-2 rounded-full" style={{ width: '0%' }}></div>
                                            </div>
                                        </div>
                                        <div>
                                            <div className="flex justify-between mb-2">
                                                <span className="text-sm text-gray-600">Rejected Events</span>
                                                <span className="text-sm text-gray-600">0/0</span>
                                            </div>
                                            <div className="w-full bg-gray-200 rounded-full h-2">
                                                <div className="bg-red-400 h-2 rounded-full" style={{ width: '0%' }}></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* Upcoming Events */}
                        <div className="bg-white rounded-lg border border-gray-200 mb-6">
                            <div className="p-4 border-b border-gray-200">
                                <h3 className="text-lg font-semibold text-gray-900">Upcoming Events (Next 30 Days)</h3>
                            </div>
                            <div className="p-6">
                                <p className="text-gray-500">No upcoming events in the next 30 days.</p>
                            </div>
                        </div>

                        {/* Recent Department Events */}
                        <div className="bg-white rounded-lg border border-gray-200">
                            <div className="p-4 border-b border-gray-200">
                                <h3 className="text-lg font-semibold text-gray-900">Recent Department Events</h3>
                            </div>
                            <div className="p-6">
                                <p className="text-gray-500">No events found for your department.</p>
                            </div>
                        </div>
                    </div>
                )}

                {activeTab === 'events' && (
                    <div>
                        {/* Event Management Header */}
                        <div className="flex justify-between items-center mb-6">
                            <h2 className="text-2xl font-bold text-gray-900">Event Management</h2>
                            <div className="flex space-x-3">
                                <button className="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors">
                                    Admin Dashboard
                                </button>
                                <button className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors flex items-center">
                                    <Download className="mr-2" size={16} />
                                    Export Calendar
                                </button>
                            </div>
                        </div>

                        {/* Calendar Navigation */}
                        <div className="bg-white rounded-lg border border-gray-200 mb-6">
                            <div className="flex justify-between items-center p-4 border-b border-gray-200">
                                <div className="flex items-center space-x-4">
                                    <button
                                        onClick={() => navigateMonth('prev')}
                                        className="p-2 hover:bg-gray-100 rounded-md"
                                    >
                                        <ChevronLeft size={20} />
                                    </button>
                                    <button
                                        onClick={() => setCurrentDate(new Date())}
                                        className="px-3 py-1 bg-gray-100 text-gray-700 rounded text-sm hover:bg-gray-200"
                                    >
                                        Today
                                    </button>
                                    <button
                                        onClick={() => navigateMonth('prev')}
                                        className="px-3 py-1 text-gray-600 hover:bg-gray-100 rounded text-sm"
                                    >
                                        Previous
                                    </button>
                                    <button
                                        onClick={() => navigateMonth('next')}
                                        className="px-3 py-1 text-gray-600 hover:bg-gray-100 rounded text-sm"
                                    >
                                        Next
                                    </button>
                                    <button
                                        onClick={() => navigateMonth('next')}
                                        className="p-2 hover:bg-gray-100 rounded-md"
                                    >
                                        <ChevronRight size={20} />
                                    </button>
                                </div>

                                <h3 className="text-lg font-semibold text-gray-900">
                                    {formatMonthYear(currentDate)}
                                </h3>

                                <div className="flex space-x-2">
                                    {(['Month', 'Week', 'Day', 'Agenda'] as const).map((view) => (
                                        <button
                                            key={view}
                                            onClick={() => setCalendarView(view)}
                                            className={`px-3 py-1 text-sm rounded ${calendarView === view
                                                ? 'bg-gray-200 text-gray-900'
                                                : 'text-gray-600 hover:bg-gray-100'
                                                }`}
                                        >
                                            {view}
                                        </button>
                                    ))}
                                </div>
                            </div>

                            {/* Calendar Grid */}
                            <div className="p-4">
                                <div className="grid grid-cols-7 gap-1 mb-2">
                                    {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map((day) => (
                                        <div key={day} className="p-2 text-center text-sm font-medium text-gray-500">
                                            {day}
                                        </div>
                                    ))}
                                </div>
                                <div className="grid grid-cols-7 gap-1">
                                    {/* Calendar days would be generated here */}
                                    {Array.from({ length: 35 }, (_, i) => {
                                        const dayNumber = i - 5; // Adjust for month start
                                        const isCurrentMonth = dayNumber > 0 && dayNumber <= 31;
                                        const isToday = dayNumber === 10; // Example: 10th is today
                                        const hasEvent = [10, 17].includes(dayNumber); // Example events on 10th and 17th

                                        return (
                                            <div
                                                key={i}
                                                className={`h-20 p-1 border border-gray-100 ${isCurrentMonth ? 'bg-white' : 'bg-gray-50'
                                                    } ${isToday ? 'bg-blue-50' : ''}`}
                                            >
                                                <div className={`text-sm ${isCurrentMonth ? 'text-gray-900' : 'text-gray-400'
                                                    } ${isToday ? 'font-bold text-blue-600' : ''}`}>
                                                    {isCurrentMonth ? (dayNumber < 10 ? `0${dayNumber}` : dayNumber) : ''}
                                                </div>
                                                {hasEvent && isCurrentMonth && (
                                                    <div className="mt-1">
                                                        <div className="w-full h-1 bg-blue-500 rounded"></div>
                                                    </div>
                                                )}
                                            </div>
                                        );
                                    })}
                                </div>
                            </div>
                        </div>

                        {/* Company Events Table */}
                        <div className="bg-white rounded-lg border border-gray-200">
                            <div className="flex justify-between items-center p-4 border-b border-gray-200">
                                <h3 className="text-lg font-semibold text-gray-900">Company Events</h3>
                                <div className="flex items-center space-x-4">
                                    <input
                                        type="text"
                                        placeholder="Search events..."
                                        value={eventsSearchTerm}
                                        onChange={(e) => setEventsSearchTerm(e.target.value)}
                                        className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    />
                                    <select
                                        value={eventsStatusFilter}
                                        onChange={(e) => setEventsStatusFilter(e.target.value)}
                                        className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    >
                                        <option>All Statuses</option>
                                        <option>CONFIRMED</option>
                                        <option>PENDING</option>
                                        <option>CRITICAL</option>
                                    </select>
                                </div>
                            </div>

                            <div className="overflow-x-auto">
                                <table className="w-full">
                                    <thead className="bg-gray-50">
                                        <tr>
                                            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Event Name
                                            </th>
                                            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Date & Time
                                            </th>
                                            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Venue
                                            </th>
                                            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Organizer
                                            </th>
                                            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Status
                                            </th>
                                            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Priority
                                            </th>
                                            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Actions
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody className="bg-white divide-y divide-gray-200">
                                        {paginatedEvents.map((event) => (
                                            <tr key={event.id} className="hover:bg-gray-50">
                                                <td className="px-4 py-4">
                                                    <div>
                                                        <div className="text-sm font-medium text-gray-900">
                                                            {event.eventName}
                                                        </div>
                                                        <div className="text-sm text-gray-500">
                                                            {event.description}
                                                        </div>
                                                    </div>
                                                </td>
                                                <td className="px-4 py-4">
                                                    <div className="text-sm text-gray-900 flex items-center">
                                                        <Calendar className="mr-1" size={14} />
                                                        {event.date}
                                                    </div>
                                                    <div className="text-sm text-gray-500 flex items-center">
                                                        <span className="ml-4">{event.time}</span>
                                                    </div>
                                                </td>
                                                <td className="px-4 py-4">
                                                    <div className="text-sm text-gray-900 flex items-center">
                                                        <span className="mr-1">📍</span>
                                                        {event.venue}
                                                    </div>
                                                </td>
                                                <td className="px-4 py-4">
                                                    <div className="text-sm text-gray-900">{event.organizer}</div>
                                                </td>
                                                <td className="px-4 py-4">
                                                    <span className={getStatusBadgeClass(event.status)}>
                                                        {event.status}
                                                    </span>
                                                </td>
                                                <td className="px-4 py-4">
                                                    <span className={getPriorityBadgeClass(event.priority)}>
                                                        {event.priority}
                                                    </span>
                                                </td>
                                                <td className="px-4 py-4">
                                                    <div className="flex space-x-2">
                                                        <button className="px-3 py-1 bg-blue-100 text-blue-800 rounded text-xs hover:bg-blue-200">
                                                            ✏️ Edit
                                                        </button>
                                                        <button className="px-3 py-1 bg-red-100 text-red-800 rounded text-xs hover:bg-red-200">
                                                            🗑️ Delete
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                        ))}
                                    </tbody>
                                </table>
                            </div>

                            {/* Pagination */}
                            {eventsTotalPages > 1 && (
                                <div className="px-6 py-3 border-t border-gray-200 flex items-center justify-between">
                                    <div className="text-sm text-gray-700">
                                        Showing {eventsStartIndex + 1} to {Math.min(eventsStartIndex + eventsPerPage, filteredEvents.length)} of {filteredEvents.length} results
                                    </div>
                                    <div className="flex space-x-1">
                                        <button
                                            onClick={() => setEventsCurrentPage(Math.max(1, eventsCurrentPage - 1))}
                                            disabled={eventsCurrentPage === 1}
                                            className="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                                        >
                                            Previous
                                        </button>
                                        {Array.from({ length: eventsTotalPages }, (_, i) => i + 1).map((page) => (
                                            <button
                                                key={page}
                                                onClick={() => setEventsCurrentPage(page)}
                                                className={`px-3 py-1 text-sm border rounded ${eventsCurrentPage === page
                                                    ? 'bg-blue-600 text-white border-blue-600'
                                                    : 'border-gray-300 hover:bg-gray-50'
                                                    }`}
                                            >
                                                {page}
                                            </button>
                                        ))}
                                        <button
                                            onClick={() => setEventsCurrentPage(Math.min(eventsTotalPages, eventsCurrentPage + 1))}
                                            disabled={eventsCurrentPage === eventsTotalPages}
                                            className="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                                        >
                                            Next
                                        </button>
                                    </div>
                                </div>
                            )}
                        </div>
                    </div>
                )}

                {activeTab === 'department' && (
                    <div>
                        {/* Department Events Header */}
                        <div className="mb-6 flex justify-between items-center">
                            <div>
                                <h2 className="text-2xl font-bold text-gray-900 flex items-center">
                                    <Calendar className="mr-2 text-blue-600" />
                                    Department Events
                                </h2>
                                <p className="text-gray-600 mt-1">Events for Administration</p>
                            </div>
                            <button className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 flex items-center">
                                <Download className="mr-2" size={16} />
                                Export Calendar
                            </button>
                        </div>

                        {/* Statistics Cards */}
                        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
                            <div className="bg-white rounded-lg border border-gray-200 p-6 text-center">
                                <div className="flex justify-center mb-3">
                                    <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                                        <svg className="w-6 h-6 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                        </svg>
                                    </div>
                                </div>
                                <div className="text-2xl font-bold text-gray-900">4</div>
                                <div className="text-sm text-gray-600">Confirmed Events</div>
                            </div>
                            <div className="bg-white rounded-lg border border-gray-200 p-6 text-center">
                                <div className="flex justify-center mb-3">
                                    <div className="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center">
                                        <Clock className="w-6 h-6 text-yellow-600" />
                                    </div>
                                </div>
                                <div className="text-2xl font-bold text-gray-900">2</div>
                                <div className="text-sm text-gray-600">Pending Approval</div>
                            </div>
                            <div className="bg-white rounded-lg border border-gray-200 p-6 text-center">
                                <div className="flex justify-center mb-3">
                                    <div className="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center">
                                        <svg className="w-6 h-6 text-gray-600" fill="currentColor" viewBox="0 0 20 20">
                                            <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
                                        </svg>
                                    </div>
                                </div>
                                <div className="text-2xl font-bold text-gray-900">1</div>
                                <div className="text-sm text-gray-600">Draft Events</div>
                            </div>
                            <div className="bg-white rounded-lg border border-gray-200 p-6 text-center">
                                <div className="flex justify-center mb-3">
                                    <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                                        <Calendar className="w-6 h-6 text-blue-600" />
                                    </div>
                                </div>
                                <div className="text-2xl font-bold text-gray-900">8</div>
                                <div className="text-sm text-gray-600">Total Events</div>
                            </div>
                        </div>

                        {/* Calendar Navigation */}
                        <div className="bg-white rounded-lg border border-gray-200 mb-6">
                            <div className="p-4 border-b border-gray-200 flex justify-between items-center">
                                <div className="flex items-center space-x-4">
                                    <button className="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded">Today</button>
                                    <button className="px-3 py-1 text-sm text-gray-700 hover:bg-gray-100 rounded">Previous</button>
                                    <button className="px-3 py-1 text-sm text-gray-700 hover:bg-gray-100 rounded">Next</button>
                                </div>
                                <h3 className="text-lg font-semibold text-gray-900">July 2025</h3>
                                <div className="flex items-center space-x-2">
                                    <button className="px-3 py-1 text-sm bg-blue-600 text-white rounded">Month</button>
                                    <button className="px-3 py-1 text-sm text-gray-700 hover:bg-gray-100 rounded">Week</button>
                                    <button className="px-3 py-1 text-sm text-gray-700 hover:bg-gray-100 rounded">Day</button>
                                    <button className="px-3 py-1 text-sm text-gray-700 hover:bg-gray-100 rounded">Agenda</button>
                                </div>
                            </div>

                            {/* Calendar Grid */}
                            <div className="p-4">
                                <div className="grid grid-cols-7 gap-1 mb-2">
                                    <div className="p-2 text-center text-sm font-medium text-gray-700">Sun</div>
                                    <div className="p-2 text-center text-sm font-medium text-gray-700">Mon</div>
                                    <div className="p-2 text-center text-sm font-medium text-gray-700">Tue</div>
                                    <div className="p-2 text-center text-sm font-medium text-gray-700">Wed</div>
                                    <div className="p-2 text-center text-sm font-medium text-gray-700">Thu</div>
                                    <div className="p-2 text-center text-sm font-medium text-gray-700">Fri</div>
                                    <div className="p-2 text-center text-sm font-medium text-gray-700">Sat</div>
                                </div>
                                <div className="grid grid-cols-7 gap-1">
                                    {/* Previous month days */}
                                    <div className="h-20 p-1 text-sm text-gray-400 bg-gray-50">29</div>
                                    <div className="h-20 p-1 text-sm text-gray-400 bg-gray-50">30</div>

                                    {/* Current month days */}
                                    <div className="h-20 p-1 text-sm border hover:bg-gray-50">01</div>
                                    <div className="h-20 p-1 text-sm border hover:bg-gray-50">02</div>
                                    <div className="h-20 p-1 text-sm border hover:bg-gray-50">03</div>
                                    <div className="h-20 p-1 text-sm border hover:bg-gray-50">04</div>
                                    <div className="h-20 p-1 text-sm border hover:bg-gray-50">05</div>

                                    <div className="h-20 p-1 text-sm border hover:bg-gray-50">06</div>
                                    <div className="h-20 p-1 text-sm border hover:bg-gray-50">07</div>
                                    <div className="h-20 p-1 text-sm border hover:bg-gray-50">08</div>
                                    <div className="h-20 p-1 text-sm border hover:bg-gray-50">09</div>
                                    <div className="h-20 p-1 text-sm border hover:bg-gray-50 bg-blue-50">10</div>
                                    <div className="h-20 p-1 text-sm border hover:bg-gray-50">11</div>
                                    <div className="h-20 p-1 text-sm border hover:bg-gray-50">12</div>

                                    <div className="h-20 p-1 text-sm border hover:bg-gray-50">13</div>
                                    <div className="h-20 p-1 text-sm border hover:bg-gray-50">14</div>
                                    <div className="h-20 p-1 text-sm border hover:bg-gray-50">15</div>
                                    <div className="h-20 p-1 text-sm border hover:bg-gray-50">16</div>
                                    <div className="h-20 p-1 text-sm border hover:bg-gray-50">17</div>
                                    <div className="h-20 p-1 text-sm border hover:bg-gray-50">18</div>
                                    <div className="h-20 p-1 text-sm border hover:bg-gray-50">19</div>

                                    <div className="h-20 p-1 text-sm border hover:bg-gray-50">20</div>
                                    <div className="h-20 p-1 text-sm border hover:bg-gray-50">21</div>
                                    <div className="h-20 p-1 text-sm border hover:bg-gray-50">22</div>
                                    <div className="h-20 p-1 text-sm border hover:bg-gray-50">23</div>
                                    <div className="h-20 p-1 text-sm border hover:bg-gray-50">24</div>
                                    <div className="h-20 p-1 text-sm border hover:bg-gray-50">25</div>
                                    <div className="h-20 p-1 text-sm border hover:bg-gray-50">26</div>

                                    <div className="h-20 p-1 text-sm border hover:bg-gray-50">27</div>
                                    <div className="h-20 p-1 text-sm border hover:bg-gray-50">28</div>
                                    <div className="h-20 p-1 text-sm border hover:bg-gray-50">29</div>
                                    <div className="h-20 p-1 text-sm border hover:bg-gray-50">30</div>
                                    <div className="h-20 p-1 text-sm border hover:bg-gray-50">31</div>

                                    {/* Next month days */}
                                    <div className="h-20 p-1 text-sm text-gray-400 bg-gray-50">01</div>
                                    <div className="h-20 p-1 text-sm text-gray-400 bg-gray-50">02</div>
                                </div>
                            </div>
                        </div>

                        {/* Department Events Table */}
                        <DepartmentEventsTable />
                    </div>
                )}

                {activeTab === 'admin' && permissions.canApproveEvent && (
                    <div>
                        {/* Admin Dashboard Header */}
                        <div className="mb-6">
                            <h2 className="text-2xl font-bold text-gray-900 flex items-center">
                                <Users className="mr-2" />
                                Admin Dashboard
                            </h2>
                        </div>

                        {/* Statistics Cards */}
                        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
                            <div className="bg-white rounded-lg border border-gray-200 p-6 text-center">
                                <Clock className="mx-auto mb-3 text-yellow-500" size={32} />
                                <h3 className="text-2xl font-bold text-yellow-500">3</h3>
                                <p className="text-gray-600">Pending Review</p>
                            </div>
                            <div className="bg-white rounded-lg border border-gray-200 p-6 text-center">
                                <div className="mx-auto mb-3 text-red-500 flex justify-center">
                                    <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                        <path d="m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z"></path>
                                        <path d="M12 9v4"></path>
                                        <path d="m12 17 .01 0"></path>
                                    </svg>
                                </div>
                                <h3 className="text-2xl font-bold text-red-500">1</h3>
                                <p className="text-gray-600">Conflicts</p>
                            </div>
                            <div className="bg-white rounded-lg border border-gray-200 p-6 text-center">
                                <div className="mx-auto mb-3 text-green-500 flex justify-center">
                                    <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                        <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                                        <polyline points="22,4 12,14.01 9,11.01"></polyline>
                                    </svg>
                                </div>
                                <h3 className="text-2xl font-bold text-green-500">4</h3>
                                <p className="text-gray-600">Confirmed</p>
                            </div>
                            <div className="bg-white rounded-lg border border-gray-200 p-6 text-center">
                                <div className="mx-auto mb-3 text-gray-500 flex justify-center">
                                    <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                        <circle cx="12" cy="12" r="10"></circle>
                                        <path d="m15 9-6 6"></path>
                                        <path d="m9 9 6 6"></path>
                                    </svg>
                                </div>
                                <h3 className="text-2xl font-bold text-gray-500">0</h3>
                                <p className="text-gray-600">Rejected</p>
                            </div>
                        </div>

                        {/* Events Pending Review */}
                        <div className="bg-white rounded-lg border border-gray-200 mb-6">
                            <div className="p-4 border-b border-gray-200">
                                <h3 className="text-lg font-semibold text-gray-900 flex items-center">
                                    <Clock className="mr-2" size={20} />
                                    Events Pending Review (3)
                                </h3>
                            </div>
                            <div className="overflow-x-auto">
                                <table className="w-full">
                                    <thead className="bg-gray-50">
                                        <tr>
                                            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Event</th>
                                            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Department</th>
                                            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date & Time</th>
                                            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Organizer</th>
                                            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody className="bg-white divide-y divide-gray-200">
                                        <tr className="hover:bg-gray-50">
                                            <td className="px-4 py-4">
                                                <div>
                                                    <div className="text-sm font-medium text-gray-900">Annual Company Retreat Planning</div>
                                                    <div className="text-sm text-gray-500">Planning session for the annual company retreat. Discussing venue options, activities, budget, and logistics.</div>
                                                </div>
                                            </td>
                                            <td className="px-4 py-4 text-sm text-gray-900">Human Resources</td>
                                            <td className="px-4 py-4">
                                                <div className="text-sm text-gray-900">Apr 10, 2024</div>
                                                <div className="text-sm text-gray-500">1:00 PM</div>
                                            </td>
                                            <td className="px-4 py-4 text-sm text-gray-900">Sarah Johnson</td>
                                            <td className="px-4 py-4">
                                                <span className="bg-yellow-100 text-yellow-800 px-2 py-1 rounded text-xs font-medium">PENDING</span>
                                            </td>
                                            <td className="px-4 py-4">
                                                <div className="flex space-x-2">
                                                    <button className="px-3 py-1 bg-green-100 text-green-800 rounded text-xs hover:bg-green-200">Approve</button>
                                                    <button className="px-3 py-1 bg-red-100 text-red-800 rounded text-xs hover:bg-red-200">Reject</button>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr className="hover:bg-gray-50">
                                            <td className="px-4 py-4">
                                                <div>
                                                    <div className="text-sm font-medium text-gray-900">Budget Review Meeting</div>
                                                    <div className="text-sm text-gray-500">Quarterly budget review and financial planning session. Analysis of Q1 performance and Q2 projections.</div>
                                                </div>
                                            </td>
                                            <td className="px-4 py-4 text-sm text-gray-900">Finance</td>
                                            <td className="px-4 py-4">
                                                <div className="text-sm text-gray-900">Apr 15, 2024</div>
                                                <div className="text-sm text-gray-500">11:00 AM</div>
                                            </td>
                                            <td className="px-4 py-4 text-sm text-gray-900">Finance Lead</td>
                                            <td className="px-4 py-4">
                                                <span className="bg-yellow-100 text-yellow-800 px-2 py-1 rounded text-xs font-medium">PENDING</span>
                                            </td>
                                            <td className="px-4 py-4">
                                                <div className="flex space-x-2">
                                                    <button className="px-3 py-1 bg-green-100 text-green-800 rounded text-xs hover:bg-green-200">Approve</button>
                                                    <button className="px-3 py-1 bg-red-100 text-red-800 rounded text-xs hover:bg-red-200">Reject</button>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr className="hover:bg-gray-50">
                                            <td className="px-4 py-4">
                                                <div>
                                                    <div className="text-sm font-medium text-gray-900">Product Demo Presentation</div>
                                                    <div className="text-sm text-gray-500">Demonstration of new product features to potential clients and stakeholders.</div>
                                                </div>
                                            </td>
                                            <td className="px-4 py-4 text-sm text-gray-900">Marketing</td>
                                            <td className="px-4 py-4">
                                                <div className="text-sm text-gray-900">Apr 10, 2024</div>
                                                <div className="text-sm text-gray-500">2:00 PM</div>
                                            </td>
                                            <td className="px-4 py-4 text-sm text-gray-900">Marketing Lead</td>
                                            <td className="px-4 py-4">
                                                <span className="bg-yellow-100 text-yellow-800 px-2 py-1 rounded text-xs font-medium">PENDING</span>
                                            </td>
                                            <td className="px-4 py-4">
                                                <div className="flex space-x-2">
                                                    <button className="px-3 py-1 bg-green-100 text-green-800 rounded text-xs hover:bg-green-200">Approve</button>
                                                    <button className="px-3 py-1 bg-red-100 text-red-800 rounded text-xs hover:bg-red-200">Reject</button>
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        {/* Events with Conflicts */}
                        <div className="bg-white rounded-lg border border-gray-200">
                            <div className="p-4 border-b border-gray-200">
                                <h3 className="text-lg font-semibold text-gray-900 flex items-center">
                                    <div className="mr-2 text-red-500">
                                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                            <path d="m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z"></path>
                                            <path d="M12 9v4"></path>
                                            <path d="m12 17 .01 0"></path>
                                        </svg>
                                    </div>
                                    Events with Conflicts (1)
                                </h3>
                            </div>
                            <div className="overflow-x-auto">
                                <table className="w-full">
                                    <thead className="bg-gray-50">
                                        <tr>
                                            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Event</th>
                                            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Department</th>
                                            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date & Time</th>
                                            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Conflicts</th>
                                            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                        </tr>
                                    </thead>
                                    <tbody className="bg-white divide-y divide-gray-200">
                                        <tr className="hover:bg-gray-50">
                                            <td className="px-4 py-4 text-sm font-medium text-gray-900">Product Demo Presentation</td>
                                            <td className="px-4 py-4 text-sm text-gray-900">Marketing</td>
                                            <td className="px-4 py-4">
                                                <div className="text-sm text-gray-900">Apr 10, 2024</div>
                                                <div className="text-sm text-gray-500">2:00 PM</div>
                                            </td>
                                            <td className="px-4 py-4">
                                                <span className="bg-yellow-100 text-yellow-800 px-2 py-1 rounded text-xs font-medium mr-2">MEDIUM</span>
                                                <span className="text-sm text-gray-600">Overlaps with Annual Company Retreat Planning meeting</span>
                                            </td>
                                            <td className="px-4 py-4">
                                                <span className="bg-yellow-100 text-yellow-800 px-2 py-1 rounded text-xs font-medium">PENDING</span>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                )}

                {activeTab === 'deadlines' && permissions.canSetDeadlines && (
                    <div>
                        {/* Deadlines Header */}
                        <div className="mb-6">
                            <h2 className="text-2xl font-bold text-gray-900 flex items-center">
                                <Calendar className="mr-2" />
                                Submission Deadlines
                            </h2>
                        </div>

                        {/* Deadlines Table */}
                        <div className="bg-white rounded-lg border border-gray-200">
                            <div className="overflow-x-auto">
                                <table className="w-full">
                                    <thead className="bg-gray-50">
                                        <tr>
                                            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Year</th>
                                            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                                            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Deadline</th>
                                            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
                                            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody className="bg-white divide-y divide-gray-200">
                                        <tr className="hover:bg-gray-50">
                                            <td className="px-4 py-4 text-sm text-gray-900">2024</td>
                                            <td className="px-4 py-4 text-sm text-gray-900">Q2 2024 Event Submissions</td>
                                            <td className="px-4 py-4 text-sm text-gray-900">Mar 31, 2024 at 11:59 PM</td>
                                            <td className="px-4 py-4">
                                                <span className="bg-red-100 text-red-800 px-2 py-1 rounded text-xs font-medium">Expired</span>
                                            </td>
                                            <td className="px-4 py-4 text-sm text-gray-900">Jan 01, 2024</td>
                                            <td className="px-4 py-4">
                                                <div className="flex space-x-2">
                                                    <button className="px-3 py-1 bg-blue-100 text-blue-800 rounded text-xs hover:bg-blue-200">✏️ Edit</button>
                                                    <button className="px-3 py-1 bg-yellow-100 text-yellow-800 rounded text-xs hover:bg-yellow-200">Deactivate</button>
                                                    <button className="px-3 py-1 bg-red-100 text-red-800 rounded text-xs hover:bg-red-200">🗑️ Delete</button>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr className="hover:bg-gray-50">
                                            <td className="px-4 py-4 text-sm text-gray-900">2024</td>
                                            <td className="px-4 py-4 text-sm text-gray-900">Q3 2024 Event Submissions</td>
                                            <td className="px-4 py-4 text-sm text-gray-900">Jun 30, 2024 at 11:59 PM</td>
                                            <td className="px-4 py-4">
                                                <span className="bg-green-100 text-green-800 px-2 py-1 rounded text-xs font-medium">Active</span>
                                            </td>
                                            <td className="px-4 py-4 text-sm text-gray-900">Jan 01, 2024</td>
                                            <td className="px-4 py-4">
                                                <div className="flex space-x-2">
                                                    <button className="px-3 py-1 bg-blue-100 text-blue-800 rounded text-xs hover:bg-blue-200">✏️ Edit</button>
                                                    <button className="px-3 py-1 bg-green-100 text-green-800 rounded text-xs hover:bg-green-200">Activate</button>
                                                    <button className="px-3 py-1 bg-red-100 text-red-800 rounded text-xs hover:bg-red-200">🗑️ Delete</button>
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                )}
            </div>
        </Layout>
    );
};

export default DashHome;
