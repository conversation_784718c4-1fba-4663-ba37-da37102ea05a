import React from 'react';
import { Check, X, Star, Shield, Zap, HeadphonesIcon } from 'lucide-react';
import { <PERSON><PERSON> } from '../../components/ui/button';
import { Card } from '../../components/ui/card';
import { Badge } from '../../components/ui/badge';
import Layout from '../../components/Layout';

const Pricing: React.FC = () => {
    const plans = [
        {
            name: 'Community',
            price: 0,
            period: 'Forever Free',
            description: 'Perfect for small teams and community groups',
            badge: null,
            features: [
                'Up to 5 team members',
                'Up to 25 events per month',
                'Basic calendar integration',
                'Email notifications',
                'Community support',
                'Mobile app access'
            ],
            limitations: [
                'No advanced analytics',
                'No custom branding',
                'No priority support'
            ],
            cta: 'Get Started Free',
            variant: 'outline-primary',
            popular: false
        },
        {
            name: 'Professional',
            price: 19,
            period: 'per month',
            description: 'Ideal for growing organizations and businesses',
            badge: 'Most Popular',
            features: [
                'Up to 25 team members',
                'Unlimited events',
                'Advanced calendar features',
                'Multi-department support',
                'Conflict detection & resolution',
                'Custom event templates',
                'Export to all calendar formats',
                'Email & SMS notifications',
                'Basic analytics & reporting',
                'Priority email support'
            ],
            limitations: [],
            cta: 'Start 14-Day Free Trial',
            variant: 'primary',
            popular: true
        },
        {
            name: 'Enterprise',
            price: 49,
            period: 'per month',
            description: 'For large organizations with complex needs',
            badge: 'Best Value',
            features: [
                'Unlimited team members',
                'Unlimited events',
                'Everything in Professional',
                'Advanced user permissions',
                'Custom branding & white-label',
                'API access & integrations',
                'Advanced analytics & insights',
                'Custom reporting',
                'Single Sign-On (SSO)',
                'Dedicated account manager',
                '24/7 phone & chat support',
                'Custom training sessions'
            ],
            limitations: [],
            cta: 'Contact Sales',
            variant: 'outline-primary',
            popular: false
        }
    ];

    return (
        <Layout>
            <div className="py-12">
                <div className="wk-container">
                    {/* Header */}
                    <div className="text-center mb-12">
                        <h1 className="text-4xl font-bold mb-4 text-brand-blue">
                            Simple, Transparent Pricing
                        </h1>
                        <p className="text-xl text-gray-600 mb-8">
                            Choose the perfect plan for your organization's event coordination needs
                        </p>
                        <div className="flex justify-center items-center gap-4 mb-8 flex-wrap">
                            <Badge variant="success" className="px-4 py-2 text-sm">
                                <Star size={16} className="mr-1" />
                                14-Day Free Trial
                            </Badge>
                            <Badge variant="info" className="px-4 py-2 text-sm">
                                <Shield size={16} className="mr-1" />
                                No Setup Fees
                            </Badge>
                            <Badge variant="warning" className="px-4 py-2 text-sm">
                                <Zap size={16} className="mr-1" />
                                Cancel Anytime
                            </Badge>
                        </div>
                    </div>

                    {/* Pricing Cards */}
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12 justify-items-center">
                        {plans.map((plan, index) => (
                            <Card
                                key={index}
                                className={`w-full max-w-sm h-full ${plan.popular
                                    ? 'border-brand-blue shadow-lg ring-2 ring-brand-blue/20 scale-105'
                                    : 'border shadow-sm'
                                    }`}
                            >
                                <div className="p-6 text-center">
                                    {plan.badge && (
                                        <div className="mb-4">
                                            <Badge
                                                variant={plan.popular ? 'default' : 'secondary'}
                                                className="px-3 py-1"
                                            >
                                                {plan.badge}
                                            </Badge>
                                        </div>
                                    )}

                                    <h3 className="text-2xl font-bold mb-2">{plan.name}</h3>
                                    <p className="text-gray-600 mb-4">{plan.description}</p>

                                    <div className="mb-6">
                                        {plan.price === 0 ? (
                                            <div>
                                                <span className="text-5xl font-bold text-brand-blue">
                                                    Free
                                                </span>
                                                <div className="text-gray-500">{plan.period}</div>
                                            </div>
                                        ) : (
                                            <div>
                                                <span className="text-5xl font-bold text-brand-blue">
                                                    ${plan.price}
                                                </span>
                                                <span className="text-gray-500">/{plan.period}</span>
                                            </div>
                                        )}
                                    </div>

                                    <ul className="text-left mb-6 space-y-3">
                                        {plan.features.map((feature, idx) => (
                                            <li key={idx} className="flex items-start">
                                                <Check size={16} className="text-green-500 mr-3 mt-0.5 flex-shrink-0" />
                                                <span className="text-sm">{feature}</span>
                                            </li>
                                        ))}
                                        {plan.limitations.map((limitation, idx) => (
                                            <li key={idx} className="flex items-start text-gray-400">
                                                <X size={16} className="text-gray-400 mr-3 mt-0.5 flex-shrink-0" />
                                                <span className="text-sm">{limitation}</span>
                                            </li>
                                        ))}
                                    </ul>

                                    <Button
                                        variant={plan.popular ? 'brand-orange' : 'outline'}
                                        className="w-full"
                                        size="lg"
                                    >
                                        {plan.cta}
                                    </Button>
                                </div>
                            </Card>
                        ))}
                    </div>

                    {/* CTA Section */}
                    <div className="text-center">
                        <div className="wk-card-accent p-8 rounded-lg">
                            <h3 className="text-2xl font-bold mb-4 text-brand-blue">
                                Ready to Start Coordinating?
                            </h3>
                            <p className="text-gray-600 mb-6">
                                Join thousands of organizations already using WithKalenda to streamline their event planning.
                            </p>
                            <div className="flex justify-center gap-4 flex-wrap">
                                <Button
                                    size="lg"
                                    variant="brand-orange"
                                >
                                    Start Free Trial
                                </Button>
                                <Button variant="outline" size="lg">
                                    <HeadphonesIcon size={20} className="mr-2" />
                                    Talk to Sales
                                </Button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </Layout>
    );
};

export default Pricing;
