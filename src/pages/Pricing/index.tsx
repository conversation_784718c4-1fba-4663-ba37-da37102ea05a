import React from 'react';
import { Container, <PERSON>, <PERSON>, Card, Button, Badge } from 'react-bootstrap';
import { Check, X, Star, Shield, Zap, HeadphonesIcon } from 'lucide-react';
import Layout from '../../components/Layout';

const Pricing: React.FC = () => {
    const plans = [
        {
            name: 'Community',
            price: 0,
            period: 'Forever Free',
            description: 'Perfect for small teams and community groups',
            badge: null,
            features: [
                'Up to 5 team members',
                'Up to 25 events per month',
                'Basic calendar integration',
                'Email notifications',
                'Community support',
                'Mobile app access'
            ],
            limitations: [
                'No advanced analytics',
                'No custom branding',
                'No priority support'
            ],
            cta: 'Get Started Free',
            variant: 'outline-primary',
            popular: false
        },
        {
            name: 'Professional',
            price: 19,
            period: 'per month',
            description: 'Ideal for growing organizations and businesses',
            badge: 'Most Popular',
            features: [
                'Up to 25 team members',
                'Unlimited events',
                'Advanced calendar features',
                'Multi-department support',
                'Conflict detection & resolution',
                'Custom event templates',
                'Export to all calendar formats',
                'Email & SMS notifications',
                'Basic analytics & reporting',
                'Priority email support'
            ],
            limitations: [],
            cta: 'Start 14-Day Free Trial',
            variant: 'primary',
            popular: true
        },
        {
            name: 'Enterprise',
            price: 49,
            period: 'per month',
            description: 'For large organizations with complex needs',
            badge: 'Best Value',
            features: [
                'Unlimited team members',
                'Unlimited events',
                'Everything in Professional',
                'Advanced user permissions',
                'Custom branding & white-label',
                'API access & integrations',
                'Advanced analytics & insights',
                'Custom reporting',
                'Single Sign-On (SSO)',
                'Dedicated account manager',
                '24/7 phone & chat support',
                'Custom training sessions'
            ],
            limitations: [],
            cta: 'Contact Sales',
            variant: 'outline-primary',
            popular: false
        }
    ];

    return (
        <Layout>
            <div className="py-5">
                <Container>
                    {/* Header */}
                    <Row className="text-center mb-5">
                        <Col>
                            <h1 className="display-4 fw-bold mb-3" style={{ color: '#3174ad' }}>
                                Simple, Transparent Pricing
                            </h1>
                            <p className="lead text-muted mb-4">
                                Choose the perfect plan for your organization's event coordination needs
                            </p>
                            <div className="d-flex justify-content-center align-items-center gap-3 mb-4">
                                <Badge bg="success" className="px-3 py-2">
                                    <Star size={16} className="me-1" />
                                    14-Day Free Trial
                                </Badge>
                                <Badge bg="info" className="px-3 py-2">
                                    <Shield size={16} className="me-1" />
                                    No Setup Fees
                                </Badge>
                                <Badge bg="warning" className="px-3 py-2">
                                    <Zap size={16} className="me-1" />
                                    Cancel Anytime
                                </Badge>
                            </div>
                        </Col>
                    </Row>

                    {/* Pricing Cards */}
                    <Row className="justify-content-center mb-5">
                        {plans.map((plan, index) => (
                            <Col lg={4} md={6} className="mb-4" key={index}>
                                <Card
                                    className={`h-100 ${plan.popular ? 'border-primary shadow-lg' : 'border-0 shadow'}`}
                                    style={{ transform: plan.popular ? 'scale(1.05)' : 'none' }}
                                >
                                    <Card.Body className="p-4 text-center">
                                        {plan.badge && (
                                            <div className="mb-3">
                                                <Badge
                                                    bg={plan.popular ? 'primary' : 'secondary'}
                                                    className="px-3 py-2"
                                                >
                                                    {plan.badge}
                                                </Badge>
                                            </div>
                                        )}

                                        <h3 className="fw-bold mb-2">{plan.name}</h3>
                                        <p className="text-muted mb-3">{plan.description}</p>

                                        <div className="mb-4">
                                            {plan.price === 0 ? (
                                                <div>
                                                    <span className="display-4 fw-bold" style={{ color: '#3174ad' }}>
                                                        Free
                                                    </span>
                                                    <div className="text-muted">{plan.period}</div>
                                                </div>
                                            ) : (
                                                <div>
                                                    <span className="display-4 fw-bold" style={{ color: '#3174ad' }}>
                                                        ${plan.price}
                                                    </span>
                                                    <span className="text-muted">/{plan.period}</span>
                                                </div>
                                            )}
                                        </div>

                                        <ul className="list-unstyled text-start mb-4">
                                            {plan.features.map((feature, idx) => (
                                                <li key={idx} className="mb-2 d-flex align-items-start">
                                                    <Check size={16} className="text-success me-2 mt-1 flex-shrink-0" />
                                                    <span>{feature}</span>
                                                </li>
                                            ))}
                                            {plan.limitations.map((limitation, idx) => (
                                                <li key={idx} className="mb-2 d-flex align-items-start text-muted">
                                                    <X size={16} className="text-muted me-2 mt-1 flex-shrink-0" />
                                                    <span>{limitation}</span>
                                                </li>
                                            ))}
                                        </ul>

                                        <Button
                                            variant={plan.variant}
                                            className="w-100"
                                            size="lg"
                                            style={plan.popular ? {
                                                backgroundColor: '#ff6b35',
                                                borderColor: '#ff6b35',
                                                color: 'white'
                                            } : {}}
                                        >
                                            {plan.cta}
                                        </Button>
                                    </Card.Body>
                                </Card>
                            </Col>
                        ))}
                    </Row>

                    {/* CTA Section */}
                    <Row className="text-center">
                        <Col>
                            <div className="p-5 rounded-4" style={{ backgroundColor: 'rgba(49, 116, 173, 0.05)' }}>
                                <h3 className="fw-bold mb-3" style={{ color: '#3174ad' }}>
                                    Ready to Start Coordinating?
                                </h3>
                                <p className="text-muted mb-4">
                                    Join thousands of organizations already using WithKalenda to streamline their event planning.
                                </p>
                                <div className="d-flex justify-content-center gap-3">
                                    <Button
                                        size="lg"
                                        style={{ backgroundColor: '#ff6b35', borderColor: '#ff6b35', color: 'white' }}
                                    >
                                        Start Free Trial
                                    </Button>
                                    <Button variant="outline-primary" size="lg">
                                        <HeadphonesIcon size={20} className="me-2" />
                                        Talk to Sales
                                    </Button>
                                </div>
                            </div>
                        </Col>
                    </Row>
                </Container>
            </div>
            );
};

            export default Pricing;
