import React, { useState } from 'react';
import { Container, <PERSON>, Col, Card, But<PERSON>, ToggleButtonGroup, ToggleButton, Badge } from 'react-bootstrap';
import { CheckCircleFill } from 'react-bootstrap-icons';
import Layout from '../../components/Layout';

interface PricingPlanProps {
    title: string;
    description: string;
    price: string;
    period: string;
    buttonText: string;
    buttonVariant: string;
    noCardRequired?: boolean;
    features: string[];
    popular?: boolean;
}

const PricingPlan: React.FC<PricingPlanProps> = ({
    title,
    description,
    price,
    period,
    buttonText,
    buttonVariant,
    noCardRequired,
    features,
    popular,
}) => (
    <Card className="h-100 bg-light border-0">
        {popular && (
            <Badge bg="success" className="position-absolute top-0 end-0 m-2">
                Popular
            </Badge>
        )}
        <Card.Body className="d-flex flex-column">
            <Card.Title className="fs-4 mb-3">{title}</Card.Title>
            <Card.Text className="text-muted mb-4">{description}</Card.Text>
            <div className="mb-4">
                <span className="fs-1 fw-bold">{price}</span>
                {period && <span className="text-muted"> {period}</span>}
            </div>
            <Button variant={buttonVariant} className="mb-3">
                {buttonText}
            </Button>
            {noCardRequired && (
                <small className="text-muted text-center mb-4">No credit card required</small>
            )}
            <h6 className="fw-bold mb-3">Key features:</h6>
            <ul className="list-unstyled">
                {features.map((feature, index) => (
                    <li key={index} className="mb-2">
                        <CheckCircleFill className="text-success me-2" />
                        {feature}
                    </li>
                ))}
            </ul>
        </Card.Body>
    </Card>
);

const PricingPage: React.FC = () => {
    const [billingPeriod, setBillingPeriod] = useState<string>('monthly');

    return (

        <Container className="py-5">
            <h1 className="text-center mb-2">Simple, scalable pricing.</h1>
            <p className="text-center text-muted mb-5">No extra charges. No hidden fees.</p>

            <div className="text-center mb-5">
                <ToggleButtonGroup type="radio" name="billing" defaultValue="monthly">
                    <ToggleButton
                        id="monthly"
                        value="monthly"
                        variant="outline-success"
                        onClick={() => setBillingPeriod('monthly')}
                    >
                        Monthly
                    </ToggleButton>
                    <ToggleButton
                        id="yearly"
                        value="yearly"
                        variant="outline-success"
                        onClick={() => setBillingPeriod('yearly')}
                    >
                        Yearly
                    </ToggleButton>
                </ToggleButtonGroup>
            </div>

            <Row className="g-4">
                <Col md={4}>
                    <PricingPlan
                        title="Essentials"
                        description="For creating impressive tools that generate results."
                        price="$19 USD"
                        period="Seat per month, 2 seats max"
                        buttonText="Start a free trial"
                        buttonVariant="success"
                        noCardRequired={true}
                        features={[
                            'Real-time tracking and notifications',
                            'Real-time analytics',
                            'Drag and drop templates',
                            'Project Management',
                            '24/7 email and chat support',
                        ]}
                    />
                </Col>
                <Col md={4}>
                    <PricingPlan
                        title="Business"
                        description="For seamless integrations and sending tools in bulk."
                        price="$49 USD"
                        period="Seat cost per month"
                        buttonText="Start a free trial"
                        buttonVariant="success"
                        noCardRequired={true}
                        features={[
                            'CRM and Zapier integrations',
                            'Content reporting',
                            'Unlimited team workspaces',
                            'Approval workflows',
                            'Salesforce integration*',
                        ]}
                        popular={true}
                    />
                </Col>
                <Col md={4}>
                    <PricingPlan
                        title="Enterprise"
                        description="For large companies with complex Tool workflows."
                        price="Let's talk"
                        period="Per seat or per tool pricing"
                        buttonText="Contact sales"
                        buttonVariant="dark"
                        features={[
                            'Unlimited files uploads',
                            'Real-time tracking and notifications',
                            'User performance',
                            'SSO support and custom user roles',
                            'Bulk send & Forms*',
                        ]}
                    />
                </Col>
            </Row>

            <p className="text-center text-muted mt-5">Prices exclude any applicable taxes.</p>

            <h2 className="text-center mt-5 mb-4">Frequently asked questions:</h2>
            <Card className="bg-light border-0 mb-3">
                <Card.Body>
                    <div className="d-flex justify-content-between align-items-center">
                        <h5 className="mb-0">Do I need to know about how to code?</h5>
                        <Button variant="link">-</Button>
                    </div>
                    <p className="mt-3 mb-0">
                        Yes, you need to have a fair amount of knowledge in dealing with HTML/CSS as well as JavaScript in order to be able to use Lexend.
                    </p>
                </Card.Body>
            </Card>
            <Card className="bg-light border-0">
                <Card.Body>
                    <div className="d-flex justify-content-between align-items-center">
                        <h5 className="mb-0">Can I use it for commercial projects?</h5>
                        <Button variant="link">+</Button>
                    </div>
                </Card.Body>
            </Card>
        </Container>
    );
};

export default PricingPage;