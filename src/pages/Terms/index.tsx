import React from 'react';
import { Container, <PERSON>, Col, Card, Alert } from 'react-bootstrap';
import { FileText, Scale, Shield, AlertTriangle, CheckCircle, XCircle } from 'lucide-react';

const Terms = () => {
    const lastUpdated = "January 15, 2024";

    const sections = [
        {
            icon: <CheckCircle size={24} />,
            title: "Acceptance of Terms",
            content: [
                "By accessing and using WithKalenda, you accept and agree to be bound by these Terms of Service.",
                "If you do not agree to these terms, please do not use our services.",
                "These terms apply to all users, including visitors, registered users, and premium subscribers.",
                "Your continued use of the service constitutes acceptance of any updates to these terms."
            ]
        },
        {
            icon: <FileText size={24} />,
            title: "Service Description",
            content: [
                "WithKalenda provides event coordination and calendar management services for organizations.",
                "Our platform enables teams to plan, coordinate, and manage events collaboratively.",
                "We offer various subscription tiers with different features and usage limits.",
                "Service availability may vary by geographic location and subscription level."
            ]
        },
        {
            icon: <Shield size={24} />,
            title: "User Responsibilities",
            content: [
                "You are responsible for maintaining the confidentiality of your account credentials.",
                "You must provide accurate and complete information when creating your account.",
                "You agree to use the service only for lawful purposes and in accordance with these terms.",
                "You are responsible for all activities that occur under your account.",
                "You must notify us immediately of any unauthorized use of your account."
            ]
        },
        {
            icon: <XCircle size={24} />,
            title: "Prohibited Uses",
            content: [
                "Using the service for any illegal or unauthorized purpose.",
                "Attempting to gain unauthorized access to our systems or other users' accounts.",
                "Uploading or transmitting viruses, malware, or other harmful code.",
                "Harassing, abusing, or harming other users or our staff.",
                "Violating any applicable laws or regulations.",
                "Interfering with or disrupting the service or servers."
            ]
        }
    ];

    return (
        <Container className="py-5">
            {/* Header */}
            <Row className="mb-5">
                <Col lg={8} className="mx-auto text-center">
                    <div className="mb-4">
                        <Scale size={48} style={{ color: '#3174ad' }} />
                    </div>
                    <h1 className="display-4 fw-bold mb-3" style={{ color: '#3174ad' }}>Terms of Service</h1>
                    <p className="lead mb-3">
                        These terms govern your use of WithKalenda and outline the rights and responsibilities
                        of both users and our company.
                    </p>
                    <p className="text-muted">
                        <strong>Last Updated:</strong> {lastUpdated}
                    </p>
                </Col>
            </Row>

            {/* Important Notice */}
            <Row className="mb-5">
                <Col lg={10} className="mx-auto">
                    <Alert variant="warning" className="d-flex align-items-start">
                        <AlertTriangle size={24} className="me-3 mt-1 flex-shrink-0" />
                        <div>
                            <strong>Important:</strong> Please read these Terms of Service carefully before using WithKalenda.
                            By creating an account or using our services, you agree to be legally bound by these terms.
                            If you do not agree with any part of these terms, you may not use our services.
                        </div>
                    </Alert>
                </Col>
            </Row>

            {/* Main Sections */}
            <Row className="mb-5 g-4">
                {sections.map((section, index) => (
                    <Col lg={6} key={index}>
                        <Card className="h-100 border-0 shadow-sm">
                            <Card.Body className="p-4">
                                <div className="d-flex align-items-center mb-3">
                                    <div className="me-3 text-primary">{section.icon}</div>
                                    <h4 className="fw-bold mb-0" style={{ color: '#3174ad' }}>{section.title}</h4>
                                </div>
                                <ul className="list-unstyled">
                                    {section.content.map((item, idx) => (
                                        <li key={idx} className="mb-2">
                                            <span className="text-primary me-2">•</span>
                                            {item}
                                        </li>
                                    ))}
                                </ul>
                            </Card.Body>
                        </Card>
                    </Col>
                ))}
            </Row>

            {/* Subscription and Payment Terms */}
            <Row className="mb-5">
                <Col lg={10} className="mx-auto">
                    <Card className="border-0 shadow-sm">
                        <Card.Body className="p-4">
                            <h4 className="fw-bold mb-3" style={{ color: '#3174ad' }}>Subscription and Payment Terms</h4>
                            <Row>
                                <Col md={6}>
                                    <h5 className="fw-semibold mb-2">Billing</h5>
                                    <ul className="list-unstyled">
                                        <li className="mb-2"><span className="text-primary me-2">•</span>Subscription fees are billed in advance</li>
                                        <li className="mb-2"><span className="text-primary me-2">•</span>All fees are non-refundable unless required by law</li>
                                        <li className="mb-2"><span className="text-primary me-2">•</span>Prices may change with 30 days notice</li>
                                        <li className="mb-2"><span className="text-primary me-2">•</span>Failed payments may result in service suspension</li>
                                    </ul>
                                </Col>
                                <Col md={6}>
                                    <h5 className="fw-semibold mb-2">Cancellation</h5>
                                    <ul className="list-unstyled">
                                        <li className="mb-2"><span className="text-primary me-2">•</span>You may cancel your subscription at any time</li>
                                        <li className="mb-2"><span className="text-primary me-2">•</span>Service continues until the end of your billing period</li>
                                        <li className="mb-2"><span className="text-primary me-2">•</span>Data export is available for 30 days after cancellation</li>
                                        <li className="mb-2"><span className="text-primary me-2">•</span>We may terminate accounts for terms violations</li>
                                    </ul>
                                </Col>
                            </Row>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>

            {/* Intellectual Property */}
            <Row className="mb-5">
                <Col lg={10} className="mx-auto">
                    <Card className="border-0 shadow-sm">
                        <Card.Body className="p-4">
                            <h4 className="fw-bold mb-3" style={{ color: '#3174ad' }}>Intellectual Property Rights</h4>
                            <Row>
                                <Col md={6}>
                                    <h5 className="fw-semibold mb-2">Our Rights</h5>
                                    <ul className="list-unstyled">
                                        <li className="mb-2"><span className="text-primary me-2">•</span>WithKalenda owns all platform software and technology</li>
                                        <li className="mb-2"><span className="text-primary me-2">•</span>Our trademarks and logos are protected</li>
                                        <li className="mb-2"><span className="text-primary me-2">•</span>Platform design and functionality are proprietary</li>
                                        <li className="mb-2"><span className="text-primary me-2">•</span>Unauthorized copying or distribution is prohibited</li>
                                    </ul>
                                </Col>
                                <Col md={6}>
                                    <h5 className="fw-semibold mb-2">Your Rights</h5>
                                    <ul className="list-unstyled">
                                        <li className="mb-2"><span className="text-primary me-2">•</span>You retain ownership of your event data and content</li>
                                        <li className="mb-2"><span className="text-primary me-2">•</span>You grant us license to process your data for service delivery</li>
                                        <li className="mb-2"><span className="text-primary me-2">•</span>You can export your data at any time</li>
                                        <li className="mb-2"><span className="text-primary me-2">•</span>You're responsible for content you upload</li>
                                    </ul>
                                </Col>
                            </Row>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>

            {/* Limitation of Liability */}
            <Row className="mb-5">
                <Col lg={10} className="mx-auto">
                    <Card className="border-0 shadow-sm">
                        <Card.Body className="p-4">
                            <h4 className="fw-bold mb-3" style={{ color: '#3174ad' }}>Limitation of Liability and Disclaimers</h4>
                            <div className="mb-4">
                                <h5 className="fw-semibold mb-2">Service Availability</h5>
                                <p className="mb-3">
                                    While we strive for 99.9% uptime, we cannot guarantee uninterrupted service.
                                    We are not liable for service interruptions, data loss, or any consequential damages
                                    resulting from service unavailability.
                                </p>
                            </div>
                            <div className="mb-4">
                                <h5 className="fw-semibold mb-2">Limitation of Damages</h5>
                                <p className="mb-3">
                                    Our total liability to you for any claims arising from these terms or your use of
                                    WithKalenda shall not exceed the amount you paid us in the 12 months preceding the claim.
                                </p>
                            </div>
                            <div>
                                <h5 className="fw-semibold mb-2">Disclaimer of Warranties</h5>
                                <p className="mb-0">
                                    WithKalenda is provided "as is" without warranties of any kind. We disclaim all
                                    warranties, express or implied, including merchantability, fitness for a particular
                                    purpose, and non-infringement.
                                </p>
                            </div>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>

            {/* Governing Law */}
            <Row className="mb-5">
                <Col lg={10} className="mx-auto">
                    <Row className="g-4">
                        <Col md={6}>
                            <Card className="border-0 shadow-sm h-100">
                                <Card.Body className="p-4">
                                    <h4 className="fw-bold mb-3" style={{ color: '#3174ad' }}>Governing Law</h4>
                                    <ul className="list-unstyled">
                                        <li className="mb-2"><span className="text-primary me-2">•</span>These terms are governed by California law</li>
                                        <li className="mb-2"><span className="text-primary me-2">•</span>Disputes will be resolved in San Francisco courts</li>
                                        <li className="mb-2"><span className="text-primary me-2">•</span>Arbitration may be required for certain disputes</li>
                                        <li className="mb-0"><span className="text-primary me-2">•</span>Class action lawsuits are waived</li>
                                    </ul>
                                </Card.Body>
                            </Card>
                        </Col>
                        <Col md={6}>
                            <Card className="border-0 shadow-sm h-100">
                                <Card.Body className="p-4">
                                    <h4 className="fw-bold mb-3" style={{ color: '#3174ad' }}>Contact for Legal Matters</h4>
                                    <p className="mb-3">
                                        For legal inquiries or to report terms violations:
                                    </p>
                                    <div className="mb-0">
                                        <strong>Email:</strong> <EMAIL><br />
                                        <strong>Address:</strong> 123 Innovation Drive<br />
                                        San Francisco, CA 94105<br />
                                        <strong>Phone:</strong> +1 (555) 123-KALE
                                    </div>
                                </Card.Body>
                            </Card>
                        </Col>
                    </Row>
                </Col>
            </Row>

            {/* Changes to Terms */}
            <Row className="mb-5">
                <Col lg={8} className="mx-auto">
                    <Card className="border-0 shadow-sm">
                        <Card.Body className="p-4 text-center">
                            <h4 className="fw-bold mb-3" style={{ color: '#3174ad' }}>Changes to These Terms</h4>
                            <p className="mb-3">
                                We may update these Terms of Service from time to time to reflect changes in our
                                services, legal requirements, or business practices.
                            </p>
                            <ul className="list-unstyled text-start">
                                <li className="mb-2"><span className="text-primary me-2">•</span>We will notify you of material changes via email or platform notification</li>
                                <li className="mb-2"><span className="text-primary me-2">•</span>Changes become effective 30 days after notification</li>
                                <li className="mb-2"><span className="text-primary me-2">•</span>Continued use after changes constitutes acceptance</li>
                                <li className="mb-0"><span className="text-primary me-2">•</span>You may terminate your account if you disagree with changes</li>
                            </ul>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>

            {/* Severability */}
            <Row>
                <Col lg={8} className="mx-auto">
                    <div className="p-4 rounded-4 text-center" style={{ backgroundColor: 'rgba(49, 116, 173, 0.05)' }}>
                        <h5 className="fw-bold mb-2" style={{ color: '#3174ad' }}>Severability</h5>
                        <p className="mb-0">
                            If any provision of these terms is found to be unenforceable, the remaining provisions
                            will continue in full force and effect. The unenforceable provision will be replaced
                            with an enforceable provision that most closely reflects the original intent.
                        </p>
                    </div>
                </Col>
            </Row>
        </Container>
    );
};

export default Terms;
