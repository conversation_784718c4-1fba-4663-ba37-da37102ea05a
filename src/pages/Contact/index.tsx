import React, { useState } from 'react';
import { Container, Row, Col, Card, Form, Button, Alert } from 'react-bootstrap';
import { Mail, Phone, MapPin, Clock, Send, MessageCircle, HeadphonesIcon } from 'lucide-react';

const Contact = () => {
    const [formData, setFormData] = useState({
        name: '',
        email: '',
        company: '',
        subject: '',
        message: '',
        inquiryType: 'general'
    });
    const [showSuccess, setShowSuccess] = useState(false);
    const [isSubmitting, setIsSubmitting] = useState(false);

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
        const { name, value } = e.target;
        setFormData(prev => ({ ...prev, [name]: value }));
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        setIsSubmitting(true);

        // Simulate form submission
        setTimeout(() => {
            setShowSuccess(true);
            setIsSubmitting(false);
            setFormData({
                name: '',
                email: '',
                company: '',
                subject: '',
                message: '',
                inquiryType: 'general'
            });

            // Hide success message after 5 seconds
            setTimeout(() => setShowSuccess(false), 5000);
        }, 1000);
    };

    const contactInfo = [
        {
            icon: <Mail size={24} />,
            title: 'Email Us',
            content: '<EMAIL>',
            description: 'Send us an email and we\'ll respond within 24 hours'
        },
        {
            icon: <Phone size={24} />,
            title: 'Call Us',
            content: '+1 (555) 123-KALE',
            description: 'Monday to Friday, 9 AM to 6 PM EST'
        },
        {
            icon: <MapPin size={24} />,
            title: 'Visit Us',
            content: '123 Innovation Drive, San Francisco, CA 94105',
            description: 'Our headquarters in the heart of Silicon Valley'
        },
        {
            icon: <Clock size={24} />,
            title: 'Support Hours',
            content: '24/7 Online Support',
            description: 'Live chat and email support available around the clock'
        }
    ];

    const inquiryTypes = [
        { value: 'general', label: 'General Inquiry' },
        { value: 'sales', label: 'Sales & Pricing' },
        { value: 'support', label: 'Technical Support' },
        { value: 'partnership', label: 'Partnership Opportunities' },
        { value: 'media', label: 'Media & Press' }
    ];

    return (
        <Container className="py-5">
            {/* Header */}
            <Row className="mb-5 text-center">
                <Col lg={8} className="mx-auto">
                    <h1 className="display-4 fw-bold mb-3" style={{ color: '#3174ad' }}>Get in Touch</h1>
                    <p className="lead mb-4">
                        Have questions about WithKalenda? We'd love to hear from you.
                        Send us a message and we'll respond as soon as possible.
                    </p>
                </Col>
            </Row>

            {/* Contact Info Cards */}
            <Row className="mb-5 g-4">
                {contactInfo.map((info, index) => (
                    <Col md={6} lg={3} key={index}>
                        <Card className="h-100 border-0 shadow-sm text-center">
                            <Card.Body className="p-4">
                                <div className="mb-3 text-primary">{info.icon}</div>
                                <h5 className="fw-bold mb-2">{info.title}</h5>
                                <p className="fw-semibold mb-2" style={{ color: '#3174ad' }}>{info.content}</p>
                                <p className="text-muted small mb-0">{info.description}</p>
                            </Card.Body>
                        </Card>
                    </Col>
                ))}
            </Row>

            {/* Contact Form and Info */}
            <Row className="mb-5">
                <Col lg={8}>
                    <Card className="border-0 shadow">
                        <Card.Body className="p-5">
                            <h2 className="fw-bold mb-4" style={{ color: '#3174ad' }}>Send us a Message</h2>

                            {showSuccess && (
                                <Alert variant="success" className="mb-4">
                                    <strong>Thank you!</strong> Your message has been sent successfully.
                                    We'll get back to you within 24 hours.
                                </Alert>
                            )}

                            <Form onSubmit={handleSubmit}>
                                <Row>
                                    <Col md={6}>
                                        <Form.Group className="mb-3">
                                            <Form.Label>Full Name *</Form.Label>
                                            <Form.Control
                                                type="text"
                                                name="name"
                                                value={formData.name}
                                                onChange={handleInputChange}
                                                required
                                                placeholder="Your full name"
                                            />
                                        </Form.Group>
                                    </Col>
                                    <Col md={6}>
                                        <Form.Group className="mb-3">
                                            <Form.Label>Email Address *</Form.Label>
                                            <Form.Control
                                                type="email"
                                                name="email"
                                                value={formData.email}
                                                onChange={handleInputChange}
                                                required
                                                placeholder="<EMAIL>"
                                            />
                                        </Form.Group>
                                    </Col>
                                </Row>

                                <Row>
                                    <Col md={6}>
                                        <Form.Group className="mb-3">
                                            <Form.Label>Company/Organization</Form.Label>
                                            <Form.Control
                                                type="text"
                                                name="company"
                                                value={formData.company}
                                                onChange={handleInputChange}
                                                placeholder="Your organization name"
                                            />
                                        </Form.Group>
                                    </Col>
                                    <Col md={6}>
                                        <Form.Group className="mb-3">
                                            <Form.Label>Inquiry Type</Form.Label>
                                            <Form.Select
                                                name="inquiryType"
                                                value={formData.inquiryType}
                                                onChange={handleInputChange}
                                            >
                                                {inquiryTypes.map(type => (
                                                    <option key={type.value} value={type.value}>
                                                        {type.label}
                                                    </option>
                                                ))}
                                            </Form.Select>
                                        </Form.Group>
                                    </Col>
                                </Row>

                                <Form.Group className="mb-3">
                                    <Form.Label>Subject *</Form.Label>
                                    <Form.Control
                                        type="text"
                                        name="subject"
                                        value={formData.subject}
                                        onChange={handleInputChange}
                                        required
                                        placeholder="Brief description of your inquiry"
                                    />
                                </Form.Group>

                                <Form.Group className="mb-4">
                                    <Form.Label>Message *</Form.Label>
                                    <Form.Control
                                        as="textarea"
                                        rows={5}
                                        name="message"
                                        value={formData.message}
                                        onChange={handleInputChange}
                                        required
                                        placeholder="Tell us more about how we can help you..."
                                    />
                                </Form.Group>

                                <Button
                                    type="submit"
                                    size="lg"
                                    disabled={isSubmitting}
                                    className="d-flex align-items-center"
                                    style={{ backgroundColor: '#ff6b35', borderColor: '#ff6b35' }}
                                >
                                    <Send size={20} className="me-2" />
                                    {isSubmitting ? 'Sending...' : 'Send Message'}
                                </Button>
                            </Form>
                        </Card.Body>
                    </Card>
                </Col>

                <Col lg={4}>
                    <div className="ps-lg-4">
                        {/* Quick Contact */}
                        <Card className="border-0 shadow-sm mb-4">
                            <Card.Body className="p-4">
                                <h4 className="fw-bold mb-3" style={{ color: '#3174ad' }}>
                                    <MessageCircle size={24} className="me-2" />
                                    Quick Contact
                                </h4>
                                <p className="text-muted mb-3">
                                    Need immediate assistance? Choose the fastest way to reach us:
                                </p>
                                <div className="d-grid gap-2">
                                    <Button variant="outline-primary" size="sm">
                                        <HeadphonesIcon size={16} className="me-2" />
                                        Live Chat
                                    </Button>
                                    <Button variant="outline-primary" size="sm">
                                        <Phone size={16} className="me-2" />
                                        Schedule Call
                                    </Button>
                                </div>
                            </Card.Body>
                        </Card>

                        {/* FAQ Link */}
                        <Card className="border-0 shadow-sm">
                            <Card.Body className="p-4">
                                <h4 className="fw-bold mb-3" style={{ color: '#3174ad' }}>
                                    Frequently Asked Questions
                                </h4>
                                <p className="text-muted mb-3">
                                    Find answers to common questions about WithKalenda features, pricing, and setup.
                                </p>
                                <Button variant="outline-primary" size="sm">
                                    View FAQ
                                </Button>
                            </Card.Body>
                        </Card>
                    </div>
                </Col>
            </Row>

            {/* CTA Section */}
            <Row className="text-center">
                <Col>
                    <div className="p-5 rounded-4" style={{ backgroundColor: 'rgba(49, 116, 173, 0.05)' }}>
                        <h3 className="fw-bold mb-3" style={{ color: '#3174ad' }}>
                            Ready to Get Started?
                        </h3>
                        <p className="text-muted mb-4">
                            Don't wait! Start coordinating your events with WithKalenda today.
                        </p>
                        <Button
                            size="lg"
                            className="px-4 py-2"
                            style={{ backgroundColor: '#ff6b35', borderColor: '#ff6b35' }}
                        >
                            Start Free Trial
                        </Button>
                    </div>
                </Col>
            </Row>
        </Container>
    );
};

export default Contact;
