import React, { useState } from 'react';
import { Mail, Phone, MapPin, Clock, Send, MessageCircle, HeadphonesIcon } from 'lucide-react';
import { Card } from '../../components/ui/card';
import { Input } from '../../components/ui/input';
import { Label } from '../../components/ui/label';
import { Textarea } from '../../components/ui/textarea';
import { Button } from '../../components/ui/button';
import { Alert, AlertDescription } from '../../components/ui/alert';
import Layout from '../../components/Layout';

const Contact = () => {
    const [formData, setFormData] = useState({
        name: '',
        email: '',
        company: '',
        subject: '',
        message: '',
        inquiryType: 'general'
    });
    const [showSuccess, setShowSuccess] = useState(false);
    const [isSubmitting, setIsSubmitting] = useState(false);

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
        const { name, value } = e.target;
        setFormData(prev => ({ ...prev, [name]: value }));
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        setIsSubmitting(true);

        // Simulate form submission
        setTimeout(() => {
            setShowSuccess(true);
            setIsSubmitting(false);
            setFormData({
                name: '',
                email: '',
                company: '',
                subject: '',
                message: '',
                inquiryType: 'general'
            });

            // Hide success message after 5 seconds
            setTimeout(() => setShowSuccess(false), 5000);
        }, 1000);
    };

    const contactInfo = [
        {
            icon: <Mail size={24} />,
            title: 'Email Us',
            content: '<EMAIL>',
            description: 'Send us an email and we\'ll respond within 24 hours'
        },
        {
            icon: <Phone size={24} />,
            title: 'Call Us',
            content: '+1 (555) 123-KALE',
            description: 'Monday to Friday, 9 AM to 6 PM EST'
        },
        {
            icon: <MapPin size={24} />,
            title: 'Visit Us',
            content: '123 Innovation Drive, San Francisco, CA 94105',
            description: 'Our headquarters in the heart of Silicon Valley'
        },
        {
            icon: <Clock size={24} />,
            title: 'Support Hours',
            content: '24/7 Online Support',
            description: 'Live chat and email support available around the clock'
        }
    ];

    const inquiryTypes = [
        { value: 'general', label: 'General Inquiry' },
        { value: 'sales', label: 'Sales & Pricing' },
        { value: 'support', label: 'Technical Support' },
        { value: 'partnership', label: 'Partnership Opportunities' },
        { value: 'media', label: 'Media & Press' }
    ];

    return (
        <Layout>
            <div className="py-12">
                <div className="wk-container">
                    {/* Header */}
                    <div className="text-center mb-12 max-w-4xl mx-auto">
                        <h1 className="text-4xl font-bold mb-4 text-brand-blue">Get in Touch</h1>
                        <p className="text-xl text-gray-600 mb-8">
                            Have questions about WithKalenda? We'd love to hear from you.
                            Send us a message and we'll respond as soon as possible.
                        </p>
                    </div>

                    {/* Contact Info Cards */}
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
                        {contactInfo.map((info, index) => (
                            <Card key={index} className="text-center">
                                <div className="p-6">
                                    <div className="mb-4 text-brand-blue flex justify-center">{info.icon}</div>
                                    <h5 className="font-bold mb-2">{info.title}</h5>
                                    <p className="font-semibold mb-2 text-brand-blue">{info.content}</p>
                                    <p className="text-gray-500 text-sm">{info.description}</p>
                                </div>
                            </Card>
                        ))}
                    </div>

                    {/* Contact Form and Info */}
                    <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-12">
                        <div className="lg:col-span-2">
                            <Card>
                                <div className="p-8">
                                    <h2 className="text-2xl font-bold mb-6 text-brand-blue">Send us a Message</h2>

                                    {showSuccess && (
                                        <Alert className="mb-6">
                                            <AlertDescription>
                                                <strong>Thank you!</strong> Your message has been sent successfully.
                                                We'll get back to you within 24 hours.
                                            </AlertDescription>
                                        </Alert>
                                    )}

                                    <form onSubmit={handleSubmit} className="space-y-6">
                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                            <div>
                                                <Label htmlFor="name">Full Name *</Label>
                                                <Input
                                                    id="name"
                                                    type="text"
                                                    name="name"
                                                    value={formData.name}
                                                    onChange={handleInputChange}
                                                    required
                                                    placeholder="Your full name"
                                                    className="mt-1"
                                                />
                                            </div>
                                            <div>
                                                <Label htmlFor="email">Email Address *</Label>
                                                <Input
                                                    id="email"
                                                    type="email"
                                                    name="email"
                                                    value={formData.email}
                                                    onChange={handleInputChange}
                                                    required
                                                    placeholder="<EMAIL>"
                                                    className="mt-1"
                                                />
                                            </div>
                                        </div>

                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                            <div>
                                                <Label htmlFor="company">Company/Organization</Label>
                                                <Input
                                                    id="company"
                                                    type="text"
                                                    name="company"
                                                    value={formData.company}
                                                    onChange={handleInputChange}
                                                    placeholder="Your organization name"
                                                    className="mt-1"
                                                />
                                            </div>
                                            <div>
                                                <Label htmlFor="subject">Subject *</Label>
                                                <Input
                                                    id="subject"
                                                    type="text"
                                                    name="subject"
                                                    value={formData.subject}
                                                    onChange={handleInputChange}
                                                    required
                                                    placeholder="Brief description of your inquiry"
                                                    className="mt-1"
                                                />
                                            </div>
                                        </div>

                                        <div>
                                            <Label htmlFor="message">Message *</Label>
                                            <Textarea
                                                id="message"
                                                name="message"
                                                value={formData.message}
                                                onChange={handleInputChange}
                                                required
                                                placeholder="Tell us more about how we can help you..."
                                                rows={5}
                                                className="mt-1"
                                            />
                                        </div>

                                        <div>
                                            <Button
                                                type="submit"
                                                size="lg"
                                                disabled={isSubmitting}
                                                variant="brand-orange"
                                                className="w-full"
                                            >
                                                {isSubmitting ? (
                                                    'Sending...'
                                                ) : (
                                                    <>
                                                        <Send size={16} className="mr-2" />
                                                        Send Message
                                                    </>
                                                )}
                                            </Button>
                                        </div>
                                    </form>
                                </div>
                            </Card>
                        </div>

                        <div>
                            <div className="space-y-6">
                                {/* Quick Contact */}
                                <Card>
                                    <div className="p-6">
                                        <h4 className="text-xl font-bold mb-4 text-brand-blue flex items-center">
                                            <MessageCircle size={24} className="mr-2" />
                                            Quick Contact
                                        </h4>
                                        <p className="text-gray-600 mb-4">
                                            Need immediate assistance? Choose the fastest way to reach us:
                                        </p>
                                        <div className="space-y-3">
                                            <Button variant="outline" size="sm" className="w-full">
                                                <HeadphonesIcon size={16} className="mr-2" />
                                                Live Chat
                                            </Button>
                                            <Button variant="outline" size="sm" className="w-full">
                                                <Phone size={16} className="mr-2" />
                                                Schedule Call
                                            </Button>
                                        </div>
                                    </div>
                                </Card>

                                {/* FAQ Link */}
                                <Card>
                                    <div className="p-6">
                                        <h4 className="text-xl font-bold mb-4 text-brand-blue">
                                            Frequently Asked Questions
                                        </h4>
                                        <p className="text-gray-600 mb-4">
                                            Find answers to common questions about WithKalenda features, pricing, and setup.
                                        </p>
                                        <Button variant="outline" size="sm" className="w-full">
                                            View FAQ
                                        </Button>
                                    </div>
                                </Card>
                            </div>
                        </div>
                    </div>

                    {/* CTA Section */}
                    <div className="text-center">
                        <div className="wk-card-accent p-8 rounded-lg">
                            <h3 className="text-2xl font-bold mb-4 text-brand-blue">
                                Ready to Get Started?
                            </h3>
                            <p className="text-gray-600 mb-6">
                                Don't wait! Start coordinating your events with WithKalenda today.
                            </p>
                            <Button
                                size="lg"
                                variant="brand-orange"
                            >
                                Start Free Trial
                            </Button>
                        </div>
                    </div>
                </div>
            </div>
        </Layout>
    );
};

export default Contact;
