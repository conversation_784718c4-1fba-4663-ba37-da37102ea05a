import React, { useState, useEffect } from 'react';
import {
    Container,
    Row,
    Col,
    Card,
    Form,
    But<PERSON>,
    Al<PERSON>,
    Spinner,
    InputGroup
} from 'react-bootstrap';
import { Link, useNavigate } from 'react-router-dom';
import { Eye, EyeOff, User, Mail, Building, Briefcase } from 'lucide-react';
import { SignupFormData, UserRole } from '../../types/user';
import { useAuth } from '../../contexts/AuthContext';
import './Signup.css';

const Signup: React.FC = () => {
    const navigate = useNavigate();
    const { login, isAuthenticated } = useAuth();
    const [formData, setFormData] = useState<SignupFormData>({
        firstName: '',
        lastName: '',
        email: '',
        password: '',
        confirmPassword: '',
        organization: '',
        role: UserRole.EMPLOYEE,
        departmentId: '',
        agreeToTerms: false
    });

    const [errors, setErrors] = useState<Partial<SignupFormData>>({});
    const [isLoading, setIsLoading] = useState(false);
    const [showPassword, setShowPassword] = useState(false);
    const [showConfirmPassword, setShowConfirmPassword] = useState(false);
    const [submitError, setSubmitError] = useState<string>('');

    // Redirect if already authenticated
    useEffect(() => {
        if (isAuthenticated) {
            navigate('/dashboard');
        }
    }, [isAuthenticated, navigate]);

    // Handle input changes
    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
        const { name, value, type } = e.target;
        const checked = (e.target as HTMLInputElement).checked;

        setFormData(prev => ({
            ...prev,
            [name]: type === 'checkbox' ? checked : value
        }));

        // Clear error when user starts typing
        if (errors[name as keyof SignupFormData]) {
            setErrors(prev => ({
                ...prev,
                [name]: undefined
            }));
        }
    };

    // Validate form
    const validateForm = (): boolean => {
        const newErrors: Partial<SignupFormData> = {};

        // First name validation
        if (!formData.firstName.trim()) {
            newErrors.firstName = 'First name is required';
        } else if (formData.firstName.trim().length < 2) {
            newErrors.firstName = 'First name must be at least 2 characters';
        }

        // Last name validation
        if (!formData.lastName.trim()) {
            newErrors.lastName = 'Last name is required';
        } else if (formData.lastName.trim().length < 2) {
            newErrors.lastName = 'Last name must be at least 2 characters';
        }

        // Email validation
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!formData.email.trim()) {
            newErrors.email = 'Email is required';
        } else if (!emailRegex.test(formData.email)) {
            newErrors.email = 'Please enter a valid email address';
        }

        // Password validation
        if (!formData.password) {
            newErrors.password = 'Password is required';
        } else if (formData.password.length < 8) {
            newErrors.password = 'Password must be at least 8 characters';
        } else if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(formData.password)) {
            newErrors.password = 'Password must contain at least one uppercase letter, one lowercase letter, and one number';
        }

        // Confirm password validation
        if (!formData.confirmPassword) {
            newErrors.confirmPassword = 'Please confirm your password';
        } else if (formData.password !== formData.confirmPassword) {
            newErrors.confirmPassword = 'Passwords do not match';
        }

        // Terms agreement validation
        if (!formData.agreeToTerms) {
            (newErrors as any).agreeToTerms = 'You must agree to the terms and conditions';
        }

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    // Handle form submission
    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        setSubmitError('');

        if (!validateForm()) {
            return;
        }

        setIsLoading(true);

        try {
            // TODO: Replace with actual API call
            // const response = await axios.post('/api/auth/signup', {
            //     firstName: formData.firstName,
            //     lastName: formData.lastName,
            //     email: formData.email,
            //     password: formData.password,
            //     organization: formData.organization,
            //     role: formData.role
            // });

            // Simulate API call
            await new Promise(resolve => setTimeout(resolve, 2000));

            // Simulate successful signup and auto-login
            const userData = {
                id: Date.now(), // Simulate generated ID
                firstName: formData.firstName,
                lastName: formData.lastName,
                email: formData.email,
                organization: formData.organization,
                role: formData.role,
                departmentId: formData.departmentId,
                isActive: true
            };

            // Auto-login the user after successful signup
            login(userData, 'demo-token-' + Date.now());

            // Redirect to dashboard
            navigate('/dashboard', { replace: true });
        } catch (error: any) {
            setSubmitError(
                error.response?.data?.message ||
                'An error occurred while creating your account. Please try again.'
            );
        } finally {
            setIsLoading(false);
        }
    };

    const roleOptions = [
        { value: UserRole.EMPLOYEE, label: 'Employee' },
        { value: UserRole.TEAM_LEADER, label: 'Team Leader' },
        { value: UserRole.ADMIN, label: 'Administrator' }
    ];

    return (
        <div className="signup-container d-flex align-items-center py-5">
            <Container>
                <Row className="justify-content-center">
                    <Col md={8} lg={6} xl={5}>
                        <Card className="signup-card shadow-lg border-0">
                            <Card.Body className="p-5">
                                <div className="text-center mb-4">
                                    <h1 className="h3 mb-3 fw-bold text-primary">Create Your Account</h1>
                                    <p className="text-muted">Join EVA Planning and start organizing amazing events</p>
                                </div>

                                {submitError && (
                                    <Alert variant="danger" className="mb-4">
                                        {submitError}
                                    </Alert>
                                )}

                                <Form onSubmit={handleSubmit} className="signup-form">
                                    {/* Name Fields */}
                                    <Row className="mb-3">
                                        <Col md={6}>
                                            <Form.Group>
                                                <Form.Label className="fw-semibold">
                                                    <User size={16} className="me-2" />
                                                    First Name
                                                </Form.Label>
                                                <Form.Control
                                                    type="text"
                                                    name="firstName"
                                                    value={formData.firstName}
                                                    onChange={handleInputChange}
                                                    isInvalid={!!errors.firstName}
                                                    placeholder="Enter your first name"
                                                />
                                                <Form.Control.Feedback type="invalid">
                                                    {errors.firstName}
                                                </Form.Control.Feedback>
                                            </Form.Group>
                                        </Col>
                                        <Col md={6}>
                                            <Form.Group>
                                                <Form.Label className="fw-semibold">
                                                    <User size={16} className="me-2" />
                                                    Last Name
                                                </Form.Label>
                                                <Form.Control
                                                    type="text"
                                                    name="lastName"
                                                    value={formData.lastName}
                                                    onChange={handleInputChange}
                                                    isInvalid={!!errors.lastName}
                                                    placeholder="Enter your last name"
                                                />
                                                <Form.Control.Feedback type="invalid">
                                                    {errors.lastName}
                                                </Form.Control.Feedback>
                                            </Form.Group>
                                        </Col>
                                    </Row>

                                    {/* Email Field */}
                                    <Form.Group className="mb-3">
                                        <Form.Label className="fw-semibold">
                                            <Mail size={16} className="me-2" />
                                            Email Address
                                        </Form.Label>
                                        <Form.Control
                                            type="email"
                                            name="email"
                                            value={formData.email}
                                            onChange={handleInputChange}
                                            isInvalid={!!errors.email}
                                            placeholder="Enter your email address"
                                        />
                                        <Form.Control.Feedback type="invalid">
                                            {errors.email}
                                        </Form.Control.Feedback>
                                    </Form.Group>

                                    {/* Password Fields */}
                                    <Row className="mb-3">
                                        <Col md={6}>
                                            <Form.Group>
                                                <Form.Label className="fw-semibold">Password</Form.Label>
                                                <InputGroup>
                                                    <Form.Control
                                                        type={showPassword ? "text" : "password"}
                                                        name="password"
                                                        value={formData.password}
                                                        onChange={handleInputChange}
                                                        isInvalid={!!errors.password}
                                                        placeholder="Create a password"
                                                    />
                                                    <Button
                                                        variant="outline-secondary"
                                                        className="password-toggle-btn"
                                                        onClick={() => setShowPassword(!showPassword)}
                                                        style={{ zIndex: 1 }}
                                                    >
                                                        {showPassword ? <EyeOff size={16} /> : <Eye size={16} />}
                                                    </Button>
                                                </InputGroup>
                                                <Form.Control.Feedback type="invalid">
                                                    {errors.password}
                                                </Form.Control.Feedback>
                                            </Form.Group>
                                        </Col>
                                        <Col md={6}>
                                            <Form.Group>
                                                <Form.Label className="fw-semibold">Confirm Password</Form.Label>
                                                <InputGroup>
                                                    <Form.Control
                                                        type={showConfirmPassword ? "text" : "password"}
                                                        name="confirmPassword"
                                                        value={formData.confirmPassword}
                                                        onChange={handleInputChange}
                                                        isInvalid={!!errors.confirmPassword}
                                                        placeholder="Confirm your password"
                                                    />
                                                    <Button
                                                        variant="outline-secondary"
                                                        className="password-toggle-btn"
                                                        onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                                                        style={{ zIndex: 1 }}
                                                    >
                                                        {showConfirmPassword ? <EyeOff size={16} /> : <Eye size={16} />}
                                                    </Button>
                                                </InputGroup>
                                                <Form.Control.Feedback type="invalid">
                                                    {errors.confirmPassword}
                                                </Form.Control.Feedback>
                                            </Form.Group>
                                        </Col>
                                    </Row>

                                    {/* Optional Fields */}
                                    <Row className="mb-3">
                                        <Col md={6}>
                                            <Form.Group>
                                                <Form.Label className="fw-semibold">
                                                    <Building size={16} className="me-2" />
                                                    Organization (Optional)
                                                </Form.Label>
                                                <Form.Control
                                                    type="text"
                                                    name="organization"
                                                    value={formData.organization}
                                                    onChange={handleInputChange}
                                                    placeholder="Your company or organization"
                                                />
                                            </Form.Group>
                                        </Col>
                                        <Col md={6}>
                                            <Form.Group>
                                                <Form.Label className="fw-semibold">
                                                    <Briefcase size={16} className="me-2" />
                                                    Role (Optional)
                                                </Form.Label>
                                                <Form.Select
                                                    name="role"
                                                    value={formData.role}
                                                    onChange={handleInputChange}
                                                >
                                                    {roleOptions.map(option => (
                                                        <option key={option.value} value={option.value}>
                                                            {option.label}
                                                        </option>
                                                    ))}
                                                </Form.Select>
                                            </Form.Group>
                                        </Col>
                                    </Row>

                                    {/* Terms and Conditions */}
                                    <Form.Group className="mb-4">
                                        <Form.Check
                                            type="checkbox"
                                            name="agreeToTerms"
                                            checked={formData.agreeToTerms}
                                            onChange={handleInputChange}
                                            isInvalid={!!errors.agreeToTerms}
                                            label={
                                                <span>
                                                    I agree to the{' '}
                                                    <Link to="/terms" className="terms-link">
                                                        Terms of Service
                                                    </Link>{' '}
                                                    and{' '}
                                                    <Link to="/privacy" className="terms-link">
                                                        Privacy Policy
                                                    </Link>
                                                </span>
                                            }
                                        />
                                        <Form.Control.Feedback type="invalid">
                                            {(errors as any).agreeToTerms}
                                        </Form.Control.Feedback>
                                    </Form.Group>

                                    {/* Submit Button */}
                                    <Button
                                        type="submit"
                                        variant="primary"
                                        size="lg"
                                        className="signup-btn w-100 mb-3"
                                        disabled={isLoading}
                                    >
                                        {isLoading ? (
                                            <>
                                                <Spinner
                                                    as="span"
                                                    animation="border"
                                                    size="sm"
                                                    role="status"
                                                    aria-hidden="true"
                                                    className="signup-spinner me-2"
                                                />
                                                Creating Account...
                                            </>
                                        ) : (
                                            'Create Account'
                                        )}
                                    </Button>

                                    {/* Login Link */}
                                    <div className="text-center">
                                        <span className="text-muted">Already have an account? </span>
                                        <Link to="/login" className="login-link">
                                            Sign in here
                                        </Link>
                                    </div>
                                </Form>
                            </Card.Body>
                        </Card>
                    </Col>
                </Row>
            </Container>
        </div>
    );
};

export default Signup;
