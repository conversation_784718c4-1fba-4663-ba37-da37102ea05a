# Signup Page

A comprehensive user registration page for the EVA Planning application.

## Features

### Form Fields
- **First Name** - Required, minimum 2 characters
- **Last Name** - Required, minimum 2 characters  
- **Email** - Required, valid email format
- **Password** - Required, minimum 8 characters with uppercase, lowercase, and number
- **Confirm Password** - Required, must match password
- **Organization** - Optional company/organization name
- **Role** - Optional dropdown with predefined roles
- **Terms Agreement** - Required checkbox for terms and privacy policy

### Validation
- Real-time form validation with error messages
- Password strength requirements
- Email format validation
- Password confirmation matching
- Terms agreement requirement

### User Experience
- Password visibility toggle for both password fields
- Loading states during form submission
- Error handling with user-friendly messages
- Responsive design for mobile and desktop
- Custom styling with gradient backgrounds
- Form field icons using Lucide React

### Security Features
- Password strength validation
- Client-side form validation
- Secure password input fields
- Terms and privacy policy links

## Technical Implementation

### Dependencies
- React Bootstrap for UI components
- Lucide React for icons
- React Router for navigation
- TypeScript for type safety

### State Management
- Local component state for form data
- Form validation state
- Loading and error states
- Password visibility toggles

### API Integration
- Ready for backend integration
- Simulated API call with loading state
- Error handling for API failures
- Success navigation to login page

### Styling
- Custom CSS with gradient backgrounds
- Responsive design
- Form validation animations
- Hover and focus effects
- Loading spinner customization

## Usage

The signup page is accessible at `/signup` route and includes:

1. **Form Validation**: All fields are validated on submit and individual field errors clear on input
2. **Password Security**: Strong password requirements with visual feedback
3. **User Feedback**: Loading states and error messages for better UX
4. **Navigation**: Links to login page and terms/privacy pages
5. **Responsive**: Works on all device sizes

## Integration

To integrate with a backend API:

1. Update the API endpoint in the `handleSubmit` function
2. Replace the simulated API call with actual axios request
3. Handle authentication tokens and user data storage
4. Add proper error handling for different API responses

## Customization

The signup page can be customized by:

1. **Styling**: Modify `Signup.css` for visual changes
2. **Fields**: Add/remove form fields in the component
3. **Validation**: Update validation rules in `validateForm` function
4. **Roles**: Modify `roleOptions` array for different user roles
5. **Navigation**: Update redirect paths after successful signup
