/* Signup Page Styles */

.signup-container {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.signup-card {
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
}

.signup-form .form-label {
    color: #495057;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.signup-form .form-control {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.signup-form .form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.signup-form .form-control.is-invalid {
    border-color: #dc3545;
}

.signup-form .form-control.is-invalid:focus {
    border-color: #dc3545;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.password-toggle-btn {
    border-left: none !important;
    background: transparent;
    border: 2px solid #e9ecef;
    border-left: none;
}

.password-toggle-btn:hover {
    background: #f8f9fa;
}

.signup-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 8px;
    padding: 0.75rem 2rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.signup-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.signup-btn:disabled {
    transform: none;
    opacity: 0.7;
}

.terms-link {
    text-decoration: none;
    color: #667eea;
    font-weight: 500;
}

.terms-link:hover {
    color: #764ba2;
    text-decoration: underline;
}

.login-link {
    color: #667eea;
    text-decoration: none;
    font-weight: 600;
}

.login-link:hover {
    color: #764ba2;
    text-decoration: underline;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .signup-container {
        padding: 1rem;
    }
    
    .signup-card .card-body {
        padding: 2rem 1.5rem;
    }
}

/* Animation for form validation */
.form-control.is-invalid {
    animation: shake 0.5s ease-in-out;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

/* Loading spinner customization */
.signup-spinner {
    width: 1rem;
    height: 1rem;
}

/* Icon styling */
.form-label svg {
    color: #667eea;
}

/* Success message styling */
.alert-success {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 8px;
}

/* Error message styling */
.alert-danger {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    color: white;
    border: none;
    border-radius: 8px;
}
