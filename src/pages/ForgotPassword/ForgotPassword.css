/* Forgot Password Page Styles */

.forgot-password-container {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.forgot-password-card {
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
    border-radius: 16px;
    transition: all 0.3s ease;
}

.forgot-password-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 40px rgba(31, 38, 135, 0.5);
}

.forgot-password-form .form-label {
    color: #495057;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.forgot-password-form .form-control {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.forgot-password-form .form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    animation: focusPulse 0.3s ease-out;
}

.forgot-password-form .form-control.is-invalid {
    border-color: #dc3545;
    animation: shake 0.5s ease-in-out;
}

.forgot-password-form .form-control.is-invalid:focus {
    border-color: #dc3545;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.forgot-password-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 8px;
    padding: 0.75rem 2rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.forgot-password-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.forgot-password-btn:disabled {
    transform: none;
    opacity: 0.7;
}

.back-to-login-link {
    color: #667eea;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
}

.back-to-login-link:hover {
    color: #764ba2;
    text-decoration: underline;
    transform: translateX(-3px);
}

/* Success state styling */
.text-success svg {
    color: #28a745 !important;
}

/* Error message styling */
.alert-danger {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    color: white;
    border: none;
    border-radius: 8px;
}

/* Icon styling */
.form-label svg {
    color: #667eea;
}

.text-primary svg {
    color: #667eea !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .forgot-password-container {
        padding: 1rem;
    }
    
    .forgot-password-card .card-body {
        padding: 2rem 1.5rem;
    }
}

/* Animation for form validation */
@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

/* Focus animation */
@keyframes focusPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.02); }
    100% { transform: scale(1); }
}

/* Card entrance animation */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.forgot-password-card {
    animation: fadeInUp 0.6s ease-out;
}

/* Success checkmark animation */
@keyframes checkmarkBounce {
    0% {
        transform: scale(0);
        opacity: 0;
    }
    50% {
        transform: scale(1.2);
        opacity: 1;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

.text-success svg {
    animation: checkmarkBounce 0.6s ease-out;
}

/* Focus states for accessibility */
.forgot-password-btn:focus,
.back-to-login-link:focus {
    outline: 2px solid #667eea;
    outline-offset: 2px;
}

/* Button group styling */
.d-grid.gap-2 .btn {
    border-radius: 8px;
}

.btn-outline-secondary {
    border: 2px solid #6c757d;
    color: #6c757d;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-outline-secondary:hover {
    background: #6c757d;
    border-color: #6c757d;
    transform: translateY(-1px);
}

/* Email highlight styling */
strong {
    color: #667eea;
    font-weight: 600;
}

/* Loading spinner customization */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Card content spacing */
.forgot-password-card .card-body p {
    line-height: 1.6;
}

/* Success message styling */
.text-success {
    color: #28a745 !important;
}

/* Muted text styling */
.text-muted {
    color: #6c757d !important;
}

/* Button hover effects */
.btn-primary:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4c93 100%);
}

/* Form group spacing */
.form-group {
    margin-bottom: 1.5rem;
}

/* Input autofocus styling */
.form-control:focus {
    outline: none;
}

/* Success state button styling */
.btn-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border: none;
}

.btn-success:hover {
    background: linear-gradient(135deg, #218838 0%, #1ea085 100%);
}
