import React, { useState } from 'react';
import {
    Contain<PERSON>,
    <PERSON>,
    Col,
    Card,
    <PERSON>,
    <PERSON><PERSON>,
    Al<PERSON>,
    Spinner
} from 'react-bootstrap';
import { Link, useNavigate } from 'react-router-dom';
import { Mail, ArrowLeft, CheckCircle } from 'lucide-react';
import './ForgotPassword.css';

interface ForgotPasswordFormData {
    email: string;
}

const ForgotPassword: React.FC = () => {
    const navigate = useNavigate();
    
    const [formData, setFormData] = useState<ForgotPasswordFormData>({
        email: ''
    });

    const [errors, setErrors] = useState<Partial<ForgotPasswordFormData>>({});
    const [isLoading, setIsLoading] = useState(false);
    const [submitError, setSubmitError] = useState<string>('');
    const [isEmailSent, setIsEmailSent] = useState(false);

    // Handle input changes
    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { name, value } = e.target;
        
        setFormData(prev => ({
            ...prev,
            [name]: value
        }));

        // Clear error when user starts typing
        if (errors[name as keyof ForgotPasswordFormData]) {
            setErrors(prev => ({
                ...prev,
                [name]: undefined
            }));
        }
    };

    // Validate form
    const validateForm = (): boolean => {
        const newErrors: Partial<ForgotPasswordFormData> = {};

        // Email validation
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!formData.email.trim()) {
            newErrors.email = 'Email is required';
        } else if (!emailRegex.test(formData.email)) {
            newErrors.email = 'Please enter a valid email address';
        }

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    // Handle form submission
    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        setSubmitError('');

        if (!validateForm()) {
            return;
        }

        setIsLoading(true);

        try {
            // TODO: Replace with actual API call
            // const response = await axios.post('/api/auth/forgot-password', {
            //     email: formData.email
            // });

            // Simulate API call
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            // Simulate successful email send
            setIsEmailSent(true);
        } catch (error: any) {
            setSubmitError(
                error.message || 
                'Failed to send reset email. Please try again.'
            );
        } finally {
            setIsLoading(false);
        }
    };

    // Handle back to login
    const handleBackToLogin = () => {
        navigate('/login');
    };

    // Handle resend email
    const handleResendEmail = () => {
        setIsEmailSent(false);
        setFormData({ email: '' });
    };

    if (isEmailSent) {
        return (
            <div className="forgot-password-container d-flex align-items-center py-5">
                <Container>
                    <Row className="justify-content-center">
                        <Col md={6} lg={5} xl={4}>
                            <Card className="forgot-password-card shadow-lg border-0">
                                <Card.Body className="p-5 text-center">
                                    <CheckCircle size={64} className="text-success mb-4" />
                                    <h1 className="h3 mb-3 fw-bold text-success">Email Sent!</h1>
                                    <p className="text-muted mb-4">
                                        We've sent a password reset link to <strong>{formData.email}</strong>
                                    </p>
                                    <p className="text-muted mb-4">
                                        Please check your email and follow the instructions to reset your password.
                                        Don't forget to check your spam folder!
                                    </p>
                                    
                                    <div className="d-grid gap-2">
                                        <Button
                                            variant="primary"
                                            size="lg"
                                            onClick={handleBackToLogin}
                                            className="mb-2"
                                        >
                                            <ArrowLeft size={16} className="me-2" />
                                            Back to Login
                                        </Button>
                                        <Button
                                            variant="outline-secondary"
                                            onClick={handleResendEmail}
                                        >
                                            Send Another Email
                                        </Button>
                                    </div>
                                </Card.Body>
                            </Card>
                        </Col>
                    </Row>
                </Container>
            </div>
        );
    }

    return (
        <div className="forgot-password-container d-flex align-items-center py-5">
            <Container>
                <Row className="justify-content-center">
                    <Col md={6} lg={5} xl={4}>
                        <Card className="forgot-password-card shadow-lg border-0">
                            <Card.Body className="p-5">
                                <div className="text-center mb-4">
                                    <Mail size={48} className="text-primary mb-3" />
                                    <h1 className="h3 mb-3 fw-bold text-primary">Forgot Password?</h1>
                                    <p className="text-muted">
                                        No worries! Enter your email address and we'll send you a link to reset your password.
                                    </p>
                                </div>

                                {submitError && (
                                    <Alert variant="danger" className="mb-4">
                                        {submitError}
                                    </Alert>
                                )}

                                <Form onSubmit={handleSubmit} className="forgot-password-form">
                                    {/* Email Field */}
                                    <Form.Group className="mb-4">
                                        <Form.Label className="fw-semibold">
                                            <Mail size={16} className="me-2" />
                                            Email Address
                                        </Form.Label>
                                        <Form.Control
                                            type="email"
                                            name="email"
                                            value={formData.email}
                                            onChange={handleInputChange}
                                            isInvalid={!!errors.email}
                                            placeholder="Enter your email address"
                                            autoComplete="email"
                                            autoFocus
                                        />
                                        <Form.Control.Feedback type="invalid">
                                            {errors.email}
                                        </Form.Control.Feedback>
                                    </Form.Group>

                                    {/* Submit Button */}
                                    <Button
                                        type="submit"
                                        variant="primary"
                                        size="lg"
                                        className="forgot-password-btn w-100 mb-4"
                                        disabled={isLoading}
                                    >
                                        {isLoading ? (
                                            <>
                                                <Spinner
                                                    as="span"
                                                    animation="border"
                                                    size="sm"
                                                    role="status"
                                                    aria-hidden="true"
                                                    className="me-2"
                                                />
                                                Sending Reset Link...
                                            </>
                                        ) : (
                                            'Send Reset Link'
                                        )}
                                    </Button>

                                    {/* Back to Login */}
                                    <div className="text-center">
                                        <Button
                                            variant="link"
                                            className="p-0 back-to-login-link"
                                            onClick={handleBackToLogin}
                                        >
                                            <ArrowLeft size={16} className="me-2" />
                                            Back to Login
                                        </Button>
                                    </div>
                                </Form>
                            </Card.Body>
                        </Card>
                    </Col>
                </Row>
            </Container>
        </div>
    );
};

export default ForgotPassword;
