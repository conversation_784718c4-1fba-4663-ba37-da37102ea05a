import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Mail, ArrowLeft, CheckCircle } from 'lucide-react';

interface ForgotPasswordFormData {
    email: string;
}

const ForgotPassword: React.FC = () => {
    const navigate = useNavigate();

    const [formData, setFormData] = useState<ForgotPasswordFormData>({
        email: ''
    });

    const [errors, setErrors] = useState<Partial<ForgotPasswordFormData>>({});
    const [isLoading, setIsLoading] = useState(false);
    const [submitError, setSubmitError] = useState<string>('');
    const [isEmailSent, setIsEmailSent] = useState(false);

    // Handle input changes
    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { name, value } = e.target;

        setFormData(prev => ({
            ...prev,
            [name]: value
        }));

        // Clear error when user starts typing
        if (errors[name as keyof ForgotPasswordFormData]) {
            setErrors(prev => ({
                ...prev,
                [name]: undefined
            }));
        }
    };

    // Validate form
    const validateForm = (): boolean => {
        const newErrors: Partial<ForgotPasswordFormData> = {};

        // Email validation
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!formData.email.trim()) {
            newErrors.email = 'Email is required';
        } else if (!emailRegex.test(formData.email)) {
            newErrors.email = 'Please enter a valid email address';
        }

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    // Handle form submission
    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        setSubmitError('');

        if (!validateForm()) {
            return;
        }

        setIsLoading(true);

        try {
            // TODO: Replace with actual API call
            // const response = await axios.post('/api/auth/forgot-password', {
            //     email: formData.email
            // });

            // Simulate API call
            await new Promise(resolve => setTimeout(resolve, 2000));

            // Simulate successful email send
            setIsEmailSent(true);
        } catch (error: any) {
            setSubmitError(
                error.message ||
                'Failed to send reset email. Please try again.'
            );
        } finally {
            setIsLoading(false);
        }
    };

    // Handle back to login
    const handleBackToLogin = () => {
        navigate('/login');
    };

    // Handle resend email
    const handleResendEmail = () => {
        setIsEmailSent(false);
        setFormData({ email: '' });
    };

    if (isEmailSent) {
        return (
            <div className="min-h-screen bg-gradient-to-br from-purple-600 via-purple-700 to-purple-800 flex items-center justify-center p-4">
                <div className="w-full max-w-md">
                    <div className="bg-white rounded-lg shadow-xl p-8 text-center">
                        <CheckCircle size={64} className="text-green-500 mb-4 mx-auto" />
                        <h1 className="text-2xl font-bold text-green-600 mb-3">Email Sent!</h1>
                        <p className="text-gray-600 mb-4">
                            We've sent a password reset link to <strong>{formData.email}</strong>
                        </p>
                        <p className="text-gray-600 mb-6">
                            Please check your email and follow the instructions to reset your password.
                            Don't forget to check your spam folder!
                        </p>

                        <div className="space-y-3">
                            <button
                                onClick={handleBackToLogin}
                                className="w-full bg-gradient-to-r from-purple-600 to-purple-700 text-white py-3 px-4 rounded-md hover:from-purple-700 hover:to-purple-800 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 transition-colors flex items-center justify-center"
                            >
                                <ArrowLeft size={16} className="mr-2" />
                                Back to Login
                            </button>
                            <button
                                onClick={handleResendEmail}
                                className="w-full border border-gray-300 text-gray-700 py-3 px-4 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors"
                            >
                                Send Another Email
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-gradient-to-br from-purple-600 via-purple-700 to-purple-800 flex items-center justify-center p-4">
            <div className="w-full max-w-md">
                <div className="bg-white rounded-lg shadow-xl p-8">
                    <div className="text-center mb-6">
                        <Mail size={48} className="text-blue-600 mb-3 mx-auto" />
                        <h1 className="text-2xl font-bold text-blue-600 mb-2">Forgot Password?</h1>
                        <p className="text-gray-600">
                            No worries! Enter your email address and we'll send you a link to reset your password.
                        </p>
                    </div>

                    {submitError && (
                        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md mb-4">
                            {submitError}
                        </div>
                    )}

                    <form onSubmit={handleSubmit} className="space-y-4">
                        {/* Email Field */}
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                <Mail size={16} className="inline mr-2" />
                                Email Address
                            </label>
                            <input
                                type="email"
                                name="email"
                                value={formData.email}
                                onChange={handleInputChange}
                                className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${errors.email ? 'border-red-500' : 'border-gray-300'
                                    }`}
                                placeholder="Enter your email address"
                                autoComplete="email"
                                autoFocus
                            />
                            {errors.email && (
                                <p className="text-red-500 text-sm mt-1">{errors.email}</p>
                            )}
                        </div>

                        {/* Submit Button */}
                        <button
                            type="submit"
                            className="w-full bg-gradient-to-r from-purple-600 to-purple-700 text-white py-3 px-4 rounded-md hover:from-purple-700 hover:to-purple-800 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                            disabled={isLoading}
                        >
                            {isLoading ? (
                                <div className="flex items-center justify-center">
                                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                                    Sending Reset Link...
                                </div>
                            ) : (
                                'Send Reset Link'
                            )}
                        </button>

                        {/* Back to Login */}
                        <div className="text-center">
                            <button
                                type="button"
                                className="text-blue-600 hover:underline flex items-center justify-center mx-auto"
                                onClick={handleBackToLogin}
                            >
                                <ArrowLeft size={16} className="mr-2" />
                                Back to Login
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    );
};

export default ForgotPassword;
