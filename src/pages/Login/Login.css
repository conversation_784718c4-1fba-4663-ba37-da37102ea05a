/* Login Page Styles */

.login-container {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.login-card {
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
    border-radius: 16px;
}

.login-form .form-label {
    color: #495057;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.login-form .form-control {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.login-form .form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.login-form .form-control.is-invalid {
    border-color: #dc3545;
}

.login-form .form-control.is-invalid:focus {
    border-color: #dc3545;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.password-toggle-btn {
    border-left: none !important;
    background: transparent;
    border: 2px solid #e9ecef;
    border-left: none;
    border-radius: 0 8px 8px 0;
}

.password-toggle-btn:hover {
    background: #f8f9fa;
}

.login-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 8px;
    padding: 0.75rem 2rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.login-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.login-btn:disabled {
    transform: none;
    opacity: 0.7;
}

.forgot-password-link {
    color: #667eea;
    text-decoration: none;
    font-size: 0.9rem;
    font-weight: 500;
}

.forgot-password-link:hover {
    color: #764ba2;
    text-decoration: underline;
}

.signup-link {
    color: #667eea;
    text-decoration: none;
    font-weight: 600;
}

.signup-link:hover {
    color: #764ba2;
    text-decoration: underline;
}

/* Remember me checkbox styling */
.form-check-input:checked {
    background-color: #667eea;
    border-color: #667eea;
}

.form-check-input:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

/* Demo credentials alert styling */
.alert-info {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    border: 1px solid rgba(102, 126, 234, 0.2);
    border-radius: 8px;
    color: #495057;
}

/* Success message styling */
.alert-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    border: none;
    border-radius: 8px;
}

/* Error message styling */
.alert-danger {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    color: white;
    border: none;
    border-radius: 8px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .login-container {
        padding: 1rem;
    }
    
    .login-card .card-body {
        padding: 2rem 1.5rem;
    }
}

/* Animation for form validation */
.form-control.is-invalid {
    animation: shake 0.5s ease-in-out;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

/* Loading spinner customization */
.login-spinner {
    width: 1rem;
    height: 1rem;
}

/* Icon styling */
.form-label svg {
    color: #667eea;
}

/* User icon styling */
.text-primary svg {
    color: #667eea !important;
}

/* Card hover effect */
.login-card {
    transition: all 0.3s ease;
}

.login-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 40px rgba(31, 38, 135, 0.5);
}

/* Focus states for accessibility */
.login-btn:focus,
.forgot-password-link:focus,
.signup-link:focus {
    outline: 2px solid #667eea;
    outline-offset: 2px;
}

/* Input group styling */
.input-group .form-control {
    border-right: none;
}

.input-group .password-toggle-btn {
    border-left: 2px solid #e9ecef;
}

.input-group .form-control:focus + .password-toggle-btn {
    border-color: #667eea;
}

.input-group .form-control.is-invalid + .password-toggle-btn {
    border-color: #dc3545;
}

/* Welcome back animation */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.login-card {
    animation: fadeInUp 0.6s ease-out;
}

/* Form field focus animation */
.form-control:focus {
    animation: focusPulse 0.3s ease-out;
}

@keyframes focusPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.02); }
    100% { transform: scale(1); }
}
