import React, { useState, useEffect } from 'react';
import {
    Con<PERSON><PERSON>,
    <PERSON>,
    Col,
    Card,
    <PERSON>,
    <PERSON><PERSON>,
    Al<PERSON>,
    Spinner,
    InputGroup
} from 'react-bootstrap';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { Eye, EyeOff, Mail, Lock, User } from 'lucide-react';
import { LoginFormData } from '../../types/user';
import './Login.css';

const Login: React.FC = () => {
    const navigate = useNavigate();
    const location = useLocation();

    const [formData, setFormData] = useState<LoginFormData>({
        email: '',
        password: '',
        rememberMe: false
    });

    const [errors, setErrors] = useState<Partial<LoginFormData>>({});
    const [isLoading, setIsLoading] = useState(false);
    const [showPassword, setShowPassword] = useState(false);
    const [submitError, setSubmitError] = useState<string>('');
    const [successMessage, setSuccessMessage] = useState<string>('');

    // Check for success message from signup
    useEffect(() => {
        if (location.state?.message) {
            setSuccessMessage(location.state.message);
            if (location.state?.email) {
                setFormData(prev => ({ ...prev, email: location.state.email }));
            }
        }
    }, [location.state]);

    // Handle input changes
    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { name, value, type, checked } = e.target;

        setFormData(prev => ({
            ...prev,
            [name]: type === 'checkbox' ? checked : value
        }));

        // Clear error when user starts typing
        if (errors[name as keyof LoginFormData]) {
            setErrors(prev => ({
                ...prev,
                [name]: undefined
            }));
        }
    };

    // Validate form
    const validateForm = (): boolean => {
        const newErrors: Partial<LoginFormData> = {};

        // Email validation
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!formData.email.trim()) {
            newErrors.email = 'Email is required';
        } else if (!emailRegex.test(formData.email)) {
            newErrors.email = 'Please enter a valid email address';
        }

        // Password validation
        if (!formData.password) {
            newErrors.password = 'Password is required';
        } else if (formData.password.length < 6) {
            newErrors.password = 'Password must be at least 6 characters';
        }

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    // Handle form submission
    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        setSubmitError('');
        setSuccessMessage('');

        if (!validateForm()) {
            return;
        }

        setIsLoading(true);

        try {
            // TODO: Replace with actual API call
            // const response = await axios.post('/api/auth/login', {
            //     email: formData.email,
            //     password: formData.password,
            //     rememberMe: formData.rememberMe
            // });

            // Simulate API call
            await new Promise(resolve => setTimeout(resolve, 2000));

            // Simulate successful login
            if (formData.email === '<EMAIL>' && formData.password === 'password123') {
                // Store auth data (in real app, this would come from API response)
                localStorage.setItem('authToken', 'demo-token-123');
                localStorage.setItem('userData', JSON.stringify({
                    id: 1,
                    firstName: 'Demo',
                    lastName: 'User',
                    email: formData.email
                }));

                // Redirect to dashboard or intended page
                const redirectTo = location.state?.from?.pathname || '/dashboard';
                navigate(redirectTo, { replace: true });
            } else {
                throw new Error('Invalid email or password');
            }
        } catch (error: any) {
            setSubmitError(
                error.message ||
                'Invalid email or password. Please try again.'
            );
        } finally {
            setIsLoading(false);
        }
    };

    // Handle forgot password
    const handleForgotPassword = () => {
        // TODO: Implement forgot password functionality
        alert('Forgot password functionality will be implemented soon!');
    };

    return (
        <div className="login-container d-flex align-items-center py-5">
            <Container>
                <Row className="justify-content-center">
                    <Col md={6} lg={5} xl={4}>
                        <Card className="login-card shadow-lg border-0">
                            <Card.Body className="p-5">
                                <div className="text-center mb-4">
                                    <User size={48} className="text-primary mb-3" />
                                    <h1 className="h3 mb-3 fw-bold text-primary">Welcome Back</h1>
                                    <p className="text-muted">Sign in to your EVA Planning account</p>
                                </div>

                                {successMessage && (
                                    <Alert variant="success" className="mb-4">
                                        {successMessage}
                                    </Alert>
                                )}

                                {submitError && (
                                    <Alert variant="danger" className="mb-4">
                                        {submitError}
                                    </Alert>
                                )}

                                <Form onSubmit={handleSubmit} className="login-form">
                                    {/* Email Field */}
                                    <Form.Group className="mb-3">
                                        <Form.Label className="fw-semibold">
                                            <Mail size={16} className="me-2" />
                                            Email Address
                                        </Form.Label>
                                        <Form.Control
                                            type="email"
                                            name="email"
                                            value={formData.email}
                                            onChange={handleInputChange}
                                            isInvalid={!!errors.email}
                                            placeholder="Enter your email address"
                                            autoComplete="email"
                                        />
                                        <Form.Control.Feedback type="invalid">
                                            {errors.email}
                                        </Form.Control.Feedback>
                                    </Form.Group>

                                    {/* Password Field */}
                                    <Form.Group className="mb-3">
                                        <Form.Label className="fw-semibold">
                                            <Lock size={16} className="me-2" />
                                            Password
                                        </Form.Label>
                                        <InputGroup>
                                            <Form.Control
                                                type={showPassword ? "text" : "password"}
                                                name="password"
                                                value={formData.password}
                                                onChange={handleInputChange}
                                                isInvalid={!!errors.password}
                                                placeholder="Enter your password"
                                                autoComplete="current-password"
                                            />
                                            <Button
                                                variant="outline-secondary"
                                                className="password-toggle-btn"
                                                onClick={() => setShowPassword(!showPassword)}
                                                style={{ zIndex: 1 }}
                                            >
                                                {showPassword ? <EyeOff size={16} /> : <Eye size={16} />}
                                            </Button>
                                        </InputGroup>
                                        <Form.Control.Feedback type="invalid">
                                            {errors.password}
                                        </Form.Control.Feedback>
                                    </Form.Group>

                                    {/* Remember Me and Forgot Password */}
                                    <Row className="mb-4">
                                        <Col>
                                            <Form.Check
                                                type="checkbox"
                                                name="rememberMe"
                                                checked={formData.rememberMe}
                                                onChange={handleInputChange}
                                                label="Remember me"
                                                className="text-muted"
                                            />
                                        </Col>
                                        <Col className="text-end">
                                            <Button
                                                variant="link"
                                                className="p-0 forgot-password-link"
                                                onClick={handleForgotPassword}
                                            >
                                                Forgot password?
                                            </Button>
                                        </Col>
                                    </Row>

                                    {/* Submit Button */}
                                    <Button
                                        type="submit"
                                        variant="primary"
                                        size="lg"
                                        className="login-btn w-100 mb-4"
                                        disabled={isLoading}
                                    >
                                        {isLoading ? (
                                            <>
                                                <Spinner
                                                    as="span"
                                                    animation="border"
                                                    size="sm"
                                                    role="status"
                                                    aria-hidden="true"
                                                    className="login-spinner me-2"
                                                />
                                                Signing In...
                                            </>
                                        ) : (
                                            'Sign In'
                                        )}
                                    </Button>

                                    {/* Demo Credentials */}
                                    <Alert variant="info" className="mb-4">
                                        <small>
                                            <strong>Demo Credentials:</strong><br />
                                            Email: <EMAIL><br />
                                            Password: password123
                                        </small>
                                    </Alert>

                                    {/* Signup Link */}
                                    <div className="text-center">
                                        <span className="text-muted">Don't have an account? </span>
                                        <Link to="/signup" className="signup-link">
                                            Create one here
                                        </Link>
                                    </div>
                                </Form>
                            </Card.Body>
                        </Card>
                    </Col>
                </Row>
            </Container>
        </div>
    );
};

export default Login;
