import React, { useState } from 'react';
import { Play, Calendar, Users, CheckCircle, Clock, Star, ArrowRight, Video, Phone, Mail } from 'lucide-react';
import { <PERSON><PERSON> } from '../../components/ui/button';
import { Card } from '../../components/ui/card';
import { Badge } from '../../components/ui/badge';
import { Input } from '../../components/ui/input';
import { Label } from '../../components/ui/label';
import { Textarea } from '../../components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../components/ui/select';
import { Alert, AlertDescription } from '../../components/ui/alert';
import Layout from '../../components/Layout';

const Demo: React.FC = () => {
    const [formData, setFormData] = useState({
        name: '',
        email: '',
        company: '',
        organizationType: 'company',
        teamSize: '1-10',
        phone: '',
        message: ''
    });
    const [showSuccess, setShowSuccess] = useState(false);
    const [isSubmitting, setIsSubmitting] = useState(false);

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        const { name, value } = e.target;
        setFormData(prev => ({ ...prev, [name]: value }));
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        setIsSubmitting(true);

        // Simulate form submission
        setTimeout(() => {
            setShowSuccess(true);
            setIsSubmitting(false);
            setFormData({
                name: '',
                email: '',
                company: '',
                organizationType: 'company',
                teamSize: '1-10',
                phone: '',
                message: ''
            });

            // Hide success message after 5 seconds
            setTimeout(() => setShowSuccess(false), 5000);
        }, 1000);
    };

    const demoFeatures = [
        {
            icon: <Calendar size={24} />,
            title: 'Interactive Calendar',
            description: 'See how easy it is to create, edit, and manage events across multiple departments.'
        },
        {
            icon: <Users size={24} />,
            title: 'Team Collaboration',
            description: 'Experience real-time collaboration features and approval workflows in action.'
        },
        {
            icon: <CheckCircle size={24} />,
            title: 'Conflict Detection',
            description: 'Watch our intelligent conflict detection system prevent scheduling overlaps.'
        },
        {
            icon: <Clock size={24} />,
            title: 'Real-time Updates',
            description: 'See how changes sync instantly across all team members and departments.'
        }
    ];

    const organizationTypes = [
        { value: 'company', label: 'Company/Business' },
        { value: 'religious', label: 'Religious Organization' },
        { value: 'charity', label: 'Charity/Non-profit' },
        { value: 'team', label: 'Team/Group' },
        { value: 'other', label: 'Other' }
    ];

    const teamSizes = [
        { value: '1-10', label: '1-10 people' },
        { value: '11-50', label: '11-50 people' },
        { value: '51-200', label: '51-200 people' },
        { value: '201-1000', label: '201-1000 people' },
        { value: '1000+', label: '1000+ people' }
    ];

    return (
        <Layout>
            <div className="py-12">
                <div className="wk-container">
                    {/* Header */}
                    <div className="text-center mb-12 max-w-4xl mx-auto">
                        <div className="mb-6">
                            <Play size={48} className="text-brand-blue mx-auto" />
                        </div>
                        <h1 className="text-4xl font-bold mb-4 text-brand-blue">
                            See WithKalenda in Action
                        </h1>
                        <p className="text-xl text-gray-600 mb-8">
                            Experience the power of collaborative event planning with a personalized demo
                            tailored to your organization's needs.
                        </p>
                        <div className="flex justify-center gap-4 mb-8 flex-wrap">
                            <Badge variant="success" className="px-4 py-2">
                                <Star size={16} className="mr-1" />
                                15-Minute Demo
                            </Badge>
                            <Badge variant="info" className="px-4 py-2">
                                <Video size={16} className="mr-1" />
                                Live or Recorded
                            </Badge>
                            <Badge variant="warning" className="px-4 py-2">
                                <CheckCircle size={16} className="mr-1" />
                                No Commitment
                            </Badge>
                        </div>
                    </div>

                    {/* Demo Options */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
                        <Card className="h-full">
                            <div className="p-6 text-center">
                                <div className="mb-4">
                                    <Video size={48} className="text-brand-blue mx-auto" />
                                </div>
                                <h3 className="text-xl font-bold mb-3">Live Demo</h3>
                                <p className="text-gray-600 mb-6">
                                    Schedule a personalized 15-minute demo with our team. We'll show you how
                                    WithKalenda can solve your specific event coordination challenges.
                                </p>
                                <ul className="text-left mb-6 space-y-2">
                                    <li className="flex items-center">
                                        <CheckCircle size={16} className="text-green-500 mr-2" />
                                        Personalized to your use case
                                    </li>
                                    <li className="flex items-center">
                                        <CheckCircle size={16} className="text-green-500 mr-2" />
                                        Q&A with product experts
                                    </li>
                                    <li className="flex items-center">
                                        <CheckCircle size={16} className="text-green-500 mr-2" />
                                        Custom setup recommendations
                                    </li>
                                    <li className="flex items-center">
                                        <CheckCircle size={16} className="text-green-500 mr-2" />
                                        Pricing discussion
                                    </li>
                                </ul>
                                <Button
                                    size="lg"
                                    className="w-full"
                                    variant="brand-orange"
                                >
                                    Schedule Live Demo
                                </Button>
                            </div>
                        </Card>
                        <Card className="h-full">
                            <div className="p-6 text-center">
                                <div className="mb-4">
                                    <Play size={48} className="text-brand-blue mx-auto" />
                                </div>
                                <h3 className="text-xl font-bold mb-3">Interactive Demo</h3>
                                <p className="text-gray-600 mb-6">
                                    Try WithKalenda yourself with our interactive demo environment.
                                    Explore features at your own pace with sample data.
                                </p>
                                <ul className="text-left mb-6 space-y-2">
                                    <li className="flex items-center">
                                        <CheckCircle size={16} className="text-green-500 mr-2" />
                                        Hands-on experience
                                    </li>
                                    <li className="flex items-center">
                                        <CheckCircle size={16} className="text-green-500 mr-2" />
                                        Available 24/7
                                    </li>
                                    <li className="flex items-center">
                                        <CheckCircle size={16} className="text-green-500 mr-2" />
                                        No installation required
                                    </li>
                                    <li className="flex items-center">
                                        <CheckCircle size={16} className="text-green-500 mr-2" />
                                        Sample scenarios included
                                    </li>
                                </ul>
                                <Button
                                    variant="outline"
                                    size="lg"
                                    className="w-full"
                                >
                                    Try Interactive Demo
                                </Button>
                            </div>
                        </Card>
                    </div>

                    {/* What You'll See */}
                    <div className="mb-12">
                        <h2 className="text-center text-2xl font-bold mb-8 text-brand-blue">
                            What You'll See in the Demo
                        </h2>
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                            {demoFeatures.map((feature, index) => (
                                <Card key={index} className="text-center">
                                    <div className="p-6">
                                        <div className="mb-4 text-brand-blue flex justify-center">{feature.icon}</div>
                                        <h5 className="font-bold mb-2">{feature.title}</h5>
                                        <p className="text-gray-600 text-sm">{feature.description}</p>
                                    </div>
                                </Card>
                            ))}
                        </div>
                    </div>

                    {/* Demo Request Form */}
                    <Row className="mb-5">
                        <Col lg={8} className="mx-auto">
                            <Card className="border-0 shadow">
                                <Card.Body className="p-5">
                                    <h2 className="fw-bold mb-4 text-center" style={{ color: '#3174ad' }}>
                                        Request Your Demo
                                    </h2>

                                    {showSuccess && (
                                        <Alert variant="success" className="mb-4">
                                            <strong>Thank you!</strong> Your demo request has been submitted.
                                            We'll contact you within 24 hours to schedule your personalized demo.
                                        </Alert>
                                    )}

                                    <Form onSubmit={handleSubmit}>
                                        <Row>
                                            <Col md={6}>
                                                <Form.Group className="mb-3">
                                                    <Form.Label>Full Name *</Form.Label>
                                                    <Form.Control
                                                        type="text"
                                                        name="name"
                                                        value={formData.name}
                                                        onChange={handleInputChange}
                                                        required
                                                        placeholder="Your full name"
                                                    />
                                                </Form.Group>
                                            </Col>
                                            <Col md={6}>
                                                <Form.Group className="mb-3">
                                                    <Form.Label>Work Email *</Form.Label>
                                                    <Form.Control
                                                        type="email"
                                                        name="email"
                                                        value={formData.email}
                                                        onChange={handleInputChange}
                                                        required
                                                        placeholder="<EMAIL>"
                                                    />
                                                </Form.Group>
                                            </Col>
                                        </Row>

                                        <Row>
                                            <Col md={6}>
                                                <Form.Group className="mb-3">
                                                    <Form.Label>Organization Name *</Form.Label>
                                                    <Form.Control
                                                        type="text"
                                                        name="company"
                                                        value={formData.company}
                                                        onChange={handleInputChange}
                                                        required
                                                        placeholder="Your organization name"
                                                    />
                                                </Form.Group>
                                            </Col>
                                            <Col md={6}>
                                                <Form.Group className="mb-3">
                                                    <Form.Label>Phone Number</Form.Label>
                                                    <Form.Control
                                                        type="tel"
                                                        name="phone"
                                                        value={formData.phone}
                                                        onChange={handleInputChange}
                                                        placeholder="+****************"
                                                    />
                                                </Form.Group>
                                            </Col>
                                        </Row>

                                        <Row>
                                            <Col md={6}>
                                                <Form.Group className="mb-3">
                                                    <Form.Label>Organization Type</Form.Label>
                                                    <Form.Select
                                                        name="organizationType"
                                                        value={formData.organizationType}
                                                        onChange={handleInputChange}
                                                    >
                                                        {organizationTypes.map(type => (
                                                            <option key={type.value} value={type.value}>
                                                                {type.label}
                                                            </option>
                                                        ))}
                                                    </Form.Select>
                                                </Form.Group>
                                            </Col>
                                            <Col md={6}>
                                                <Form.Group className="mb-3">
                                                    <Form.Label>Team Size</Form.Label>
                                                    <Form.Select
                                                        name="teamSize"
                                                        value={formData.teamSize}
                                                        onChange={handleInputChange}
                                                    >
                                                        {teamSizes.map(size => (
                                                            <option key={size.value} value={size.value}>
                                                                {size.label}
                                                            </option>
                                                        ))}
                                                    </Form.Select>
                                                </Form.Group>
                                            </Col>
                                        </Row>

                                        <Form.Group className="mb-4">
                                            <Form.Label>Tell us about your event coordination challenges</Form.Label>
                                            <Form.Control
                                                as="textarea"
                                                rows={3}
                                                name="message"
                                                value={formData.message}
                                                onChange={handleInputChange}
                                                placeholder="What specific challenges are you facing with event planning and coordination?"
                                            />
                                        </Form.Group>

                                        <div className="text-center">
                                            <Button
                                                type="submit"
                                                size="lg"
                                                disabled={isSubmitting}
                                                className="px-5"
                                                style={{ backgroundColor: '#ff6b35', borderColor: '#ff6b35' }}
                                            >
                                                {isSubmitting ? 'Submitting...' : 'Request Demo'}
                                                <ArrowRight size={20} className="ms-2" />
                                            </Button>
                                        </div>
                                    </Form>
                                </Card.Body>
                            </Card>
                        </Col>
                    </Row>

                    {/* Contact Options */}
                    <Row className="text-center">
                        <Col>
                            <div className="p-4 rounded-4" style={{ backgroundColor: 'rgba(49, 116, 173, 0.05)' }}>
                                <h4 className="fw-bold mb-3" style={{ color: '#3174ad' }}>
                                    Prefer to Talk Directly?
                                </h4>
                                <p className="text-muted mb-4">
                                    Our team is ready to answer your questions and show you how WithKalenda can transform your event coordination.
                                </p>
                                <div className="d-flex justify-content-center gap-3">
                                    <Button variant="outline-primary">
                                        <Phone size={16} className="me-2" />
                                        Call Us: +1 (555) 123-KALE
                                    </Button>
                                    <Button variant="outline-primary">
                                        <Mail size={16} className="me-2" />
                                        Email: <EMAIL>
                                    </Button>
                                </div>
                            </div>
                        </Col>
                    </Row>
                </Container>
        </Layout>
    );
};

export default Demo;
