import React, { useState } from 'react';
import { Play, Calendar, Users, CheckCircle, Clock, Star, ArrowRight, Video, Phone, Mail } from '../../components/ui/icons';
import { Button } from '../../components/ui/button';
import { Card } from '../../components/ui/card';
import { Badge } from '../../components/ui/badge';
import { Input } from '../../components/ui/input';
import { Label } from '../../components/ui/label';
import { Textarea } from '../../components/ui/textarea';
import { Alert, AlertDescription } from '../../components/ui/alert';
import Layout from '../../components/Layout';

const Demo: React.FC = () => {
    const [formData, setFormData] = useState({
        name: '',
        email: '',
        company: '',
        organizationType: 'company',
        teamSize: '1-10',
        phone: '',
        message: ''
    });
    const [showSuccess, setShowSuccess] = useState(false);
    const [isSubmitting, setIsSubmitting] = useState(false);

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        const { name, value } = e.target;
        setFormData(prev => ({ ...prev, [name]: value }));
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        setIsSubmitting(true);

        // Simulate form submission
        setTimeout(() => {
            setShowSuccess(true);
            setIsSubmitting(false);
            setFormData({
                name: '',
                email: '',
                company: '',
                organizationType: 'company',
                teamSize: '1-10',
                phone: '',
                message: ''
            });

            // Hide success message after 5 seconds
            setTimeout(() => setShowSuccess(false), 5000);
        }, 1000);
    };

    const demoFeatures = [
        {
            icon: <Calendar size={24} />,
            title: 'Interactive Calendar',
            description: 'See how easy it is to create, edit, and manage events across multiple departments.'
        },
        {
            icon: <Users size={24} />,
            title: 'Team Collaboration',
            description: 'Experience real-time collaboration features and approval workflows in action.'
        },
        {
            icon: <CheckCircle size={24} />,
            title: 'Conflict Detection',
            description: 'Watch our intelligent conflict detection system prevent scheduling overlaps.'
        },
        {
            icon: <Clock size={24} />,
            title: 'Real-time Updates',
            description: 'See how changes sync instantly across all team members and departments.'
        }
    ];



    return (
        <Layout>
            <div className="py-12">
                <div className="wk-container">
                    {/* Header */}
                    <div className="text-center mb-12 max-w-4xl mx-auto">
                        <div className="mb-6">
                            <Play size={48} className="text-brand-blue mx-auto" />
                        </div>
                        <h1 className="text-4xl font-bold mb-4 text-brand-blue">
                            See WithKalenda in Action
                        </h1>
                        <p className="text-xl text-gray-600 mb-8">
                            Experience the power of collaborative event planning with a personalized demo
                            tailored to your organization's needs.
                        </p>
                        <div className="flex justify-center gap-4 mb-8 flex-wrap">
                            <Badge variant="success" className="px-4 py-2">
                                <Star size={16} className="mr-1" />
                                15-Minute Demo
                            </Badge>
                            <Badge variant="info" className="px-4 py-2">
                                <Video size={16} className="mr-1" />
                                Live or Recorded
                            </Badge>
                            <Badge variant="warning" className="px-4 py-2">
                                <CheckCircle size={16} className="mr-1" />
                                No Commitment
                            </Badge>
                        </div>
                    </div>

                    {/* Demo Options */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
                        <Card className="h-full">
                            <div className="p-6 text-center">
                                <div className="mb-4">
                                    <Video size={48} className="text-brand-blue mx-auto" />
                                </div>
                                <h3 className="text-xl font-bold mb-3">Live Demo</h3>
                                <p className="text-gray-600 mb-6">
                                    Schedule a personalized 15-minute demo with our team. We'll show you how
                                    WithKalenda can solve your specific event coordination challenges.
                                </p>
                                <ul className="text-left mb-6 space-y-2">
                                    <li className="flex items-center">
                                        <CheckCircle size={16} className="text-green-500 mr-2" />
                                        Personalized to your use case
                                    </li>
                                    <li className="flex items-center">
                                        <CheckCircle size={16} className="text-green-500 mr-2" />
                                        Q&A with product experts
                                    </li>
                                    <li className="flex items-center">
                                        <CheckCircle size={16} className="text-green-500 mr-2" />
                                        Custom setup recommendations
                                    </li>
                                    <li className="flex items-center">
                                        <CheckCircle size={16} className="text-green-500 mr-2" />
                                        Pricing discussion
                                    </li>
                                </ul>
                                <Button
                                    size="lg"
                                    className="w-full"
                                    variant="brand-orange"
                                >
                                    Schedule Live Demo
                                </Button>
                            </div>
                        </Card>
                        <Card className="h-full">
                            <div className="p-6 text-center">
                                <div className="mb-4">
                                    <Play size={48} className="text-brand-blue mx-auto" />
                                </div>
                                <h3 className="text-xl font-bold mb-3">Interactive Demo</h3>
                                <p className="text-gray-600 mb-6">
                                    Try WithKalenda yourself with our interactive demo environment.
                                    Explore features at your own pace with sample data.
                                </p>
                                <ul className="text-left mb-6 space-y-2">
                                    <li className="flex items-center">
                                        <CheckCircle size={16} className="text-green-500 mr-2" />
                                        Hands-on experience
                                    </li>
                                    <li className="flex items-center">
                                        <CheckCircle size={16} className="text-green-500 mr-2" />
                                        Available 24/7
                                    </li>
                                    <li className="flex items-center">
                                        <CheckCircle size={16} className="text-green-500 mr-2" />
                                        No installation required
                                    </li>
                                    <li className="flex items-center">
                                        <CheckCircle size={16} className="text-green-500 mr-2" />
                                        Sample scenarios included
                                    </li>
                                </ul>
                                <Button
                                    variant="outline"
                                    size="lg"
                                    className="w-full"
                                >
                                    Try Interactive Demo
                                </Button>
                            </div>
                        </Card>
                    </div>

                    {/* What You'll See */}
                    <div className="mb-12">
                        <h2 className="text-center text-2xl font-bold mb-8 text-brand-blue">
                            What You'll See in the Demo
                        </h2>
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                            {demoFeatures.map((feature, index) => (
                                <Card key={index} className="text-center">
                                    <div className="p-6">
                                        <div className="mb-4 text-brand-blue flex justify-center">{feature.icon}</div>
                                        <h5 className="font-bold mb-2">{feature.title}</h5>
                                        <p className="text-gray-600 text-sm">{feature.description}</p>
                                    </div>
                                </Card>
                            ))}
                        </div>
                    </div>

                    {/* Demo Request Form */}
                    <div className="mb-12">
                        <Card className="max-w-4xl mx-auto">
                            <div className="p-8">
                                <h2 className="text-2xl font-bold mb-6 text-center text-brand-blue">
                                    Request Your Demo
                                </h2>

                                {showSuccess && (
                                    <Alert className="mb-6">
                                        <AlertDescription>
                                            <strong>Thank you!</strong> Your demo request has been submitted.
                                            We'll contact you within 24 hours to schedule your personalized demo.
                                        </AlertDescription>
                                    </Alert>
                                )}

                                <form onSubmit={handleSubmit} className="space-y-6">
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                        <div>
                                            <Label htmlFor="name">Full Name *</Label>
                                            <Input
                                                id="name"
                                                type="text"
                                                name="name"
                                                value={formData.name}
                                                onChange={handleInputChange}
                                                required
                                                placeholder="Your full name"
                                                className="mt-1"
                                            />
                                        </div>
                                        <div>
                                            <Label htmlFor="email">Work Email *</Label>
                                            <Input
                                                id="email"
                                                type="email"
                                                name="email"
                                                value={formData.email}
                                                onChange={handleInputChange}
                                                required
                                                placeholder="<EMAIL>"
                                                className="mt-1"
                                            />
                                        </div>
                                    </div>

                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                        <div>
                                            <Label htmlFor="company">Organization Name *</Label>
                                            <Input
                                                id="company"
                                                type="text"
                                                name="company"
                                                value={formData.company}
                                                onChange={handleInputChange}
                                                required
                                                placeholder="Your organization name"
                                                className="mt-1"
                                            />
                                        </div>
                                        <div>
                                            <Label htmlFor="phone">Phone Number</Label>
                                            <Input
                                                id="phone"
                                                type="tel"
                                                name="phone"
                                                value={formData.phone}
                                                onChange={handleInputChange}
                                                placeholder="+****************"
                                                className="mt-1"
                                            />
                                        </div>
                                    </div>

                                    <div>
                                        <Label htmlFor="message">Tell us about your event coordination challenges</Label>
                                        <Textarea
                                            id="message"
                                            name="message"
                                            value={formData.message}
                                            onChange={handleInputChange}
                                            placeholder="What specific challenges are you facing with event planning and coordination?"
                                            rows={3}
                                            className="mt-1"
                                        />
                                    </div>

                                    <div className="text-center">
                                        <Button
                                            type="submit"
                                            size="lg"
                                            disabled={isSubmitting}
                                            variant="brand-orange"
                                            className="px-8"
                                        >
                                            {isSubmitting ? 'Submitting...' : 'Request Demo'}
                                            <ArrowRight size={20} className="ml-2" />
                                        </Button>
                                    </div>
                                </form>
                            </div>
                        </Card>
                    </div>

                    {/* Contact Options */}
                    <div className="text-center">
                        <div className="wk-card-accent p-6 rounded-lg">
                            <h4 className="text-xl font-bold mb-3 text-brand-blue">
                                Prefer to Talk Directly?
                            </h4>
                            <p className="text-gray-600 mb-6">
                                Our team is ready to answer your questions and show you how WithKalenda can transform your event coordination.
                            </p>
                            <div className="flex justify-center gap-4 flex-wrap">
                                <Button variant="outline">
                                    <Phone size={16} className="mr-2" />
                                    Call Us: +1 (555) 123-KALE
                                </Button>
                                <Button variant="outline">
                                    <Mail size={16} className="mr-2" />
                                    Email: <EMAIL>
                                </Button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </Layout>
    );
};

export default Demo;
