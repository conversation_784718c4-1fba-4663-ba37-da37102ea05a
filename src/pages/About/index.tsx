import React from 'react';
import { Users, Heart, Globe, Zap } from '../../components/ui/icons';
import { Card } from '../../components/ui/card';
import { But<PERSON> } from '../../components/ui/button';
import Layout from '../../components/Layout';

const About = () => {
    const teamMembers = [
        {
            name: '<PERSON>',
            title: 'CEO & Co-Founder',
            bio: 'Former event coordinator with 15+ years of experience working with Fortune 500 companies.',
            image: 'https://randomuser.me/api/portraits/women/32.jpg'
        },
        {
            name: '<PERSON>',
            title: 'CTO & Co-Founder',
            bio: 'Software engineer with a passion for creating tools that bring communities together.',
            image: 'https://randomuser.me/api/portraits/men/22.jpg'
        },
        {
            name: '<PERSON><PERSON>',
            title: 'Head of Customer Success',
            bio: 'Dedicated to helping organizations streamline their event planning processes.',
            image: 'https://randomuser.me/api/portraits/women/44.jpg'
        },
        {
            name: '<PERSON>',
            title: 'Lead Developer',
            bio: 'Full-stack developer focused on creating intuitive and responsive user experiences.',
            image: 'https://randomuser.me/api/portraits/men/46.jpg'
        }
    ];

    const values = [
        {
            icon: <Users size={32} />,
            title: 'Community First',
            description: 'We believe in the power of bringing people together through well-organized events.'
        },
        {
            icon: <Heart size={32} />,
            title: 'Passion for Excellence',
            description: 'We\'re committed to creating the best possible tools for event coordination.'
        },
        {
            icon: <Globe size={32} />,
            title: 'Inclusive Design',
            description: 'Our platform is built to serve diverse organizations with varying needs and resources.'
        },
        {
            icon: <Zap size={32} />,
            title: 'Continuous Innovation',
            description: 'We constantly improve our platform based on user feedback and emerging technologies.'
        }
    ];

    return (
        <Layout>
            <div className="py-12">
                <div className="wk-container">
                    {/* Hero Section */}
                    <div className="text-center mb-12 max-w-4xl mx-auto">
                        <h1 className="text-4xl font-bold mb-4 text-brand-blue">Our Mission</h1>
                        <p className="text-xl text-gray-600 mb-8">
                            At WithKalenda, we're on a mission to simplify event coordination for teams, companies,
                            religious organizations, charities, and any group that brings people together.
                        </p>
                        <div className="flex justify-center">
                            <Button
                                size="lg"
                                variant="brand-orange"
                            >
                                Join Our Community
                            </Button>
                        </div>
                    </div>

                    {/* Our Story Section */}
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-12 items-center">
                        <div className="relative">
                            <div className="absolute inset-0 bg-brand-blue/10 rounded-lg transform translate-x-4 translate-y-4 -z-10"></div>
                            <img
                                src="https://images.unsplash.com/photo-1517245386807-bb43f82c33c4?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1740&q=80"
                                alt="Team collaboration"
                                className="w-full rounded-lg shadow-lg object-cover"
                            />
                        </div>
                        <div>
                            <h2 className="text-2xl font-bold mb-4 text-brand-blue">Our Story</h2>
                            <p className="mb-4 text-gray-600">
                                WithKalenda was born from a simple observation: coordinating events across teams and departments
                                was unnecessarily complicated. Our founders, Sarah and Michael, experienced this firsthand while
                                working at organizations where miscommunication and scheduling conflicts were common.
                            </p>
                            <p className="mb-4 text-gray-600">
                                In 2020, they set out to create a solution that would make event planning seamless, collaborative,
                                and conflict-free. What started as a simple calendar tool has evolved into a comprehensive platform
                                serving thousands of organizations worldwide.
                            </p>
                            <p className="text-gray-600">
                                Today, WithKalenda helps teams of all sizes coordinate events, from small community gatherings to
                                large corporate conferences, with the same core mission: bringing people together through better
                                organization.
                            </p>
                        </div>
                    </div>

                    {/* Our Values Section */}
                    <div className="text-center mb-8">
                        <h2 className="text-2xl font-bold text-brand-blue">Our Values</h2>
                        <p className="text-xl text-gray-600">The principles that guide everything we do</p>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
                        {values.map((value, index) => (
                            <Card key={index} className="text-center">
                                <div className="p-6">
                                    <div className="mb-4 text-brand-blue flex justify-center">{value.icon}</div>
                                    <h4 className="font-bold mb-2">{value.title}</h4>
                                    <p className="text-gray-600 text-sm">{value.description}</p>
                                </div>
                            </Card>
                        ))}
                    </div>

                    {/* Team Section */}
                    <div className="text-center mb-8">
                        <h2 className="text-2xl font-bold text-brand-blue">Meet Our Team</h2>
                        <p className="text-xl text-gray-600">The passionate people behind WithKalenda</p>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
                        {teamMembers.map((member, index) => (
                            <Card key={index} className="text-center">
                                <div className="p-6">
                                    <div className="text-center mb-4">
                                        <img
                                            src={member.image}
                                            alt={member.name}
                                            className="w-30 h-30 rounded-full mx-auto mb-3 border-4 border-brand-blue object-cover"
                                        />
                                    </div>
                                    <h4 className="font-bold mb-1">{member.name}</h4>
                                    <p className="text-brand-blue mb-2">{member.title}</p>
                                    <p className="text-gray-600 text-sm">{member.bio}</p>
                                </div>
                            </Card>
                        ))}
                    </div>

                    {/* CTA Section */}
                    <div className="text-center">
                        <div className="wk-card-accent p-8 rounded-lg">
                            <h3 className="text-2xl font-bold mb-4 text-brand-blue">
                                Join the WithKalenda Community
                            </h3>
                            <p className="text-gray-600 mb-6">
                                Experience the difference our platform can make for your organization's event coordination.
                            </p>
                            <Button
                                size="lg"
                                variant="brand-orange"
                            >
                                Start Your Free Trial
                            </Button>
                        </div>
                    </div>
                </div>
            </div>
        </Layout>
    );
};

export default About;
