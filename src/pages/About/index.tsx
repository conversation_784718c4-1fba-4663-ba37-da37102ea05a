import React from 'react';
import { Con<PERSON>er, <PERSON>, Col, Card, Button, Image } from 'react-bootstrap';
import { Users, Calendar, Heart, Globe, Award, Zap } from 'lucide-react';

const About = () => {
    const teamMembers = [
        {
            name: '<PERSON>',
            title: 'CEO & Co-Founder',
            bio: 'Former event coordinator with 15+ years of experience working with Fortune 500 companies.',
            image: 'https://randomuser.me/api/portraits/women/32.jpg'
        },
        {
            name: '<PERSON>',
            title: 'CT<PERSON> & Co-Founder',
            bio: 'Software engineer with a passion for creating tools that bring communities together.',
            image: 'https://randomuser.me/api/portraits/men/22.jpg'
        },
        {
            name: '<PERSON><PERSON>',
            title: 'Head of Customer Success',
            bio: 'Dedicated to helping organizations streamline their event planning processes.',
            image: 'https://randomuser.me/api/portraits/women/44.jpg'
        },
        {
            name: '<PERSON>',
            title: 'Lead Developer',
            bio: 'Full-stack developer focused on creating intuitive and responsive user experiences.',
            image: 'https://randomuser.me/api/portraits/men/46.jpg'
        }
    ];

    const values = [
        {
            icon: <Users size={32} />,
            title: 'Community First',
            description: 'We believe in the power of bringing people together through well-organized events.'
        },
        {
            icon: <Heart size={32} />,
            title: 'Passion for Excellence',
            description: 'We\'re committed to creating the best possible tools for event coordination.'
        },
        {
            icon: <Globe size={32} />,
            title: 'Inclusive Design',
            description: 'Our platform is built to serve diverse organizations with varying needs and resources.'
        },
        {
            icon: <Zap size={32} />,
            title: 'Continuous Innovation',
            description: 'We constantly improve our platform based on user feedback and emerging technologies.'
        }
    ];

    return (
        <Container className="py-5">
            {/* Hero Section */}
            <Row className="mb-5 text-center">
                <Col lg={8} className="mx-auto">
                    <h1 className="display-4 fw-bold mb-3" style={{ color: '#3174ad' }}>Our Mission</h1>
                    <p className="lead mb-4">
                        At WithKalenda, we're on a mission to simplify event coordination for teams, companies,
                        religious organizations, charities, and any group that brings people together.
                    </p>
                    <div className="d-flex justify-content-center">
                        <Button
                            size="lg"
                            className="px-4 py-2"
                            style={{ backgroundColor: '#ff6b35', borderColor: '#ff6b35' }}
                        >
                            Join Our Community
                        </Button>
                    </div>
                </Col>
            </Row>

            {/* Our Story Section */}
            <Row className="mb-5 align-items-center">
                <Col lg={6} className="mb-4 mb-lg-0">
                    <div className="position-relative">
                        <div
                            className="position-absolute"
                            style={{
                                width: '80%',
                                height: '80%',
                                backgroundColor: 'rgba(49, 116, 173, 0.1)',
                                top: '10%',
                                left: '10%',
                                zIndex: -1,
                                borderRadius: '12px'
                            }}
                        ></div>
                        <img
                            src="https://images.unsplash.com/photo-1517245386807-bb43f82c33c4?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1740&q=80"
                            alt="Team collaboration"
                            className="img-fluid rounded-4 shadow"
                            style={{ width: '100%', objectFit: 'cover' }}
                        />
                    </div>
                </Col>
                <Col lg={6}>
                    <h2 className="fw-bold mb-3" style={{ color: '#3174ad' }}>Our Story</h2>
                    <p className="mb-3">
                        WithKalenda was born from a simple observation: coordinating events across teams and departments
                        was unnecessarily complicated. Our founders, Sarah and Michael, experienced this firsthand while
                        working at organizations where miscommunication and scheduling conflicts were common.
                    </p>
                    <p className="mb-3">
                        In 2020, they set out to create a solution that would make event planning seamless, collaborative,
                        and conflict-free. What started as a simple calendar tool has evolved into a comprehensive platform
                        serving thousands of organizations worldwide.
                    </p>
                    <p>
                        Today, WithKalenda helps teams of all sizes coordinate events, from small community gatherings to
                        large corporate conferences, with the same core mission: bringing people together through better
                        organization.
                    </p>
                </Col>
            </Row>

            {/* Our Values Section */}
            <Row className="mb-5">
                <Col className="text-center mb-4">
                    <h2 className="fw-bold" style={{ color: '#3174ad' }}>Our Values</h2>
                    <p className="lead">The principles that guide everything we do</p>
                </Col>
            </Row>
            <Row className="mb-5 g-4">
                {values.map((value, index) => (
                    <Col md={6} lg={3} key={index}>
                        <Card className="h-100 border-0 shadow-sm">
                            <Card.Body className="text-center p-4">
                                <div className="mb-3 text-primary">{value.icon}</div>
                                <h4 className="fw-bold mb-2">{value.title}</h4>
                                <p className="text-muted mb-0">{value.description}</p>
                            </Card.Body>
                        </Card>
                    </Col>
                ))}
            </Row>

            {/* Team Section */}
            <Row className="mb-5">
                <Col className="text-center mb-4">
                    <h2 className="fw-bold" style={{ color: '#3174ad' }}>Meet Our Team</h2>
                    <p className="lead">The passionate people behind WithKalenda</p>
                </Col>
            </Row>
            <Row className="mb-5 g-4">
                {teamMembers.map((member, index) => (
                    <Col md={6} lg={3} key={index}>
                        <Card className="h-100 border-0 shadow-sm">
                            <div className="text-center pt-4">
                                <Image
                                    src={member.image}
                                    roundedCircle
                                    width={120}
                                    height={120}
                                    className="mb-3 border border-3"
                                    style={{ borderColor: '#3174ad' }}
                                />
                            </div>
                            <Card.Body className="text-center">
                                <h4 className="fw-bold mb-1">{member.name}</h4>
                                <p className="text-primary mb-2">{member.title}</p>
                                <p className="text-muted mb-0">{member.bio}</p>
                            </Card.Body>
                        </Card>
                    </Col>
                ))}
            </Row>

            {/* CTA Section */}
            <Row className="text-center">
                <Col>
                    <div className="p-5 rounded-4" style={{ backgroundColor: 'rgba(49, 116, 173, 0.05)' }}>
                        <h3 className="fw-bold mb-3" style={{ color: '#3174ad' }}>
                            Join the WithKalenda Community
                        </h3>
                        <p className="text-muted mb-4">
                            Experience the difference our platform can make for your organization's event coordination.
                        </p>
                        <Button
                            size="lg"
                            className="px-4 py-2"
                            style={{ backgroundColor: '#ff6b35', borderColor: '#ff6b35' }}
                        >
                            Start Your Free Trial
                        </Button>
                    </div>
                </Col>
            </Row>
        </Container>
    );
};

export default About;
