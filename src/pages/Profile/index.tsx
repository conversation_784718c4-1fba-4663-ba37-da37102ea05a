import React, { useState } from 'react';
import { Con<PERSON><PERSON>, <PERSON>, Col, Card, <PERSON>, <PERSON>ton, Al<PERSON>, Badge, Tab, Nav } from 'react-bootstrap';
import { User, Settings, Shield, Building, Mail, Phone, MapPin, Calendar } from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';
import { useDepartments } from '../../hooks/useDepartments';
import { UserRole } from '../../types/user';
import { formatDate } from '../../utils/dateUtils';
import Layout from '../../components/Layout';

interface ProfileFormData {
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  organization?: string;
  departmentId?: string;
  bio?: string;
  location?: string;
  timezone?: string;
}

const Profile: React.FC = () => {
  const { user, permissions } = useAuth();
  const { departments } = useDepartments();
  
  const [activeTab, setActiveTab] = useState('profile');
  const [isEditing, setIsEditing] = useState(false);
  const [isSaving, setSaving] = useState(false);
  const [saveMessage, setSaveMessage] = useState<string>('');
  const [saveError, setSaveError] = useState<string>('');

  const [formData, setFormData] = useState<ProfileFormData>({
    firstName: user?.firstName || '',
    lastName: user?.lastName || '',
    email: user?.email || '',
    phone: '',
    organization: user?.organization || '',
    departmentId: user?.departmentId || '',
    bio: '',
    location: '',
    timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
  });

  const [passwordData, setPasswordData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });

  const handleProfileSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSaving(true);
    setSaveError('');
    setSaveMessage('');

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setSaveMessage('Profile updated successfully!');
      setIsEditing(false);
    } catch (error) {
      setSaveError('Failed to update profile. Please try again.');
    } finally {
      setSaving(false);
    }
  };

  const handlePasswordSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (passwordData.newPassword !== passwordData.confirmPassword) {
      setSaveError('New passwords do not match.');
      return;
    }

    if (passwordData.newPassword.length < 8) {
      setSaveError('Password must be at least 8 characters long.');
      return;
    }

    setSaving(true);
    setSaveError('');
    setSaveMessage('');

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setSaveMessage('Password updated successfully!');
      setPasswordData({ currentPassword: '', newPassword: '', confirmPassword: '' });
    } catch (error) {
      setSaveError('Failed to update password. Please try again.');
    } finally {
      setSaving(false);
    }
  };

  const getRoleBadge = (role: UserRole) => {
    const variants = {
      [UserRole.ADMIN]: 'danger',
      [UserRole.TEAM_LEADER]: 'warning',
      [UserRole.EMPLOYEE]: 'primary'
    };
    return <Badge bg={variants[role]}>{role.replace('_', ' ').toUpperCase()}</Badge>;
  };

  const userDepartment = departments.find(dept => dept.id === user?.departmentId);

  return (
    <Layout>
      <Container className="py-4">
        <Row>
          <Col lg={4}>
            {/* Profile Summary Card */}
            <Card className="mb-4">
              <Card.Body className="text-center">
                <div className="mb-3">
                  <div 
                    className="rounded-circle bg-primary d-inline-flex align-items-center justify-content-center"
                    style={{ width: '80px', height: '80px' }}
                  >
                    <User size={40} className="text-white" />
                  </div>
                </div>
                <h4 className="mb-1">{user?.firstName} {user?.lastName}</h4>
                <p className="text-muted mb-2">{user?.email}</p>
                <div className="mb-3">
                  {user?.role && getRoleBadge(user.role)}
                </div>
                {userDepartment && (
                  <div className="d-flex align-items-center justify-content-center text-muted">
                    <Building size={16} className="me-2" />
                    {userDepartment.name}
                  </div>
                )}
                {user?.createdAt && (
                  <div className="d-flex align-items-center justify-content-center text-muted mt-2">
                    <Calendar size={16} className="me-2" />
                    Member since {formatDate(user.createdAt)}
                  </div>
                )}
              </Card.Body>
            </Card>

            {/* Quick Stats */}
            <Card>
              <Card.Header>
                <h6 className="mb-0">Account Status</h6>
              </Card.Header>
              <Card.Body>
                <div className="d-flex justify-content-between align-items-center mb-2">
                  <span>Account Status</span>
                  <Badge bg={user?.isActive ? 'success' : 'danger'}>
                    {user?.isActive ? 'Active' : 'Inactive'}
                  </Badge>
                </div>
                <div className="d-flex justify-content-between align-items-center mb-2">
                  <span>Role</span>
                  <span className="text-muted">{user?.role?.replace('_', ' ')}</span>
                </div>
                {user?.organization && (
                  <div className="d-flex justify-content-between align-items-center">
                    <span>Organization</span>
                    <span className="text-muted">{user.organization}</span>
                  </div>
                )}
              </Card.Body>
            </Card>
          </Col>

          <Col lg={8}>
            <Tab.Container activeKey={activeTab} onSelect={(k) => setActiveTab(k || 'profile')}>
              <Nav variant="tabs" className="mb-4">
                <Nav.Item>
                  <Nav.Link eventKey="profile">
                    <User className="me-2" size={16} />
                    Profile Information
                  </Nav.Link>
                </Nav.Item>
                <Nav.Item>
                  <Nav.Link eventKey="security">
                    <Shield className="me-2" size={16} />
                    Security
                  </Nav.Link>
                </Nav.Item>
                <Nav.Item>
                  <Nav.Link eventKey="preferences">
                    <Settings className="me-2" size={16} />
                    Preferences
                  </Nav.Link>
                </Nav.Item>
              </Nav>

              <Tab.Content>
                {/* Profile Information Tab */}
                <Tab.Pane eventKey="profile">
                  <Card>
                    <Card.Header>
                      <div className="d-flex justify-content-between align-items-center">
                        <h5 className="mb-0">Profile Information</h5>
                        {!isEditing && (
                          <Button 
                            variant="outline-primary" 
                            size="sm"
                            onClick={() => setIsEditing(true)}
                          >
                            Edit Profile
                          </Button>
                        )}
                      </div>
                    </Card.Header>
                    <Card.Body>
                      {saveMessage && (
                        <Alert variant="success" dismissible onClose={() => setSaveMessage('')}>
                          {saveMessage}
                        </Alert>
                      )}
                      {saveError && (
                        <Alert variant="danger" dismissible onClose={() => setSaveError('')}>
                          {saveError}
                        </Alert>
                      )}

                      <Form onSubmit={handleProfileSubmit}>
                        <Row>
                          <Col md={6}>
                            <Form.Group className="mb-3">
                              <Form.Label>First Name</Form.Label>
                              <Form.Control
                                type="text"
                                value={formData.firstName}
                                onChange={(e) => setFormData(prev => ({ ...prev, firstName: e.target.value }))}
                                disabled={!isEditing}
                                required
                              />
                            </Form.Group>
                          </Col>
                          <Col md={6}>
                            <Form.Group className="mb-3">
                              <Form.Label>Last Name</Form.Label>
                              <Form.Control
                                type="text"
                                value={formData.lastName}
                                onChange={(e) => setFormData(prev => ({ ...prev, lastName: e.target.value }))}
                                disabled={!isEditing}
                                required
                              />
                            </Form.Group>
                          </Col>
                        </Row>

                        <Form.Group className="mb-3">
                          <Form.Label>Email Address</Form.Label>
                          <div className="input-group">
                            <span className="input-group-text">
                              <Mail size={16} />
                            </span>
                            <Form.Control
                              type="email"
                              value={formData.email}
                              onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                              disabled={!isEditing}
                              required
                            />
                          </div>
                        </Form.Group>

                        <Form.Group className="mb-3">
                          <Form.Label>Phone Number</Form.Label>
                          <div className="input-group">
                            <span className="input-group-text">
                              <Phone size={16} />
                            </span>
                            <Form.Control
                              type="tel"
                              value={formData.phone}
                              onChange={(e) => setFormData(prev => ({ ...prev, phone: e.target.value }))}
                              disabled={!isEditing}
                              placeholder="Optional"
                            />
                          </div>
                        </Form.Group>

                        <Form.Group className="mb-3">
                          <Form.Label>Organization</Form.Label>
                          <div className="input-group">
                            <span className="input-group-text">
                              <Building size={16} />
                            </span>
                            <Form.Control
                              type="text"
                              value={formData.organization}
                              onChange={(e) => setFormData(prev => ({ ...prev, organization: e.target.value }))}
                              disabled={!isEditing}
                            />
                          </div>
                        </Form.Group>

                        <Form.Group className="mb-3">
                          <Form.Label>Department</Form.Label>
                          <Form.Select
                            value={formData.departmentId}
                            onChange={(e) => setFormData(prev => ({ ...prev, departmentId: e.target.value }))}
                            disabled={!isEditing || !permissions.canManageDepartments}
                          >
                            <option value="">Select Department</option>
                            {departments.map(dept => (
                              <option key={dept.id} value={dept.id}>
                                {dept.name}
                              </option>
                            ))}
                          </Form.Select>
                          {!permissions.canManageDepartments && (
                            <Form.Text className="text-muted">
                              Contact your administrator to change departments.
                            </Form.Text>
                          )}
                        </Form.Group>

                        <Form.Group className="mb-3">
                          <Form.Label>Location</Form.Label>
                          <div className="input-group">
                            <span className="input-group-text">
                              <MapPin size={16} />
                            </span>
                            <Form.Control
                              type="text"
                              value={formData.location}
                              onChange={(e) => setFormData(prev => ({ ...prev, location: e.target.value }))}
                              disabled={!isEditing}
                              placeholder="City, Country"
                            />
                          </div>
                        </Form.Group>

                        <Form.Group className="mb-3">
                          <Form.Label>Bio</Form.Label>
                          <Form.Control
                            as="textarea"
                            rows={3}
                            value={formData.bio}
                            onChange={(e) => setFormData(prev => ({ ...prev, bio: e.target.value }))}
                            disabled={!isEditing}
                            placeholder="Tell us about yourself..."
                          />
                        </Form.Group>

                        {isEditing && (
                          <div className="d-flex gap-2">
                            <Button 
                              type="submit" 
                              variant="primary"
                              disabled={isSaving}
                            >
                              {isSaving ? 'Saving...' : 'Save Changes'}
                            </Button>
                            <Button 
                              type="button" 
                              variant="secondary"
                              onClick={() => {
                                setIsEditing(false);
                                setSaveError('');
                                setSaveMessage('');
                              }}
                            >
                              Cancel
                            </Button>
                          </div>
                        )}
                      </Form>
                    </Card.Body>
                  </Card>
                </Tab.Pane>

                {/* Security Tab */}
                <Tab.Pane eventKey="security">
                  <Card>
                    <Card.Header>
                      <h5 className="mb-0">Change Password</h5>
                    </Card.Header>
                    <Card.Body>
                      {saveMessage && (
                        <Alert variant="success" dismissible onClose={() => setSaveMessage('')}>
                          {saveMessage}
                        </Alert>
                      )}
                      {saveError && (
                        <Alert variant="danger" dismissible onClose={() => setSaveError('')}>
                          {saveError}
                        </Alert>
                      )}

                      <Form onSubmit={handlePasswordSubmit}>
                        <Form.Group className="mb-3">
                          <Form.Label>Current Password</Form.Label>
                          <Form.Control
                            type="password"
                            value={passwordData.currentPassword}
                            onChange={(e) => setPasswordData(prev => ({ ...prev, currentPassword: e.target.value }))}
                            required
                          />
                        </Form.Group>

                        <Form.Group className="mb-3">
                          <Form.Label>New Password</Form.Label>
                          <Form.Control
                            type="password"
                            value={passwordData.newPassword}
                            onChange={(e) => setPasswordData(prev => ({ ...prev, newPassword: e.target.value }))}
                            minLength={8}
                            required
                          />
                          <Form.Text className="text-muted">
                            Password must be at least 8 characters long.
                          </Form.Text>
                        </Form.Group>

                        <Form.Group className="mb-3">
                          <Form.Label>Confirm New Password</Form.Label>
                          <Form.Control
                            type="password"
                            value={passwordData.confirmPassword}
                            onChange={(e) => setPasswordData(prev => ({ ...prev, confirmPassword: e.target.value }))}
                            required
                          />
                        </Form.Group>

                        <Button 
                          type="submit" 
                          variant="primary"
                          disabled={isSaving}
                        >
                          {isSaving ? 'Updating...' : 'Update Password'}
                        </Button>
                      </Form>
                    </Card.Body>
                  </Card>
                </Tab.Pane>

                {/* Preferences Tab */}
                <Tab.Pane eventKey="preferences">
                  <Card>
                    <Card.Header>
                      <h5 className="mb-0">Preferences</h5>
                    </Card.Header>
                    <Card.Body>
                      <Form.Group className="mb-3">
                        <Form.Label>Timezone</Form.Label>
                        <Form.Select
                          value={formData.timezone}
                          onChange={(e) => setFormData(prev => ({ ...prev, timezone: e.target.value }))}
                        >
                          <option value="America/New_York">Eastern Time (ET)</option>
                          <option value="America/Chicago">Central Time (CT)</option>
                          <option value="America/Denver">Mountain Time (MT)</option>
                          <option value="America/Los_Angeles">Pacific Time (PT)</option>
                          <option value="UTC">UTC</option>
                        </Form.Select>
                      </Form.Group>

                      <Form.Group className="mb-3">
                        <Form.Check
                          type="checkbox"
                          label="Email notifications for event updates"
                          defaultChecked
                        />
                      </Form.Group>

                      <Form.Group className="mb-3">
                        <Form.Check
                          type="checkbox"
                          label="Email notifications for deadline reminders"
                          defaultChecked
                        />
                      </Form.Group>

                      <Form.Group className="mb-3">
                        <Form.Check
                          type="checkbox"
                          label="Email notifications for event conflicts"
                          defaultChecked
                        />
                      </Form.Group>

                      <Button variant="primary">
                        Save Preferences
                      </Button>
                    </Card.Body>
                  </Card>
                </Tab.Pane>
              </Tab.Content>
            </Tab.Container>
          </Col>
        </Row>
      </Container>
    </Layout>
  );
};

export default Profile;
