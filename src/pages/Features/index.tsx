import React from 'react';
import { Contain<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, Card } from 'react-bootstrap';

const FeaturesPage: React.FC = () => {
    return (
        <Container className="my-5">
            <Row className="text-center mb-5">
                <Col>
                    <h1>Simplify Event Management</h1>
                    <p>
                        Manage all your events effortlessly with our comprehensive platform.
                        Plan, schedule, and track your events with just a few clicks.
                    </p>
                    <Button variant="primary" size="lg">Get Started for Free</Button>
                </Col>
            </Row>

            <Row className="text-center mb-4">
                <Col>
                    <h2>Features to Supercharge Your Events</h2>
                </Col>
            </Row>

            <Row className="mb-5">
                <Col md={4}>
                    <Card className="p-3 shadow-sm">
                        <Card.Body>
                            <Card.Title>All-in-One Calendar</Card.Title>
                            <Card.Text>
                                Integrate with Google Calendar and iCal to manage your schedule in one place.
                            </Card.Text>
                        </Card.Body>
                    </Card>
                </Col>
                <Col md={4}>
                    <Card className="p-3 shadow-sm">
                        <Card.Body>
                            <Card.Title>Custom Event Templates</Card.Title>
                            <Card.Text>
                                Create and reuse event templates to streamline your workflow.
                            </Card.Text>
                        </Card.Body>
                    </Card>
                </Col>
                <Col md={4}>
                    <Card className="p-3 shadow-sm">
                        <Card.Body>
                            <Card.Title>Real-Time Notifications</Card.Title>
                            <Card.Text>
                                Receive instant notifications for any updates or changes to your events.
                            </Card.Text>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>

            <Row className="text-center mb-4">
                <Col>
                    <h2>What Our Users Say</h2>
                </Col>
            </Row>

            <Row>
                <Col md={4}>
                    <Card className="p-3 shadow-sm">
                        <Card.Body>
                            <Card.Text>
                                "This platform has transformed how we manage our team meetings and company events."
                            </Card.Text>
                            <Card.Footer>- Alex J.</Card.Footer>
                        </Card.Body>
                    </Card>
                </Col>
                <Col md={4}>
                    <Card className="p-3 shadow-sm">
                        <Card.Body>
                            <Card.Text>
                                "I love how easy it is to keep track of everything in one place!"
                            </Card.Text>
                            <Card.Footer>- Maria L.</Card.Footer>
                        </Card.Body>
                    </Card>
                </Col>
                <Col md={4}>
                    <Card className="p-3 shadow-sm">
                        <Card.Body>
                            <Card.Text>
                                "Our productivity has increased ever since we started using this tool."
                            </Card.Text>
                            <Card.Footer>- Chris K.</Card.Footer>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>

            <Row className="text-center mt-5">
                <Col>
                    <h3>Ready to Get Started?</h3>
                    <p>Sign up now and take control of your event management process.</p>
                    <Button variant="primary" size="lg">Start Your Free Trial</Button>
                </Col>
            </Row>
        </Container>
    );
};

export default FeaturesPage;
