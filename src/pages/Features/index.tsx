import React from 'react';
import { Button } from '../../components/ui/button';
import { Card } from '../../components/ui/card';
import Layout from '../../components/Layout';

const FeaturesPage: React.FC = () => {
    return (
        <Layout>
            <div className="py-12">
                <div className="wk-container">
                    <div className="text-center mb-12">
                        <h1 className="text-4xl font-bold mb-4 text-brand-blue">Simplify Event Management</h1>
                        <p className="text-xl text-gray-600 mb-8">
                            Manage all your events effortlessly with our comprehensive platform.
                            Plan, schedule, and track your events with just a few clicks.
                        </p>
                        <Button variant="brand-orange" size="lg">Get Started for Free</Button>
                    </div>

                    <div className="text-center mb-8">
                        <h2 className="text-2xl font-bold text-brand-blue">Features to Supercharge Your Events</h2>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
                        <Card>
                            <div className="p-6">
                                <h3 className="text-xl font-bold mb-3 text-brand-blue">All-in-One Calendar</h3>
                                <p className="text-gray-600">
                                    Integrate with Google Calendar and iCal to manage your schedule in one place.
                                </p>
                            </div>
                        </Card>
                        <Card>
                            <div className="p-6">
                                <h3 className="text-xl font-bold mb-3 text-brand-blue">Custom Event Templates</h3>
                                <p className="text-gray-600">
                                    Create and reuse event templates to streamline your workflow.
                                </p>
                            </div>
                        </Card>
                        <Card>
                            <div className="p-6">
                                <h3 className="text-xl font-bold mb-3 text-brand-blue">Real-Time Notifications</h3>
                                <p className="text-gray-600">
                                    Receive instant notifications for any updates or changes to your events.
                                </p>
                            </div>
                        </Card>
                    </div>

                    <div className="text-center mb-8">
                        <h2 className="text-2xl font-bold text-brand-blue">What Our Users Say</h2>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
                        <Card>
                            <div className="p-6">
                                <p className="text-gray-600 mb-4">
                                    "This platform has transformed how we manage our team meetings and company events."
                                </p>
                                <p className="text-sm font-semibold text-brand-blue">- Alex J.</p>
                            </div>
                        </Card>
                        <Card>
                            <div className="p-6">
                                <p className="text-gray-600 mb-4">
                                    "I love how easy it is to keep track of everything in one place!"
                                </p>
                                <p className="text-sm font-semibold text-brand-blue">- Maria L.</p>
                            </div>
                        </Card>
                        <Card>
                            <div className="p-6">
                                <p className="text-gray-600 mb-4">
                                    "Our productivity has increased ever since we started using this tool."
                                </p>
                                <p className="text-sm font-semibold text-brand-blue">- Chris K.</p>
                            </div>
                        </Card>
                    </div>

                    <div className="text-center">
                        <div className="wk-card-accent p-8 rounded-lg">
                            <h3 className="text-2xl font-bold mb-4 text-brand-blue">Ready to Get Started?</h3>
                            <p className="text-gray-600 mb-6">Sign up now and take control of your event management process.</p>
                            <Button variant="brand-orange" size="lg">Start Your Free Trial</Button>
                        </div>
                    </div>
                </div>
            </div>
        </Layout>
    );
};

export default FeaturesPage;
