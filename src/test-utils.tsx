import React from 'react';
import { render, RenderOptions } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { BrowserRouter } from 'react-router-dom';
import { AuthProvider } from './contexts/AuthContext';

// Create a test query client
const createTestQueryClient = () => {
  return new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
        cacheTime: 0,
        staleTime: 0,
      },
      mutations: {
        retry: false,
      },
    },
  });
};

// Custom render function that includes providers
interface CustomRenderOptions extends Omit<RenderOptions, 'wrapper'> {
  queryClient?: QueryClient;
  initialEntries?: string[];
}

export const renderWithProviders = (
  ui: React.ReactElement,
  {
    queryClient = createTestQueryClient(),
    initialEntries = ['/'],
    ...renderOptions
  }: CustomRenderOptions = {}
) => {
  const AllTheProviders: React.FC<{ children: React.ReactNode }> = ({ children }) => {
    return (
      <QueryClientProvider client={queryClient}>
        <BrowserRouter>
          <AuthProvider>
            {children}
          </AuthProvider>
        </BrowserRouter>
      </QueryClientProvider>
    );
  };

  return render(ui, { wrapper: AllTheProviders, ...renderOptions });
};

// Re-export everything from testing-library
export * from '@testing-library/react';

// Mock implementations for common external dependencies
export const mockWindowOpen = jest.fn();
export const mockWindowAlert = jest.fn();
export const mockWindowConfirm = jest.fn();

// Setup global mocks
beforeEach(() => {
  // Mock window methods
  Object.defineProperty(window, 'open', {
    value: mockWindowOpen,
    writable: true,
  });
  
  Object.defineProperty(window, 'alert', {
    value: mockWindowAlert,
    writable: true,
  });
  
  Object.defineProperty(window, 'confirm', {
    value: mockWindowConfirm,
    writable: true,
  });

  // Mock localStorage
  const localStorageMock = {
    getItem: jest.fn(),
    setItem: jest.fn(),
    removeItem: jest.fn(),
    clear: jest.fn(),
  };
  
  Object.defineProperty(window, 'localStorage', {
    value: localStorageMock,
    writable: true,
  });

  // Mock console methods to reduce noise in tests
  jest.spyOn(console, 'error').mockImplementation(() => {});
  jest.spyOn(console, 'warn').mockImplementation(() => {});
});

afterEach(() => {
  // Clear all mocks after each test
  jest.clearAllMocks();
  
  // Restore console methods
  jest.restoreAllMocks();
});

// Mock data for tests
export const mockUser = {
  id: 'test-user-1',
  firstName: 'Test',
  lastName: 'User',
  email: '<EMAIL>',
  role: 'employee' as const,
  departmentId: 'test-dept',
  isActive: true,
};

export const mockEvent = {
  id: 'test-event-1',
  eventName: 'Test Event',
  description: 'Test Description',
  date: '2024-01-01',
  time: '10:00',
  venue: 'Test Venue',
  organizer: 'Test Organizer',
  organizerId: 'test-user-1',
  departmentId: 'test-dept',
  departmentName: 'Test Department',
  departments: ['test-dept'],
  duration: 60,
  status: 'draft' as const,
  priority: 'medium' as const,
  createdAt: '2024-01-01T00:00:00Z',
  updatedAt: '2024-01-01T00:00:00Z',
};

// Helper functions for common test scenarios
export const waitForLoadingToFinish = () => {
  return new Promise(resolve => setTimeout(resolve, 0));
};

export const createMockQueryClient = () => {
  return new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
        cacheTime: 0,
        staleTime: 0,
      },
      mutations: {
        retry: false,
      },
    },
  });
};

// Mock implementations for external libraries
export const mockReactBigCalendar = {
  Calendar: ({ children, ...props }: any) => (
    <div data-testid="calendar" {...props}>
      {children}
    </div>
  ),
  momentLocalizer: () => ({}),
};

export const mockReactSelect = {
  default: ({ children, ...props }: any) => (
    <select data-testid="react-select" {...props}>
      {children}
    </select>
  ),
};

// Custom matchers for better test assertions
expect.extend({
  toBeInTheDocument(received) {
    const pass = received !== null && received !== undefined;
    return {
      message: () => `expected element ${pass ? 'not ' : ''}to be in the document`,
      pass,
    };
  },
  
  toHaveClass(received, className) {
    const pass = received && received.classList && received.classList.contains(className);
    return {
      message: () => `expected element ${pass ? 'not ' : ''}to have class "${className}"`,
      pass,
    };
  },
});

// Global test configuration
export const testConfig = {
  timeout: 5000,
  retries: 2,
  verbose: true,
};

// Mock API responses
export const mockApiResponses = {
  events: [mockEvent],
  users: [mockUser],
  departments: [
    {
      id: 'test-dept',
      name: 'Test Department',
      description: 'Test Department Description',
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z',
    },
  ],
};

// Error boundary for testing error states
export class TestErrorBoundary extends React.Component<
  { children: React.ReactNode },
  { hasError: boolean }
> {
  constructor(props: { children: React.ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError() {
    return { hasError: true };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Test Error Boundary caught an error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return <div data-testid="error-boundary">Something went wrong.</div>;
    }

    return this.props.children;
  }
}

// Performance testing utilities
export const measureRenderTime = (renderFn: () => void) => {
  const start = performance.now();
  renderFn();
  const end = performance.now();
  return end - start;
};

// Accessibility testing helpers
export const checkAccessibility = async (container: HTMLElement) => {
  // Basic accessibility checks
  const buttons = container.querySelectorAll('button');
  const inputs = container.querySelectorAll('input');
  const images = container.querySelectorAll('img');
  
  const issues: string[] = [];
  
  // Check buttons have accessible names
  buttons.forEach((button, index) => {
    if (!button.textContent && !button.getAttribute('aria-label')) {
      issues.push(`Button ${index} lacks accessible name`);
    }
  });
  
  // Check inputs have labels
  inputs.forEach((input, index) => {
    const id = input.getAttribute('id');
    const hasLabel = id && container.querySelector(`label[for="${id}"]`);
    const hasAriaLabel = input.getAttribute('aria-label');
    
    if (!hasLabel && !hasAriaLabel) {
      issues.push(`Input ${index} lacks label`);
    }
  });
  
  // Check images have alt text
  images.forEach((img, index) => {
    if (!img.getAttribute('alt')) {
      issues.push(`Image ${index} lacks alt text`);
    }
  });
  
  return issues;
};
