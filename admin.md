# Eva Planning - Admin Documentation

## System Overview

Eva Planning is a collaborative calendar planning platform designed for companies with multiple departments. The system enables department teams to submit yearly events with pending/confirmed states, while administrators manage overlapping conflicts and approve submissions.

## Sample Users

### 1. Administrator User
**Login Credentials:**
- **Email:** <EMAIL>
- **Password:** admin123
- **Role:** Administrator
- **Department:** Administration (Primary)
- **Additional Departments:** All departments (oversight access)

**Permissions:**
- ✅ Create, edit, and delete all events
- ✅ Approve/reject pending events
- ✅ Manage submission deadlines
- ✅ View all company events and analytics
- ✅ Manage departments and users
- ✅ Export calendar data
- ✅ Resolve event conflicts
- ✅ Access admin dashboard

**Profile Details:**
- **Name:** Admin User
- **Organization:** Demo Company Inc.
- **Phone:** +****************
- **Location:** New York, NY
- **Bio:** System administrator responsible for overall event management and conflict resolution.

### 2. Team Leader User
**Login Credentials:**
- **Email:** <EMAIL>
- **Password:** teamlead123
- **Role:** Team Leader
- **Department:** Marketing (Primary)
- **Additional Departments:** Sales (collaboration access)

**Permissions:**
- ✅ Create and edit events for their department
- ✅ Submit events for admin approval
- ✅ View department analytics
- ✅ Export department calendar
- ❌ Approve/reject events (admin only)
- ❌ Manage other departments
- ❌ Set submission deadlines

**Profile Details:**
- **Name:** Marketing Lead
- **Organization:** Demo Company Inc.
- **Phone:** +****************
- **Location:** Los Angeles, CA
- **Bio:** Marketing team leader responsible for coordinating marketing events and campaigns.

## Department Structure

### Available Departments
1. **Administration** - Executive management and administrative functions
2. **Human Resources** - Employee relations, recruitment, and HR policies
3. **Information Technology** - IT infrastructure, software development, and technical support
4. **Marketing** - Brand management, advertising, and market research
5. **Sales** - Customer acquisition, account management, and revenue generation
6. **Finance** - Financial planning, accounting, and budget management
7. **Operations** - Business operations, logistics, and process management

### Multi-Department Support
- Users can belong to multiple departments
- One department is designated as "primary"
- Primary department determines default event assignments
- Additional departments provide collaboration access

## Event Management System

### Event States
1. **Draft** - Initial creation, editable by creator
2. **Pending** - Submitted for admin review
3. **Confirmed** - Approved by administrator
4. **Rejected** - Denied by administrator with comments
5. **Cancelled** - Cancelled after confirmation

### Event Priorities
1. **Low** - Regular departmental activities
2. **Medium** - Standard business events
3. **High** - Important company meetings
4. **Critical** - Executive meetings, board meetings

### Sample Events Data

#### Confirmed Events
1. **Q1 Marketing Campaign Launch**
   - Date: 2024-03-15
   - Time: 10:00 AM
   - Duration: 120 minutes
   - Department: Marketing
   - Priority: High
   - Status: Confirmed

2. **IT Security Training**
   - Date: 2024-03-20
   - Time: 2:00 PM
   - Duration: 90 minutes
   - Department: IT
   - Priority: Medium
   - Status: Confirmed

3. **Monthly Sales Review**
   - Date: 2024-03-25
   - Time: 9:00 AM
   - Duration: 60 minutes
   - Department: Sales
   - Priority: Medium
   - Status: Confirmed

#### Pending Events
1. **Annual Company Retreat Planning**
   - Date: 2024-04-10
   - Time: 1:00 PM
   - Duration: 180 minutes
   - Department: HR
   - Priority: High
   - Status: Pending

2. **Budget Review Meeting**
   - Date: 2024-04-15
   - Time: 11:00 AM
   - Duration: 90 minutes
   - Department: Finance
   - Priority: Critical
   - Status: Pending

## System Configuration

### Color Scheme (From Homepage/Landing)
- **Primary Blue:** #3174ad
- **Secondary Yellow:** #FFF100
- **Success Green:** #198754
- **Warning Orange:** #ffc107
- **Danger Red:** #dc3545
- **Info Blue:** #0dcaf0
- **Dark Gray:** #212529
- **Light Gray:** #f8f9fa

### Navigation Structure
- **Overview** - Department dashboard and analytics
- **Event Management** - All company events (admin view)
- **Department View** - User's department events only
- **Admin Panel** - Administrative functions (admin only)
- **Deadlines** - Submission deadline management (admin only)

### Calendar Features
- **Multiple Views:** Month, Week, Day, Agenda
- **Interactive Creation:** Click dates to create events
- **Color Coding:** Events colored by status and priority
- **Conflict Detection:** Visual indicators for overlapping events
- **Export Options:** iCal, Google Calendar, Outlook, CSV, JSON

### Permissions Matrix

| Feature | Admin | Team Leader | Employee |
|---------|-------|-------------|----------|
| Create Events | ✅ All | ✅ Department | ❌ |
| Edit Events | ✅ All | ✅ Own/Dept | ❌ |
| Delete Events | ✅ All | ✅ Own (Draft) | ❌ |
| Approve Events | ✅ | ❌ | ❌ |
| View All Events | ✅ | ❌ | ❌ |
| Manage Deadlines | ✅ | ❌ | ❌ |
| Export Calendar | ✅ | ✅ | ✅ |
| View Analytics | ✅ All | ✅ Department | ❌ |

## Getting Started

### For Administrators
1. Login with admin credentials
2. Access Admin Panel to review pending events
3. Set submission deadlines for upcoming periods
4. Monitor conflicts and resolve scheduling issues
5. Approve/reject events with appropriate comments

### For Team Leaders
1. Login with team leader credentials
2. Navigate to Event Management to create department events
3. Submit events for admin approval before deadlines
4. Monitor department analytics and upcoming events
5. Export department calendar for team distribution

### For Employees
1. Login with employee credentials
2. View confirmed company events in Department View
3. Export calendar to personal calendar applications
4. Stay informed about department and company activities

## Support and Maintenance

### Default Data Reset
The system includes comprehensive sample data that resets on application restart, including:
- Sample users with proper roles and departments
- Sample events across all departments
- Default department structure
- Sample submission deadlines

### Troubleshooting
- **Login Issues:** Use provided sample credentials
- **Permission Errors:** Check user role and department assignments
- **Calendar Not Loading:** Refresh page or check browser console
- **Export Problems:** Ensure popup blockers are disabled

### Technical Notes
- Built with React, TypeScript, and Bootstrap
- Uses React Big Calendar for calendar interface
- Implements role-based access control
- Supports real-time conflict detection
- Mobile-responsive design throughout
