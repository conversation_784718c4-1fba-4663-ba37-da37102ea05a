# EVA Planning Application

A modern event planning application built with React, TypeScript, and React Query.

## Features

- 📅 Interactive calendar view using react-big-calendar
- ⚡ Optimistic UI updates for a snappy user experience
- 🔄 Real-time data synchronization with React Query
- 🎨 Responsive design with React Bootstrap
- 🔒 Type-safe code with TypeScript
- 🧪 Comprehensive test coverage

## Tech Stack

- React 18
- TypeScript
- React Query (TanStack Query)
- React Bootstrap
- React Big Calendar
- Axios
- Vite (for development)

## Getting Started

### Prerequisites

- Node.js 16+ and npm 8+

### Installation

1. Clone the repository

   ```bash
   git clone https://github.com/your-username/eva-planning.git
   cd eva-planning
   ```

2. Install dependencies

   ```bash
   npm install
   ```

3. Start the development server

   ```bash
   npm run dev
   ```

4. Open [http://localhost:3000](http://localhost:3000) to view it in your browser.

## Available Scripts

In the project directory, you can run:

### `npm start`

Runs the app in the development mode.\
Open [http://localhost:3000](http://localhost:3000) to view it in the browser.

The page will reload if you make edits.\
You will also see any lint errors in the console.

### `npm test`

Launches the test runner in the interactive watch mode.\
See the section about [running tests](https://facebook.github.io/create-react-app/docs/running-tests) for more information.

### `npm run build`

Builds the app for production to the `build` folder.\
It correctly bundles React in production mode and optimizes the build for the best performance.

The build is minified and the filenames include the hashes.\
Your app is ready to be deployed!

See the section about [deployment](https://facebook.github.io/create-react-app/docs/deployment) for more information.

### `npm run eject`

**Note: this is a one-way operation. Once you `eject`, you can’t go back!**

If you aren’t satisfied with the build tool and configuration choices, you can `eject` at any time. This command will remove the single build dependency from your project.

Instead, it will copy all the configuration files and the transitive dependencies (webpack, Babel, ESLint, etc) right into your project so you have full control over them. All of the commands except `eject` will still work, but they will point to the copied scripts so you can tweak them. At this point you’re on your own.

You don’t have to ever use `eject`. The curated feature set is suitable for small and middle deployments, and you shouldn’t feel obligated to use this feature. However we understand that this tool wouldn’t be useful if you couldn’t customize it when you are ready for it.

## React Query Integration

This application uses [React Query](https://tanstack.com/query/latest) for server state management. The main data fetching logic is encapsulated in the `useEvents` hook.

### Key Features

- **Optimistic Updates**: UI updates immediately while requests are processed in the background
- **Automatic Retries**: Failed requests are automatically retried
- **Window Focus Refetching**: Data is automatically refreshed when the window regains focus
- **Caching**: Query results are cached to minimize network requests

### Example Usage

```typescript
const { events, isLoading, error, createEvent, updateEvent, deleteEvent } = useEvents();

// Create a new event
const handleCreate = async (newEvent) => {
  try {
    await createEvent.mutateAsync(newEvent);
    // UI updates automatically via React Query
  } catch (error) {
    // Handle error (UI rolls back automatically)
  }
};
```

## Testing

Run the test suite:

```bash
npm test
```

## Building for Production

```bash
npm run build
```

## Learn More

- [React Query Documentation](https://tanstack.com/query/latest)
- [React Documentation](https://reactjs.org/)
- [TypeScript Documentation](https://www.typescriptlang.org/)
- [React Bootstrap Documentation](https://react-bootstrap.github.io/)

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.
